package orders

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	middlewareCfg "git.ninjavan.co/3pl/configs/middleware"
	"git.ninjavan.co/3pl/handlers/order_creation/mocks"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
)

func TestLazadaCCController_CreateLazadaCCOrder(t *testing.T) {
	t.<PERSON>llel()
	defaultReq := order.LazadaCreateParcelReq{
		Receiver: order.LazadaCCReceiver{
			ID:      "RCV123",
			Name:    "John Doe",
			Phone:   "1234567890",
			Mobile:  "0987654321",
			Email:   lo.ToPtr("<EMAIL>"),
			ZipCode: lo.ToPtr("123456"),
			Address: order.LazadaCCReceiverAddress{
				Country:       "Thailand",
				Province:      "Bangkok",
				City:          "Bangkok",
				District:      lo.ToPtr("Dusit"),
				Street:        lo.ToPtr("Main Street"),
				DetailAddress: "123 Main St",
			},
		},
		PreResInfo: order.LazadaCCPreResInfo{
			Address: order.LazadaCCPreResInfoAddress{
				Country: "China",
			},
		},
		Parcel: order.LazadaCCParcel{
			Weight:        lo.ToPtr(1.5),
			WeightUnit:    "kg",
			Price:         lo.ToPtr(100.0),
			PriceUnit:     lo.ToPtr("CENT"),
			Length:        lo.ToPtr(20.0),
			Width:         lo.ToPtr(15.0),
			Height:        lo.ToPtr(10.0),
			DimensionUnit: lo.ToPtr("cm"),
			GoodsQuantity: 2,
			GoodsList: []*order.LazadaCCGoods{
				{
					Name:          "Test Product",
					CnName:        lo.ToPtr("测试产品"),
					Quantity:      1,
					Price:         lo.ToPtr(50.0),
					PriceCurrency: lo.ToPtr("MYR"),
					PriceUnit:     "CENT",
					SkuCode:       lo.ToPtr("SKU123"),
					HSCode:        lo.ToPtr("123456"),
				},
			},
		},
		TradeInfo: struct {
			TradeOrderId string `json:"tradeOrderId" binding:"max=512"`
		}{
			TradeOrderId: "TO123456",
		},
		IdempotentId:   "IDMP123456",
		OrderCode:      "ORD123456",
		TrackingNumber: "TRK123456",
		ToLocation:     "CRK",
	}

	tests := []struct {
		name             string
		setupRequest     func() (*http.Request, error)
		setupContext     func(*gin.Context)
		setupMock        func(*mocks.MockLazadaCCOrderCreator)
		expectedStatus   int
		expectedResponse interface{}
	}{
		{
			name: "invalid request body",
			setupRequest: func() (*http.Request, error) {
				return http.NewRequest(http.MethodPost, "/", bytes.NewBufferString("invalid json"))
			},
			setupContext: func(c *gin.Context) {
				c.Request.Header.Set("Content-Type", "application/json")
			},
			expectedStatus: http.StatusBadRequest,
			expectedResponse: order.NewLazadaCCErrorResponse(
				order.LazadaErrorCodeInvalidRequest,
				"invalid character 'i' looking for beginning of value",
			),
		},
		{
			name: "successful order creation",
			setupRequest: func() (*http.Request, error) {
				jsonBody, err := json.Marshal(defaultReq)
				if err != nil {
					return nil, err
				}
				req, err := http.NewRequest(http.MethodPost, "/", bytes.NewBuffer(jsonBody))
				if err != nil {
					return nil, err
				}
				req.Header.Set("Content-Type", "application/json")
				return req, nil
			},
			setupContext: func(c *gin.Context) {
				c.Set(middlewareCfg.PartnerKey, &models.Partner{ID: 123})
			},
			setupMock: func(m *mocks.MockLazadaCCOrderCreator) {
				m.EXPECT().
					CreateMMCCOrder(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(&models.Parcel{ID: 1}, nil)
			},
			expectedStatus:   http.StatusOK,
			expectedResponse: order.NewLazadaCCSuccessResp(),
		},
		{
			name: "order creation failure",
			setupRequest: func() (*http.Request, error) {
				jsonBody, err := json.Marshal(defaultReq)
				if err != nil {
					return nil, err
				}
				req, err := http.NewRequest(http.MethodPost, "/", bytes.NewBuffer(jsonBody))
				if err != nil {
					return nil, err
				}
				req.Header.Set("Content-Type", "application/json")
				return req, nil
			},
			setupContext: func(c *gin.Context) {
				c.Set(middlewareCfg.PartnerKey, &models.Partner{ID: 123})
			},
			setupMock: func(m *mocks.MockLazadaCCOrderCreator) {
				m.EXPECT().
					CreateMMCCOrder(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil, errors.New("internal error"))
			},
			expectedStatus: http.StatusInternalServerError,
			expectedResponse: order.NewLazadaCCErrorResponse(
				order.LazadaErrorCodeInternal,
				"internal error when creating order",
			),
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockCreator := mocks.NewMockLazadaCCOrderCreator(ctrl)
			if tt.setupMock != nil {
				tt.setupMock(mockCreator)
			}

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			req, err := tt.setupRequest()
			require.NoError(t, err)
			c.Request = req

			if tt.setupContext != nil {
				tt.setupContext(c)
			}

			controller := &lazadaCCController{
				orderCreator: mockCreator,
			}

			controller.createLazadaCCOrder(c)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var actualResponse map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &actualResponse)
			require.NoError(t, err)

			expectedJSON, err := json.Marshal(tt.expectedResponse)
			require.NoError(t, err)
			var expectedMap map[string]interface{}
			err = json.Unmarshal(expectedJSON, &expectedMap)
			require.NoError(t, err)

			assert.Equal(t, expectedMap, actualResponse)
		})
	}
}
