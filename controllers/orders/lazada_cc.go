package orders

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"

	"bitbucket.ninjavan.co/cg/datadog-agent---go/nvtracer"

	middlewareCfg "git.ninjavan.co/3pl/configs/middleware"
	"git.ninjavan.co/3pl/configs/parcel"
	"git.ninjavan.co/3pl/errors/fplerror"
	"git.ninjavan.co/3pl/handlers/auth"
	"git.ninjavan.co/3pl/handlers/order_creation"
	"git.ninjavan.co/3pl/httpmodels/order"
	tplMiddleware "git.ninjavan.co/3pl/middleware"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/utils"
	"git.ninjavan.co/3pl/utils/loggerutils"
)

type lazadaCCController struct {
	orderCreator order_creation.LazadaCCOrderCreator
}

func LazadaCCRouteGroup(rg *gin.RouterGroup) {
	ctrl := &lazadaCCController{
		orderCreator: order_creation.NewLazadaCCOrderCreator(),
	}

	rg.Group(
		"/lazada/cc",
		auth.LazadaCCAuthHandler(),
		tplMiddleware.SaveLazadaCCOrderRequest(),
	).POST(
		"/parcel-create",
		ctrl.createLazadaCCOrder,
	)
}

const lazadaCCModuleName = "lazada-cc-order-controller"

// @Summary		Create Lazada CC order
// @Description	Create a new cross-border order for Lazada
// @Tags			Lazada CC
// @Accept			json
// @Produce		json
// @Param			request	body		order.LazadaCreateParcelReq	true	"Lazada CC order creation request"
// @Success		200		{object}	order.LazadaCCOCResponse
// @Failure		400		{object}	order.LazadaCCOCResponse
// @Failure		500		{object}	order.LazadaCCOCResponse
// @Router			/v1/lazada/cc/parcel-create [post]
func (ctrl *lazadaCCController) createLazadaCCOrder(c *gin.Context) {
	ctx, span := nvtracer.CreateSpanFromContext(c.Request.Context())
	defer span.Finish()

	logger := loggerutils.Ctx(c.Request.Context(), lazadaCCModuleName)
	r := order.LazadaCreateParcelReq{}
	if err := c.ShouldBindBodyWith(&r, binding.JSON); err != nil {
		logger.Err(err).Msg("failed-to-parse-request")
		c.JSON(http.StatusBadRequest, order.NewLazadaCCErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	v, _ := c.Get(middlewareCfg.PartnerKey)
	partner := v.(*models.Partner)
	req := convertLazadaParcelToOCReq(r)
	sc, ok := scs[strings.ToUpper(r.ToLocation)]
	if !ok {
		err := fplerror.ErrBadRequest.Newf("service not configured for to location: %s", r.ToLocation)
		logger.Err(err).Msg("failed-to-convert-request")
		c.JSON(http.StatusBadRequest, order.NewLazadaCCErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}
	req.ServiceCode = sc
	parcel, err := ctrl.orderCreator.CreateMMCCOrder(ctx, partner, req)
	tplMiddleware.SetParcelContext(c, parcel)
	if err != nil {
		logger.Err(err).Msg("error-creating-lazada-cc-order")
		switch {
		case fplerror.ErrBadRequest.Equal(err):
			c.JSON(http.StatusBadRequest, order.NewLazadaCCErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		default:
			c.JSON(http.StatusInternalServerError, order.NewLazadaCCErrorResponse(order.LazadaErrorCodeInternal, "internal error when creating order"))
		}
		return
	}
	c.JSON(http.StatusOK, order.NewLazadaCCSuccessResp())
}

var scs = map[string]string{
	"CRK": "PH-CRK-CC-1",
	"MNL": "PH-MNL-CC-1 ",
}

func convertLazadaParcelToOCReq(req order.LazadaCreateParcelReq) *order.BaseRequest {
	tcc := utils.GetCountryInfoFromName(req.Receiver.Address.Country).Alpha2
	if tcc == "" {
		tcc = req.Receiver.Address.Country
	}
	fcc := utils.GetCountryInfoFromName(req.PreResInfo.Address.Country).Alpha2
	if fcc == "" {
		fcc = req.PreResInfo.Address.Country
	}

	w := req.Parcel.Weight
	wu := req.Parcel.WeightUnit
	if w != nil && (wu == "" || strings.EqualFold(wu, "g")) {
		*w = *w / 1000
		wu = "kg"
	}

	parcelPrice := req.Parcel.Price
	if req.Parcel.Price != nil && strings.ToUpper(null.StringFromPtr(req.Parcel.PriceUnit).String) == "CENT" {
		*parcelPrice = *req.Parcel.Price / 100
	}

	return &order.BaseRequest{
		RequestId:        req.IdempotentId,
		Source:           order.SourceCustomAPI,
		SourceOrderID:    lo.ToPtr(req.TrackingNumber),
		RefTrackingID:    lo.ToPtr(req.OrderCode),
		PartnerUniqueKey: lo.ToPtr(req.OrderCode + "_" + req.TrackingNumber),
		To: order.Address{
			Name:         req.Receiver.Name,
			AddressLine1: req.Receiver.Address.DetailAddress,
			AddressLine2: req.Receiver.Address.Street,
			AddressLine3: req.Receiver.Address.District,

			PostCode:      req.Receiver.ZipCode,
			City:          lo.ToPtr(req.Receiver.Address.City),
			StateProvince: lo.ToPtr(req.Receiver.Address.Province),
			CountryCode:   tcc,
			ContactNumber: lo.ToPtr(req.Receiver.Phone),
			ContactEmail:  req.Receiver.Email,
		},
		From: order.Address{
			Name:         req.PreResInfo.ResName,
			AddressLine1: req.PreResInfo.Address.DetailAddress,
			AddressLine2: req.PreResInfo.Address.Street,
			AddressLine3: req.PreResInfo.Address.District,

			City:          req.PreResInfo.Address.City,
			StateProvince: req.PreResInfo.Address.Province,
			CountryCode:   fcc,
			ContactNumber: req.PreResInfo.Phone,
		},
		ParcelDetails: &models.ParcelDetails{
			ShipperSubmittedWeight:     w,
			Weight:                     w,
			ShipperSubmittedWeightUnit: lo.ToPtr(wu),
			Value:                      parcelPrice,
			CustomsCurrency:            req.Parcel.PriceUnit,
			ShipperSubmittedDimensions: &models.Dimensions{
				Length: req.Parcel.Length,
				Width:  req.Parcel.Width,
				Height: req.Parcel.Height,
				Unit:   req.Parcel.DimensionUnit,
			},
			Quantity: lo.ToPtr(req.Parcel.GoodsQuantity),
		},
		Items: convertLZDCCParcelItems(req),
	}
}

func convertLZDCCParcelItems(req order.LazadaCreateParcelReq) []*order.ParcelItem {
	var items []*order.ParcelItem
	for _, item := range req.Parcel.GoodsList {
		hsCode := utils.ParseUint(null.StringFromPtr(item.HSCode))
		itemPrice := item.Price
		if item.Price != nil && strings.ToUpper(item.PriceUnit) == "CENT" {
			*itemPrice = *item.Price / 100
		}
		taxes := make(order.TaxesMap)
		if strings.EqualFold(item.Extension.LvgPaid, "y") || strings.EqualFold(item.Extension.LvgPaid, "true") {
			taxes[parcel.LVGTaxName] = order.TaxInfo{
				Number:     null.StringFrom(req.Features["taxId"]).Ptr(),
				IsIncluded: utils.ParseBool(null.StringFrom(item.Extension.LvgPaid)).Ptr(),
			}
		}

		items = append(items, &order.ParcelItem{
			Description:       null.StringFrom(item.Name).Ptr(),
			NativeDescription: item.CnName,
			Quantity:          lo.ToPtr(item.Quantity),
			UnitValue:         itemPrice,
			HSCode:            hsCode.Ptr(),
			SKUId:             item.SkuCode,
			GoodsCurrency:     item.PriceCurrency,
			Taxes:             taxes,
		})
	}
	return items
}
