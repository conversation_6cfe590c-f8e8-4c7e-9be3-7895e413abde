package orders

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"

	middlewareCfg "git.ninjavan.co/3pl/configs/middleware"
	"git.ninjavan.co/3pl/errors/fplerror"
	"git.ninjavan.co/3pl/handlers/order_creation/mocks"
	mocks1 "git.ninjavan.co/3pl/handlers/order_creation/mocks"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/utils"
)

func Test_lazadaMMController_createMMCCBag(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name             string
		setupRequest     func() *http.Request
		setupMock        func(mockOrderCreator *mocks.MockLazadaMMOrderCreator)
		expectedStatus   int
		expectedResponse interface{}
	}{
		{
			name: "success",
			setupRequest: func() *http.Request {
				bagRequest := order.LazadaMMCreateBagRequest{
					BigBagID:         "bag123",
					SortCode:         "sort123",
					LaneCode:         "SZXABKI-FLASH-STD-02",
					PreCPResCode:     "pre123",
					CurrentCPResCode: "curr123",
					GrossWeight:      "1000",
					NetWeight:        "900",
					LabelWeight:      "50",
					PackingWeight:    "50",
					ParcelQty:        "1",
					ParcelList: []*order.LazadaMMParcelID{
						{
							LogisticsOrderCode: "log123",
							TrackingNumber:     "track123",
						},
					},
				}
				formValues := url.Values{}
				formValues.Add("logistics_interface", string(utils.JsonMarshalIgnoreError(bagRequest)))
				req, _ := http.NewRequest(http.MethodPost, "/", strings.NewReader(formValues.Encode()))
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
				return req
			},
			setupMock: func(mockOrderCreator *mocks.MockLazadaMMOrderCreator) {
				mockOrderCreator.EXPECT().
					CreateMMCCBag(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(&models.Parcel{}, nil)
			},
			expectedStatus: http.StatusOK,
			expectedResponse: order.LazadaBaseResponse{
				Success: true,
			},
		},
		{
			name: "invalid_logistics_interface",
			setupRequest: func() *http.Request {
				formValues := url.Values{}
				formValues.Add("logistics_interface", "invalid json")
				req, _ := http.NewRequest(http.MethodPost, "/", strings.NewReader(formValues.Encode()))
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
				return req
			},
			setupMock:      func(mockOrderCreator *mocks.MockLazadaMMOrderCreator) {},
			expectedStatus: http.StatusBadRequest,
			expectedResponse: order.LazadaBaseResponse{
				ErrorCode: order.LazadaErrorCodeInvalidRequest,
				Message:   "invalid character 'i' looking for beginning of value",
			},
		},
		{
			name: "failed_to_create_bag",
			setupRequest: func() *http.Request {
				bagRequest := order.LazadaMMCreateBagRequest{
					BigBagID:         "bag123",
					SortCode:         "sort123",
					LaneCode:         "SZXABKI-FLASH-STD-02",
					PreCPResCode:     "pre123",
					CurrentCPResCode: "curr123",
					GrossWeight:      "1000",
					NetWeight:        "900",
					LabelWeight:      "50",
					PackingWeight:    "50",
					ParcelQty:        "1",
					ParcelList: []*order.LazadaMMParcelID{
						{
							LogisticsOrderCode: "log123",
							TrackingNumber:     "track123",
						},
					},
				}
				formValues := url.Values{}
				formValues.Add("logistics_interface", string(utils.JsonMarshalIgnoreError(bagRequest)))
				req, _ := http.NewRequest(http.MethodPost, "/", strings.NewReader(formValues.Encode()))
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
				return req
			},
			setupMock: func(mockOrderCreator *mocks.MockLazadaMMOrderCreator) {
				mockOrderCreator.EXPECT().
					CreateMMCCBag(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil, errors.New("an internal error occurred"))
			},
			expectedStatus: http.StatusInternalServerError,
			expectedResponse: order.LazadaBaseResponse{
				Success:   false,
				ErrorCode: order.LazadaErrorCodeInternal,
				Message:   "[create bag] an internal error occurred. big-bag-id: bag123",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			gin.SetMode(gin.TestMode)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			c.Request = tt.setupRequest()
			c.Set(middlewareCfg.PartnerKey, &models.Partner{})

			mockOrderCreator := mocks.NewMockLazadaMMOrderCreator(ctrl)
			controller := &lazadaMMController{
				orderCreator: mockOrderCreator,
			}

			tt.setupMock(mockOrderCreator)
			controller.createMMCCBag(c)

			assert.Equal(t, tt.expectedStatus, w.Code)

			var response interface{}
			if tt.expectedStatus == http.StatusOK {
				var resp order.LazadaBaseResponse
				_ = json.Unmarshal(w.Body.Bytes(), &resp)
				response = resp
			} else {
				var resp order.LazadaBaseResponse
				_ = json.Unmarshal(w.Body.Bytes(), &resp)
				response = resp
			}

			assert.Equal(t, tt.expectedResponse, response)
		})
	}
}

func Test_lazadaMMController_createMMCCParcel(t *testing.T) {
	t.Parallel()
	gin.SetMode(gin.TestMode)
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logisticsInterface := &order.LazadaMMCreateParcelRequest{
		LogisticsOrderCode: "LOG123",
		TrackingNumber:     "TRACK123",
		Sender: order.Sender{
			ImID:      "SENDER123", // required
			Name:      "Sender Name",
			Phone:     "1234567890",
			Mobile:    "1234567890",
			Email:     "<EMAIL>",
			ZipCode:   "123456",
			StoreName: "Sender Store",
			Address: order.LazadaMMAddress{
				Country:       "SG",
				Province:      "Sender Province",
				City:          "Sender City",
				District:      "Sender District",
				Street:        "Sender Street",
				DetailAddress: "123 Sender St",
			},
		},
		Receiver: order.Receiver{
			Name:    "Receiver Name",
			Phone:   "0987654321",
			Mobile:  "0987654321",
			Email:   "<EMAIL>",
			ZipCode: "654321",
			Address: order.LazadaMMAddress{
				Country:       "MM",
				Province:      "Receiver Province",
				City:          "Receiver City",
				District:      "Receiver District",
				Street:        "Receiver Street",
				DetailAddress: "456 Receiver St",
			},
		},
		Parcel: order.Parcel{
			BigBagID: "BAG123",
			Weight:   "1.5",
			Price:    "100",
		},
	}

	validRequest := &order.LazadaRequest{
		LogisticsInterface: string(utils.JsonMarshalIgnoreError(logisticsInterface)),
	}

	type fields struct {
		orderCreator *mocks1.MockLazadaMMOrderCreator
	}
	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name               string
		fields             fields
		args               args
		setupMock          func(*mocks1.MockLazadaMMOrderCreator)
		request            *order.LazadaRequest
		setupContext       func(*gin.Context)
		expectedHTTPStatus int
		expectedResponse   interface{}
	}{
		{
			name:      "invalid request body",
			fields:    fields{orderCreator: mocks1.NewMockLazadaMMOrderCreator(ctrl)},
			setupMock: func(m *mocks1.MockLazadaMMOrderCreator) {},
			request:   nil,
			setupContext: func(c *gin.Context) {
				c.Request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
				c.Set(middlewareCfg.PartnerKey, &models.Partner{})
			},
			expectedHTTPStatus: http.StatusBadRequest,
			expectedResponse: order.LazadaBaseResponse{
				ErrorCode: order.LazadaErrorCodeInvalidRequest,
				Message:   "unexpected end of JSON input",
			},
		},
		{
			name:   "create MMCC parcel failed with bag not found",
			fields: fields{orderCreator: mocks1.NewMockLazadaMMOrderCreator(ctrl)},
			setupMock: func(m *mocks1.MockLazadaMMOrderCreator) {
				m.EXPECT().
					CreateMMCCParcel(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fplerror.ErrBagNotFound)
			},
			request: validRequest,
			setupContext: func(c *gin.Context) {
				c.Request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
				c.Set(middlewareCfg.PartnerKey, &models.Partner{ID: 1})
			},
			expectedHTTPStatus: http.StatusBadRequest,
			expectedResponse: order.LazadaBaseResponse{
				ErrorCode: order.LazadaErrorCodeInvalidRequest,
				Message:   fplerror.ErrBagNotFound.Error(),
			},
		},
		{
			name:   "create MMCC parcel failed with internal error",
			fields: fields{orderCreator: mocks1.NewMockLazadaMMOrderCreator(ctrl)},
			setupMock: func(m *mocks1.MockLazadaMMOrderCreator) {
				m.EXPECT().
					CreateMMCCParcel(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(errors.New("internal error"))
			},
			request: validRequest,
			setupContext: func(c *gin.Context) {
				c.Request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
				c.Set(middlewareCfg.PartnerKey, &models.Partner{ID: 1})
			},
			expectedHTTPStatus: http.StatusInternalServerError,
			expectedResponse: order.LazadaBaseResponse{
				ErrorCode: order.LazadaErrorCodeInternal,
				Message:   "internal error when creating order. request-id: TRACK123",
			},
		},
		{
			name: "create MMCC parcel success",
			setupMock: func(m *mocks1.MockLazadaMMOrderCreator) {
				m.EXPECT().
					CreateMMCCParcel(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil)
			},
			request: validRequest,
			setupContext: func(c *gin.Context) {
				c.Request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
				c.Set(middlewareCfg.PartnerKey, &models.Partner{ID: 1})
			},
			expectedHTTPStatus: http.StatusOK,
			expectedResponse: order.LazadaBaseResponse{
				Success: true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Setup request
			formValues := url.Values{}
			if tt.request != nil {
				formValues.Add("logistics_interface", tt.request.LogisticsInterface)
			}
			c.Request, _ = http.NewRequest(http.MethodPost, "", strings.NewReader(formValues.Encode()))

			// Apply context setup
			if tt.setupContext != nil {
				tt.setupContext(c)
			}

			// Setup mock
			mockCreator := mocks1.NewMockLazadaMMOrderCreator(ctrl)
			tt.setupMock(mockCreator)

			// Create and execute controller
			controller := &lazadaMMController{
				orderCreator: mockCreator,
			}
			controller.createMMCCParcel(c)

			// Assert response code and body
			assert.Equal(t, tt.expectedHTTPStatus, w.Code)

			var actualResponse order.LazadaBaseResponse
			decoder := json.NewDecoder(w.Body)
			err := decoder.Decode(&actualResponse)
			require.NoError(t, err)
			if !reflect.DeepEqual(tt.expectedResponse, actualResponse) {
				t.Errorf("expected response: %v, actual response: %v", tt.expectedResponse, actualResponse)
			}
		})
	}
}

func Test_convertLazadaMmParcelToOCReq(t *testing.T) {
	type args struct {
		request *order.LazadaMMCreateParcelRequest
	}
	tests := []struct {
		name string
		args args
		want *order.BaseRequest
	}{
		{
			name: "success with complete data",
			args: args{
				request: &order.LazadaMMCreateParcelRequest{
					LogisticsOrderCode: "LOG123",
					TrackingNumber:     "TRACK123",
					Sender: order.Sender{
						Name:  "John Doe",
						Phone: "1234567890",
						Email: "<EMAIL>",
						Address: order.LazadaMMAddress{
							DetailAddress: "123 Main St",
							Street:        "Main Street",
							District:      "Central",
							City:          "Singapore",
							Province:      "Singapore",
							Country:       "China",
						},
					},
					Receiver: order.Receiver{
						Name:  "Jane Smith",
						Phone: "0987654321",
						Email: "<EMAIL>",
						Address: order.LazadaMMAddress{
							DetailAddress: "456 Side St",
							Street:        "Side Street",
							District:      "North",
							City:          "Yangon",
							Province:      "Yangon",
							Country:       "Malaysia",
						},
					},
					Parcel: order.Parcel{
						BigBagID:      "BAG123",
						Weight:        "1.5",
						Length:        "10.0",
						Width:         "20.0",
						Height:        "30.0",
						DimensionUnit: "CM",
						WeightUnit:    "KG",
						Price:         "100.0",
						GoodsList: []order.Goods{
							{
								Name:     "Item 1",
								Quantity: "2",
								Price:    "11.2",
							},
						},
					},
				},
			},
			want: &order.BaseRequest{
				Source:           order.SourceCustomAPI,
				SourceOrderID:    null.StringFrom("BAG123").Ptr(),
				RefTrackingID:    null.StringFrom("LOG123").Ptr(),
				PartnerUniqueKey: null.StringFrom("LOG123_TRACK123").Ptr(),
				RequestId:        "TRACK123",
				From: order.Address{
					Name:          "John Doe",
					AddressLine1:  "123 Main St",
					AddressLine2:  null.StringFrom("Main Street").Ptr(),
					AddressLine3:  null.StringFrom("Central").Ptr(),
					City:          null.StringFrom("Singapore").Ptr(),
					StateProvince: null.StringFrom("Singapore").Ptr(),
					CountryCode:   "CN",
					ContactNumber: null.StringFrom("1234567890").Ptr(),
					ContactEmail:  null.StringFrom("<EMAIL>").Ptr(),
					PostCode:      null.StringFrom("").Ptr(),
				},
				To: order.Address{
					Name:          "Jane Smith",
					AddressLine1:  "456 Side St",
					AddressLine2:  null.StringFrom("Side Street").Ptr(),
					AddressLine3:  null.StringFrom("North").Ptr(),
					City:          null.StringFrom("Yangon").Ptr(),
					StateProvince: null.StringFrom("Yangon").Ptr(),
					CountryCode:   "MY",
					ContactNumber: null.StringFrom("0987654321").Ptr(),
					ContactEmail:  null.StringFrom("<EMAIL>").Ptr(),
					PostCode:      null.StringFrom("").Ptr(),
				},
				Delivery: &order.Delivery{},
				ParcelDetails: &models.ParcelDetails{
					ShipperSubmittedWeight:     null.Float64From(1.5).Ptr(),
					ShipperSubmittedWeightUnit: null.StringFrom("KG").Ptr(),
					Value:                      null.Float64From(100.0).Ptr(),
					ShipperSubmittedDimensions: &models.Dimensions{
						Length: null.Float64From(10.0).Ptr(),
						Width:  null.Float64From(20.0).Ptr(),
						Height: null.Float64From(30.0).Ptr(),
						Unit:   null.StringFrom("CM").Ptr(),
					},
					ShipperRefNo: "TRACK123",
				},
				Items: order.ParcelItemSlice{
					{
						SKUId:                 null.StringFrom("").Ptr(),
						Description:           null.StringFrom("Item 1").Ptr(),
						NativeDescription:     null.StringFrom("").Ptr(),
						Quantity:              null.IntFrom(2).Ptr(),
						UnitValue:             null.Float64From(11.2).Ptr(),
						UnitWeight:            null.Float64From(0).Ptr(),
						HSCode:                null.UintFrom(0).Ptr(),
						Url:                   null.StringFrom("").Ptr(),
						GoodsCurrency:         null.StringFrom("").Ptr(),
						GstRegistrationNumber: null.StringFrom("").Ptr(),
					},
				},
				IsMMCCB2C: true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertLazadaMmParcelToOCReq(tt.args.request)
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_lazadaMMController_updateMMCCBag(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		setupMocks     func(creator *mocks.MockLazadaMMOrderCreator)
		request        order.LazadaRequest
		expectedStatus int
		expectedBody   order.LazadaBaseResponse
	}{
		{
			name: "success",
			setupMocks: func(creator *mocks.MockLazadaMMOrderCreator) {
				creator.EXPECT().
					UpdateMMCCBag(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil)
			},
			request: order.LazadaRequest{
				LogisticsInterface: `{"bigBagID":"THAI_TEST_LAZADA_001","parcelList":[{"logisticsOrderCode":"LP00062858846767","trackingNumber":"NLIDXB1004373325"}],"updateType":"DELETE"}`,
			},
			expectedStatus: http.StatusOK,
			expectedBody: order.LazadaBaseResponse{
				Success: true,
			},
		},
		{
			name: "invalid request body",
			request: order.LazadaRequest{
				LogisticsInterface: `invalid json`,
			},
			expectedStatus: http.StatusBadRequest,
			expectedBody: order.LazadaBaseResponse{
				Success:   false,
				ErrorCode: order.LazadaErrorCodeInvalidRequest,
				Message:   "invalid character 'i' looking for beginning of value",
			},
		},
		{
			name: "bag not found error",
			setupMocks: func(creator *mocks.MockLazadaMMOrderCreator) {
				creator.EXPECT().
					UpdateMMCCBag(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fplerror.ErrBagNotFound)
			},
			request: order.LazadaRequest{
				LogisticsInterface: `{"bigBagID":"THAI_TEST_LAZADA_001","parcelList":[{"logisticsOrderCode":"LP00062858846767","trackingNumber":"NLIDXB1004373325"}],"updateType":"DELETE"}`,
			},
			expectedStatus: http.StatusBadRequest,
			expectedBody: order.LazadaBaseResponse{
				Success:   false,
				ErrorCode: order.LazadaErrorCodeInvalidRequest,
				Message:   "bag not found. big-bag-id: THAI_TEST_LAZADA_001",
			},
		},
		{
			name: "bad request error",
			setupMocks: func(creator *mocks.MockLazadaMMOrderCreator) {
				creator.EXPECT().
					UpdateMMCCBag(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fplerror.ErrBadRequest)
			},
			request: order.LazadaRequest{
				LogisticsInterface: `{"bigBagID":"THAI_TEST_LAZADA_001","parcelList":[{"logisticsOrderCode":"LP00062858846767","trackingNumber":"NLIDXB1004373325"}],"updateType":"DELETE"}`,
			},
			expectedStatus: http.StatusBadRequest,
			expectedBody: order.LazadaBaseResponse{
				Success:   false,
				ErrorCode: order.LazadaErrorCodeInvalidRequest,
				Message:   "the request can't be processed: %v",
			},
		},
		{
			name: "internal error",
			setupMocks: func(creator *mocks.MockLazadaMMOrderCreator) {
				creator.EXPECT().
					UpdateMMCCBag(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(errors.New("internal error"))
			},
			request: order.LazadaRequest{
				LogisticsInterface: `{"bigBagID":"THAI_TEST_LAZADA_001","parcelList":[{"logisticsOrderCode":"LP00062858846767","trackingNumber":"NLIDXB1004373325"}],"updateType":"DELETE"}`,
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody: order.LazadaBaseResponse{
				Success:   false,
				ErrorCode: order.LazadaErrorCodeInternal,
				Message:   "an internal error occurred. big-bag-id: THAI_TEST_LAZADA_001",
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockCreator := mocks.NewMockLazadaMMOrderCreator(ctrl)
			if tt.setupMocks != nil {
				tt.setupMocks(mockCreator)
			}

			controller := &lazadaMMController{
				orderCreator: mockCreator,
			}

			gin.SetMode(gin.TestMode)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Setup request
			formValues := url.Values{}
			formValues.Add("logistics_interface", tt.request.LogisticsInterface)
			c.Request = httptest.NewRequest(http.MethodPost, "/", strings.NewReader(formValues.Encode()))

			c.Request.Header.Set("Content-Type", "application/x-www-form-urlencoded")

			// Set partner in context
			c.Set(middlewareCfg.PartnerKey, &models.Partner{})

			// Call the handler
			controller.updateMMCCBag(c)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)

			var response order.LazadaBaseResponse
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedBody, response)
		})
	}
}

func Test_lazadaMMController_createMMCCParcelAIDC(t *testing.T) {
	t.Parallel()
	gin.SetMode(gin.TestMode)
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name           string
		requestBody    order.LazadaCreateParcelReq
		setupMocks     func(creator *mocks.MockLazadaMMOrderCreator)
		expectedStatus int
		expectedBody   interface{}
	}{
		{
			name: "successful parcel creation",
			requestBody: order.LazadaCreateParcelReq{
				// Required fields
				OrderCode:      "ORDER123",
				TrackingNumber: "TRACK123",
				IdempotentId:   "IDEMPOTENT123",

				// Required receiver information
				Receiver: order.LazadaCCReceiver{
					Name:  "John Doe",
					Phone: "1234567890",
					Address: order.LazadaCCReceiverAddress{
						Country:       "MY",
						Province:      "Selangor",
						City:          "Shah Alam",
						DetailAddress: "123 Main Street",
					},
				},

				// Required pre-reservation information
				PreResInfo: order.LazadaCCPreResInfo{
					ResName: "Ninjalogistics",
					Mobile:  "9876543210",
					Address: order.LazadaCCPreResInfoAddress{
						Country:       "CN",
						DetailAddress: "金港北三路6号",
					},
				},

				// Required parcel information
				Parcel: order.LazadaCCParcel{
					Weight:        lo.ToPtr(1.5), // in kg
					WeightUnit:    "kg",
					Price:         lo.ToPtr(100.0),
					PriceUnit:     lo.ToPtr("USD"),
					Length:        lo.ToPtr(10.0), // in cm
					Width:         lo.ToPtr(20.0),
					Height:        lo.ToPtr(30.0),
					DimensionUnit: lo.ToPtr("cm"),
					GoodsQuantity: 1,
					GoodsList: []*order.LazadaCCGoods{
						{
							Name:     "Test Item",
							Quantity: 1,
							Price:    lo.ToPtr(100.0),
							SkuCode:  lo.ToPtr("SKU123"),
						},
					},
				},

				// Optional fields
				ToLocation: "MY",
				Features: map[string]string{
					"taxId": "TAX123",
				},
				TradeInfo: struct {
					TradeOrderId string `json:"tradeOrderId" binding:"max=512"`
				}{
					TradeOrderId: "TRADE123",
				},
			},
			setupMocks: func(creator *mocks.MockLazadaMMOrderCreator) {
				creator.EXPECT().
					CreateMMCCParcelV2(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   order.NewLazadaMMOCResponse("ORDER123"),
		},
		{
			name:           "invalid request binding",
			requestBody:    order.LazadaCreateParcelReq{},
			setupMocks:     func(creator *mocks.MockLazadaMMOrderCreator) {},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, "Key: 'LazadaCreateParcelReq.receiver' Error:Field validation for 'receiver' failed on the 'required' tag\nKey: 'LazadaCreateParcelReq.preResInfo' Error:Field validation for 'preResInfo' failed on the 'required' tag\nKey: 'LazadaCreateParcelReq.parcel' Error:Field validation for 'parcel' failed on the 'required' tag\nKey: 'LazadaCreateParcelReq.idempotentId' Error:Field validation for 'idempotentId' failed on the 'required' tag\nKey: 'LazadaCreateParcelReq.orderCode' Error:Field validation for 'orderCode' failed on the 'required' tag"),
		},
		{
			name: "bad request error",
			requestBody: order.LazadaCreateParcelReq{
				// Required fields
				OrderCode:      "ORDER123",
				TrackingNumber: "TRACK123",
				IdempotentId:   "IDEMPOTENT123",

				// Required receiver information
				Receiver: order.LazadaCCReceiver{
					Name:  "John Doe",
					Phone: "1234567890",
					Address: order.LazadaCCReceiverAddress{
						Country:       "MY",
						Province:      "Selangor",
						City:          "Shah Alam",
						DetailAddress: "123 Main Street",
					},
				},

				// Required pre-reservation information
				PreResInfo: order.LazadaCCPreResInfo{
					ResName: "Ninjalogistics",
					Mobile:  "9876543210",
					Address: order.LazadaCCPreResInfoAddress{
						Country:       "CN",
						DetailAddress: "金港北三路6号",
					},
				},

				// Required parcel information
				Parcel: order.LazadaCCParcel{
					Weight:        lo.ToPtr(1.5), // in kg
					WeightUnit:    "kg",
					Price:         lo.ToPtr(100.0),
					PriceUnit:     lo.ToPtr("USD"),
					Length:        lo.ToPtr(10.0), // in cm
					Width:         lo.ToPtr(20.0),
					Height:        lo.ToPtr(30.0),
					DimensionUnit: lo.ToPtr("cm"),
					GoodsQuantity: 1,
					GoodsList: []*order.LazadaCCGoods{
						{
							Name:     "Test Item",
							Quantity: 1,
							Price:    lo.ToPtr(100.0),
							SkuCode:  lo.ToPtr("SKU123"),
						},
					},
				},

				// Optional fields
				ToLocation: "MY",
				Features: map[string]string{
					"taxId": "TAX123",
				},
				TradeInfo: struct {
					TradeOrderId string `json:"tradeOrderId" binding:"max=512"`
				}{
					TradeOrderId: "TRADE123",
				},
			},
			setupMocks: func(creator *mocks.MockLazadaMMOrderCreator) {
				creator.EXPECT().
					CreateMMCCParcelV2(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(fplerror.ErrBadRequest)
			},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, fplerror.ErrBadRequest.Error()),
		},
		{
			name: "internal server error",
			requestBody: order.LazadaCreateParcelReq{
				// Required fields
				OrderCode:      "ORDER123",
				TrackingNumber: "TRACK123",
				IdempotentId:   "IDEMPOTENT123",

				// Required receiver information
				Receiver: order.LazadaCCReceiver{
					Name:  "John Doe",
					Phone: "1234567890",
					Address: order.LazadaCCReceiverAddress{
						Country:       "MY",
						Province:      "Selangor",
						City:          "Shah Alam",
						DetailAddress: "123 Main Street",
					},
				},

				// Required pre-reservation information
				PreResInfo: order.LazadaCCPreResInfo{
					ResName: "Ninjalogistics",
					Mobile:  "9876543210",
					Address: order.LazadaCCPreResInfoAddress{
						Country:       "CN",
						DetailAddress: "金港北三路6号",
					},
				},

				// Required parcel information
				Parcel: order.LazadaCCParcel{
					Weight:        lo.ToPtr(1.5), // in kg
					WeightUnit:    "kg",
					Price:         lo.ToPtr(100.0),
					PriceUnit:     lo.ToPtr("USD"),
					Length:        lo.ToPtr(10.0), // in cm
					Width:         lo.ToPtr(20.0),
					Height:        lo.ToPtr(30.0),
					DimensionUnit: lo.ToPtr("cm"),
					GoodsQuantity: 1,
					GoodsList: []*order.LazadaCCGoods{
						{
							Name:     "Test Item",
							Quantity: 1,
							Price:    lo.ToPtr(100.0),
							SkuCode:  lo.ToPtr("SKU123"),
						},
					},
				},

				// Optional fields
				ToLocation: "MY",
				Features: map[string]string{
					"taxId": "TAX123",
				},
				TradeInfo: struct {
					TradeOrderId string `json:"tradeOrderId" binding:"max=512"`
				}{
					TradeOrderId: "TRADE123",
				},
			},
			setupMocks: func(creator *mocks.MockLazadaMMOrderCreator) {
				creator.EXPECT().
					CreateMMCCParcelV2(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(errors.New("internal error"))
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   order.NewLazadaErrorResponse(order.LazadaErrorCodeInternal, "internal error when creating order"),
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Given
			mockCreator := mocks.NewMockLazadaMMOrderCreator(ctrl)
			controller := &lazadaMMController{
				orderCreator: mockCreator,
			}

			// Setup test request
			body, _ := json.Marshal(tt.requestBody)
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Request = httptest.NewRequest(http.MethodPost, "/", bytes.NewBuffer(body))
			c.Request.Header.Set("Content-Type", "application/json")

			// Set partner in context
			partner := &models.Partner{ID: 1}
			c.Set(middlewareCfg.PartnerKey, partner)

			// Setup mock expectations
			tt.setupMocks(mockCreator)

			// When
			controller.createMMCCParcelAIDC(c)

			// Then
			assert.Equal(t, tt.expectedStatus, w.Code)

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			expectedResponse, _ := json.Marshal(tt.expectedBody)
			var expectedMap map[string]interface{}
			_ = json.Unmarshal(expectedResponse, &expectedMap)

			assert.Equal(t, expectedMap, response)
		})
	}
}
