package orders

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"

	"github.com/biter777/countries"
	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/rs/zerolog/log"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"

	"bitbucket.ninjavan.co/cg/datadog-agent---go/nvtracer"

	middlewareCfg "git.ninjavan.co/3pl/configs/middleware"
	"git.ninjavan.co/3pl/configs/orders"
	"git.ninjavan.co/3pl/errors/fplerror"
	"git.ninjavan.co/3pl/handlers/auth"
	"git.ninjavan.co/3pl/handlers/order_creation"
	"git.ninjavan.co/3pl/httpmodels/order"
	tplMiddleware "git.ninjavan.co/3pl/middleware"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/utils"
	"git.ninjavan.co/3pl/utils/loggerutils"
)

type lazadaController struct {
	orderCreator LazadaOrderCreator
}

const moduleName = "lazada-order-controller" // used for logging
const invalidRequestMsg = "invalid-request"

type LazadaOrderCreator interface {
	CreateMMCCOrder(
		ctx context.Context, partner *models.Partner, bagReq *order.BaseRequest, parcelReqs []*order.BaseRequest,
	) (*models.Parcel, error)
	UpdateMMCCOrder(ctx context.Context, partner models.Partner, req order.FPLLazadaUpdateRequest) error
}

func LazadaRouteGroup(rg *gin.RouterGroup) {
	ctrl := &lazadaController{
		orderCreator: order_creation.NewLazadaCreator(),
	}

	rg.Group(
		"/lazada/mmcc",
		auth.LazadaMMCCAuthHandler(),
	).POST(
		"/order-create",
		tplMiddleware.SaveLazadaOrderRequest(),
		ctrl.createLazadaMMCCOrder,
	).POST(
		"/order-update",
		tplMiddleware.SaveLazadaOrderRequest(),
		ctrl.updateLazadaMMCCOrder,
	)
}

// @Summary		Create MMCC Bag for Lazada
// @description	Create MMCC Bag for Lazada
// @Tags			Lazada
// @Accept			json
// @Produce		json
// @Param			request	body		order.LazadaMmccOCRequest	true	"payload"
// @Success		200		{object}	order.LazadaMmccOCResponse
// @Failure		500		{object}	order.LazadaBaseResponse
// @Router			/v1/lazada/mmcc/order-create [post]
func (ctrl *lazadaController) createLazadaMMCCOrder(c *gin.Context) {
	ctx, span := nvtracer.CreateSpanFromContext(c.Request.Context())
	defer span.Finish()

	logger := loggerutils.WithModule(log.Ctx(ctx), moduleName)

	r := order.LazadaRequest{}
	if err := c.ShouldBindWith(&r, binding.Form); err != nil {
		logger.Info().Err(err).Msg(invalidRequestMsg)
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	ocRequest := &order.LazadaMmccOCRequest{}
	err := json.Unmarshal([]byte(r.LogisticsInterface), ocRequest)
	if err != nil {
		logger.Info().Err(err).Msg(invalidRequestMsg)
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	if err = binding.Validator.ValidateStruct(ocRequest); err != nil {
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	bagBaseReq, parcelBaseReqs, err := convertLazadaMmccOCReq(ocRequest)
	if err != nil {
		logger.Info().Err(err).Msg(invalidRequestMsg)
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	v, _ := c.Get(middlewareCfg.PartnerKey)
	partner := v.(*models.Partner)
	bag, err := ctrl.orderCreator.CreateMMCCOrder(ctx, partner, bagBaseReq, parcelBaseReqs)
	tplMiddleware.SetParcelContext(c, bag)
	if err != nil {
		switch {
		case errors.Is(err, order_creation.ErrBagCompleted):
			logger.Info().Err(err).Msg(invalidRequestMsg)
			c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		case errors.Is(err, fplerror.ErrDuplicated):
			logger.Info().Err(err).Msg("duplicated-request")
			c.JSON(http.StatusConflict, order.NewLazadaErrorResponse(
				order.LazadaErrorCodeInvalidRequest,
				fmt.Sprintf("duplicated request id: %s", ocRequest.UniqueCode)),
			)
		default:
			logger.Err(err).Msg("error-creating-lazada-order")
			c.JSON(http.StatusInternalServerError,
				order.NewLazadaErrorResponse(
					order.LazadaErrorCodeInternal,
					fmt.Sprintf("internal error when creating order. request-id: %s", ocRequest.UniqueCode),
				),
			)
		}
		return
	}

	c.JSON(http.StatusOK, order.LazadaMmccOCResponse{
		LazadaBaseResponse: order.LazadaBaseResponse{
			Success: true,
		},
		InboundWarehouseAddress: lzdWarehouseAddressByWHCode[ocRequest.CountryCode][ocRequest.WarehouseCode],
		FirstMileOrderCode:      bag.TrackingID,
	})
}

// @Summary		Update MMCC Bag for Lazada
// @description	Update MMCC Bag for Lazada
// @Tags			Lazada
// @Accept			json
// @Produce		json
// @Param			request	body		order.LazadaUpdateRequest	true	"payload"
// @Success		200		{object}	order.LazadaBaseResponse
// @Failure		500		{object}	order.LazadaBaseResponse
// @Router			/v1/lazada/mmcc/order-update [post]
func (ctrl *lazadaController) updateLazadaMMCCOrder(c *gin.Context) {
	ctx, span := nvtracer.CreateSpanFromContext(c.Request.Context())
	defer span.Finish()
	logger := loggerutils.WithModule(log.Ctx(ctx), moduleName)

	var r order.LazadaRequest
	if err := c.ShouldBindWith(&r, binding.Form); err != nil {
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	var updateParcelReq order.LazadaUpdateRequest
	err := json.Unmarshal([]byte(r.LogisticsInterface), &updateParcelReq)
	if err != nil {
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}
	err = binding.Validator.ValidateStruct(&updateParcelReq)
	if err != nil {
		c.JSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
		return
	}

	v, _ := c.Get(middlewareCfg.PartnerKey)
	partner := v.(*models.Partner)
	err = ctrl.orderCreator.UpdateMMCCOrder(ctx, *partner, *updateParcelReq.ToFPL())
	if err != nil {
		logger.Err(err).Msg("error-update-lazada-order")
		switch {
		case fplerror.ErrBadRequest.Equal(err):
			c.JSON(http.StatusBadRequest,
				order.NewLazadaErrorResponse(
					order.LazadaErrorCodeInvalidRequest, "the update request is invalid",
				),
			)
		default:
			c.JSON(http.StatusInternalServerError,
				order.NewLazadaErrorResponse(
					order.LazadaErrorCodeInternal, "internal error when updating order",
				),
			)
		}
		return
	}
	c.JSON(http.StatusOK, order.LazadaBaseResponse{
		Success: true,
	})
}

func convertLazadaMmccOCReq(request *order.LazadaMmccOCRequest) (*order.BaseRequest, []*order.BaseRequest, error) {
	serviceCodeByCargoType, ok := orders.LazadaServiceCodeByCountryAndWarehouseCodeAndCargoType[request.CountryCode][request.WarehouseCode]
	if !ok {
		return nil, nil, fmt.Errorf("service not configured for country code: %s", request.CountryCode)
	}
	serviceCode := serviceCodeByCargoType[request.CargoType]
	if serviceCode == "" {
		return nil, nil, fmt.Errorf("service not configured for country code: %s and cargo type %s",
			request.CountryCode,
			request.CargoType,
		)
	}
	from := order.Address{
		Name:          request.Sender.Name,
		AddressLine1:  request.Sender.Address,
		AddressLine2:  null.StringFrom(request.Sender.Street).Ptr(),
		AddressLine3:  null.StringFrom(request.Sender.County).Ptr(),
		City:          null.StringFrom(request.Sender.City).Ptr(),
		StateProvince: null.StringFrom(request.Sender.Province).Ptr(),
		CountryCode:   request.Sender.Country,
		ContactNumber: null.StringFrom(request.Sender.Phone).Ptr(),
	}
	to := order.Address{
		Name:          request.Receiver.Name,
		AddressLine1:  request.Receiver.Address,
		AddressLine2:  null.StringFrom(request.Receiver.Street).Ptr(),
		AddressLine3:  null.StringFrom(request.Receiver.County).Ptr(),
		City:          null.StringFrom(request.Receiver.City).Ptr(),
		StateProvince: null.StringFrom(request.Receiver.Province).Ptr(),
		CountryCode:   request.Receiver.Country,
		PostCode:      null.StringFrom(request.Receiver.ZipCode).Ptr(),
		ContactNumber: null.StringFrom(request.Receiver.Phone).Ptr(),
	}
	delivery := &order.Delivery{
		Instructions: request.Remark,
	}

	isComplete, _ := strconv.ParseBool(request.IsComplete)
	bagReq := &order.BaseRequest{
		Source:        order.SourceCustomAPI,
		SourceOrderID: null.StringFrom(request.ExpressNumber).Ptr(),
		RefTrackingID: null.StringFrom(request.ExpressNumber).Ptr(),
		ServiceCode:   serviceCode,
		From:          from,
		To:            to,
		Delivery:      delivery,
		ParcelDetails: &models.ParcelDetails{
			ShipperSubmittedWeight: null.NewFloat64(
				utils.ParseFloat64(null.StringFrom(request.TotalWeight)).Float64/1000,
				utils.ParseFloat64(null.StringFrom(request.TotalWeight)).Valid,
			).Ptr(), // convert g to kg
			Lazada: &models.LazadaMetadata{
				IsComplete:      isComplete,
				OrderType:       request.FirstMileOrderType,
				TenantCode:      request.Attributes.TenantCode,
				ContainerType:   request.ContainerType,
				TotalVolume:     utils.ParseFloat64(null.StringFrom(request.TotalVolume)).Float64,
				WarehouseCode:   request.WarehouseCode,
				ToWarehouseCode: request.ToWarehouseCode,
			},
			Quantity: utils.ParseUint(null.StringFrom(request.Attributes.TotalOrders)).Ptr(),
		},
		IsMMCCB2C:        true,
		RequestId:        request.UniqueCode,
		PartnerUniqueKey: null.StringFrom(request.ExpressNumber).Ptr(),
	}

	bizOrderCodeToParcelReq := make(map[string]*order.BaseRequest)
	for _, detail := range request.Details {
		parcelReq, existed := bizOrderCodeToParcelReq[detail.BizOrderCode]
		if !existed {
			parcelReq = &order.BaseRequest{
				Source:        order.SourceCustomAPI,
				RefTrackingID: null.StringFrom(detail.BizOrderCode).Ptr(),
				ServiceCode:   serviceCode,
				From:          from,
				To:            to,
				Delivery:      delivery,
				ParcelDetails: &models.ParcelDetails{
					Quantity: utils.ParseUint(null.StringFrom(detail.BoxCount)).Ptr(),
					Lazada: &models.LazadaMetadata{
						IsComplete:       isComplete,
						OrderType:        request.FirstMileOrderType,
						TenantCode:       request.Attributes.TenantCode,
						ContainerType:    request.ContainerType,
						AppointOrderCode: detail.AppointOrderCode,
					},
				},
				PartnerUniqueKey: null.StringFrom(detail.BizOrderCode).Ptr(),
			}
			bizOrderCodeToParcelReq[detail.BizOrderCode] = parcelReq
		}

		item := &order.ParcelItem{
			Description: null.StringFrom(detail.Name).Ptr(),
			Dimensions: &models.Dimensions{
				Length: utils.ParseFloat64(null.StringFrom(detail.BoxLength)).Ptr(),
				Width:  utils.ParseFloat64(null.StringFrom(detail.BoxWidth)).Ptr(),
				Height: utils.ParseFloat64(null.StringFrom(detail.BoxHeight)).Ptr(),
			},
			UnitWeight: null.NewFloat64(
				utils.ParseFloat64(null.StringFrom(detail.BoxWeight)).Float64/1000,
				utils.ParseFloat64(null.StringFrom(detail.BoxWeight)).Valid,
			).Ptr(), // convert g to kg
			UnitValue:     utils.ParseFloat64(null.StringFrom(detail.Price)).Ptr(),
			GoodsCurrency: lo.ToPtr(detail.Currency),
			SKUId:         lo.ToPtr(detail.ItemID),
			SupplierId:    lo.ToPtr(detail.RealSupplierId),
			Url:           lo.ToPtr(detail.ItemLink),
			Quantity:      utils.ParseInt(detail.Count).Ptr(),
			ImageURL:      null.StringFrom(detail.ItemPicture).Ptr(),
			BoxNo:         null.StringFrom(detail.BoxNo).Ptr(),
			BoxVolume:     null.StringFrom(detail.BoxVolume).Ptr(),
			Brand:         null.StringFrom(detail.ItemBrand).Ptr(),
			Material:      null.StringFrom(detail.Material).Ptr(),
		}
		parcelReq.Items = append(parcelReq.Items, item)
	}
	parcelReqs := lo.MapToSlice(bizOrderCodeToParcelReq, func(_ string, v *order.BaseRequest) *order.BaseRequest {
		return v
	})

	return bagReq, parcelReqs, nil
}

const (
	cnCountry = "中国"

	wh1Province = "浙江省"
	wh1City     = "金华市"
	wh1District = "义乌市"
	wh1Name     = "汤孟球"

	wh2Province = "广东省"
	wh2City     = "广州市"
	wh2District = "白云区"
	wh2Name     = "曹明"
)

var lzdWarehouseAddressByWHCode = map[string]map[string]order.InboundWarehouseAddress{
	countries.ID.Alpha2(): {
		orders.LazadaFromWarehouseCodeAtRegion1: {
			Country:  countries.CN.Alpha2(),
			Province: wh1Province,
			City:     wh1City,
			District: wh1District,
			Street:   "北苑街道",
			Address:  "浙江省金华市义乌市北苑街道川塘路11号",
			Name:     wh1Name,
			Phone:    "17280588004",
		},
		orders.LazadaFromWarehouseCodeAtRegion2: {
			Country:  countries.CN.Alpha2(),
			Province: wh2Province,
			City:     wh2City,
			District: wh2District,
			Street:   "",
			Address:  "江高镇三元岗三元南路37号11号仓库",
			Name:     wh2Name,
			Phone:    "18073789991",
		},
		orders.LazadaFromWarehouseCodeTest: {
			Country:  countries.CN.Alpha2(),
			Province: wh1Province,
			City:     wh1City,
			District: wh1District,
			Street:   "北苑街道",
			Address:  "浙江省金华市义乌市北苑街道川塘路11号",
			Name:     wh1Name,
			Phone:    "17280588004",
		},
	},
	countries.TH.Alpha2(): {
		orders.LazadaFromWarehouseCodeAtRegion1: {
			Country:  cnCountry,
			Province: wh1Province,
			City:     wh1City,
			District: wh1District,
			Street:   "四海大道与弘贸路交叉口弘贸路2号",
			Address:  "四海大道与弘贸路交叉口弘贸路2号义乌盛丰物流仓5号仓",
			Name:     "陈小姐 18257074801 / 成先生 158 7495 0101",
			Phone:    "18257074801",
		},
		orders.LazadaFromWarehouseCodeAtRegion2: {
			Country:  cnCountry,
			Province: wh2Province,
			City:     "深圳市",
			District: "宝安区",
			Street:   "福海街道",
			Address:  "塘尾社区凤塘大道聚源工业区",
			Name:     "杨先生",
			Phone:    "15220407068",
		},
		orders.LazadaFromWarehouseCodeEastChinaConsolidation: {
			Country:  cnCountry,
			Province: wh1Province,
			City:     wh1City,
			District: wh1District,
			Street:   "四海大道与弘贸路交叉口弘贸路2号",
			Address:  "四海大道与弘贸路交叉口弘贸路2号义乌盛丰物流仓5号仓",
			Name:     "陈小姐 18257074801 / 成先生 158 7495 0101",
			Phone:    "18257074801",
		},
		orders.LazadaFromWarehouseCodeSouthChinaConsolidation: {
			Country:  cnCountry,
			Province: wh2Province,
			City:     "深圳市",
			District: "宝安区",
			Street:   "福海街道",
			Address:  "塘尾社区凤塘大道聚源工业区",
			Name:     "杨先生",
			Phone:    "15220407068",
		},
		orders.LazadaFromWarehouseCodeTest: {
			Country:  cnCountry,
			Province: wh1Province,
			City:     wh1City,
			District: wh1District,
			Street:   "四海大道与弘贸路交叉口弘贸路2号",
			Address:  "四海大道与弘贸路交叉口弘贸路2号义乌盛丰物流仓5号仓",
			Name:     "陈小姐 18257074801 / 成先生 158 7495 0101",
			Phone:    "18257074801",
		},
	},
}
