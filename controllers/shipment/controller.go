package shipment

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.ninjavan.co/3pl/configs"
	middlewareCfg "git.ninjavan.co/3pl/configs/middleware"
	shipmentConstants "git.ninjavan.co/3pl/configs/shipment"
	"git.ninjavan.co/3pl/errors/fplerror"
	"git.ninjavan.co/3pl/handlers/shipment"
	shipmentHandler "git.ninjavan.co/3pl/handlers/shipment"
	update_interface "git.ninjavan.co/3pl/handlers/shipment/shipment_interface"
	"git.ninjavan.co/3pl/httpmodels"
	shipmentRequest "git.ninjavan.co/3pl/httpmodels/shipment"
	"git.ninjavan.co/3pl/httpmodels/shipment_irregularity"
	"git.ninjavan.co/3pl/middleware"
	"git.ninjavan.co/3pl/services/auth"
	"git.ninjavan.co/3pl/utils"
)

type shipmentController struct {
	getter                         update_interface.DetailsGettable
	deleter                        update_interface.Deleter
	splitor                        update_interface.Splitor
	moveBagHandler                 update_interface.MoveBagHandler
	documentHandler                update_interface.DocumentHandler
	documentCreateHandler          update_interface.DocumentCreateHandler
	documentGetHandler             update_interface.DocumentGetHandler
	documentDeleteHandler          update_interface.DocumentDeleteHandler
	shipmentIrregularityGetHandler update_interface.ShipmentIrregularityGetHandler
	shipmentIrregularityCreator    update_interface.ShipmentIrregularityCreator
	shipmentIrregularityDeleter    update_interface.ShipmentIrregularityDeleter
}

const shipmentIdParam = "/:shipment_id"

const shipmentIdDocumentParam = "/:shipment_id" + "/mawb-document"

func RouteGroup(rg *gin.RouterGroup) {
	shipmentCtrl := &shipmentController{
		getter:                         shipmentHandler.NewGetter(),
		deleter:                        shipmentHandler.NewDeleter(),
		splitor:                        shipmentHandler.NewSplitor(),
		moveBagHandler:                 shipmentHandler.NewMoveBagHandler(),
		documentHandler:                shipmentHandler.NewDocumentHandler(),
		documentCreateHandler:          shipmentHandler.NewDocumentCreateHandler(),
		documentGetHandler:             shipmentHandler.NewDocumentGetHandler(),
		documentDeleteHandler:          shipmentHandler.NewDocumentDeleteHandler(),
		shipmentIrregularityGetHandler: shipmentHandler.NewShipmentIrregularityGetHandler(),
		shipmentIrregularityCreator:    shipmentHandler.NewShipmentIrregularityCreator(),
		shipmentIrregularityDeleter:    shipmentHandler.NewShipmentIrregularityDeleter(),
	}

	router := rg.Group("/shipments")
	{
		router.GET(
			"",
			middleware.Auth(configs.ScopeCrossBorderAssociate, configs.ScopeShipmentRead),
			middleware.PagingParams(),
			middleware.SortingParams(shipmentRequest.SortableFields...),
			List,
		)
		router.POST("", middleware.Auth(configs.ScopeCrossBorderAssociate, configs.ScopeShipmentWrite), middleware.GetUserInfo(), Create)

		router.GET(shipmentIdParam, middleware.Auth(configs.ScopeCrossBorderAssociate), shipmentCtrl.get)
		router.PATCH(shipmentIdParam, middleware.Auth(configs.ScopeCrossBorderAssociate), middleware.GetUserInfo(), Update)
		router.POST(shipmentIdParam+"/inbound-refresh", middleware.Auth(configs.ScopeCrossBorderAssociate), InboundRefresh)
		router.DELETE(shipmentIdParam, middleware.Auth(configs.ScopeCrossBorderAssociate, configs.ScopeShipmentWrite), middleware.GetUserInfo(), shipmentCtrl.delete)
		router.POST(shipmentIdParam+"/split", middleware.Auth(configs.ScopeCrossBorderAssociate), shipmentCtrl.split)
		router.POST(shipmentIdParam+"/move_bag", middleware.Auth(configs.ScopeCrossBorderAssociate), middleware.GetUserInfo(), shipmentCtrl.moveBags)
		router.GET(shipmentIdParam+"/supporting-documents", middleware.Auth(configs.ScopeCrossBorderAssociate, configs.ScopeShipmentRead), shipmentCtrl.getSupportingDocs)
		router.POST(shipmentIdDocumentParam, middleware.Auth(configs.ScopeCrossBorderAssociate, configs.ScopeShipmentWrite), middleware.GetUserInfo(), shipmentCtrl.createDocument)
		router.GET(shipmentIdDocumentParam, middleware.Auth(configs.ScopeCrossBorderAssociate, configs.ScopeShipmentRead), shipmentCtrl.getDocument)
		router.DELETE(shipmentIdDocumentParam, middleware.Auth(configs.ScopeCrossBorderAssociate, configs.ScopeShipmentRead), middleware.GetUserInfo(), shipmentCtrl.deleteDocument)
		router.GET(shipmentIdParam+"/irregularities", middleware.Auth(configs.ScopeCrossBorderAssociate, configs.ScopeShipmentRead), shipmentCtrl.getListOperationalIssues)
		router.POST(shipmentIdParam+"/irregularities",
			middleware.Auth(configs.ScopeCrossBorderAssociate, configs.ScopeShipmentWrite),
			middleware.GetUserInfo(),
			shipmentCtrl.createIrregularity)
		router.DELETE(shipmentIdParam+"/irregularities/:irregularity_id",
			middleware.Auth(configs.ScopeCrossBorderAssociate, configs.ScopeShipmentWrite),
			shipmentCtrl.deleteIrregularity)
	}

	shipmentMovementStatusRouterGroup := rg.Group("/shipment-movement-statuses")
	{
		shipmentMovementStatusRouterGroup.GET("/issues-reasons", middleware.Auth(configs.ScopeCrossBorderAssociate, configs.ScopeShipmentRead), shipmentCtrl.getListIssuesAndReasons)
	}
}

//	@Description	Create a new shipment
//	@Accept			json
//	@Produce		json
//	@Tags			Shipments
//	@Param			request	body		shipmentRequest.CreateRequest	true	"Shipment creation data"
//	@Success		201		{object}	object{data=object}
//	@Failure		400		{object}	object{error=fplerror.errResponse}
//	@Failure		500		{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments [post]
func Create(c *gin.Context) {
	req := shipmentRequest.CreateRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		_ = c.Error(err)
		return
	}

	userInfo := c.MustGet(middlewareCfg.UserInfoKey).(auth.UserInfo)
	shipment, err := shipment.NewCreator().Create(c, req, userInfo)
	if err != nil {
		_ = c.Error(err)
		return
	}

	c.JSON(201, gin.H{"data": shipment})
}

//	@Description	Get a shipment by ID
//	@Produce		json
//	@Tags			Shipments
//	@Param			shipment_id	path		uint	true	"Shipment ID"
//	@Success		200			{object}	object{data=object}
//	@Failure		400			{object}	object{error=fplerror.errResponse}
//	@Failure		404			{object}	object{error=fplerror.errResponse}
//	@Failure		500			{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments/{shipment_id} [get]
func (ctrl *shipmentController) get(c *gin.Context) {
	filter := shipmentRequest.Filter{}
	_ = c.ShouldBindUri(&filter)

	shipment, err := ctrl.getter.GetOne(c, filter.ID)
	if err != nil {
		_ = c.Error(err)
		return
	}

	c.JSON(200, gin.H{"data": shipment})
}

//	@Description	Update a shipment
//	@Accept			json
//	@Tags			Shipments
//	@Param			shipment_id	path	uint							true	"Shipment ID"
//	@Param			request		body	shipmentRequest.UpdateRequest	true	"Shipment update data"
//	@Success		204
//	@Failure		400	{object}	object{error=fplerror.errResponse}
//	@Failure		404	{object}	object{error=fplerror.errResponse}
//	@Failure		500	{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments/{shipment_id} [patch]
func Update(c *gin.Context) {
	filter := shipmentRequest.Filter{}
	if err := c.ShouldBindUri(&filter); err != nil {
		_ = c.Error(fplerror.ErrBadRequest.NewWithoutStack("invalid param"))
		return
	}

	req := shipmentRequest.UpdateRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		_ = c.Error(err)
		return
	}

	userInfo := c.MustGet(middlewareCfg.UserInfoKey).(auth.UserInfo)
	err := shipment.NewUpdater().Update(c, filter.ID, req, userInfo)
	if err != nil {
		_ = c.Error(err)
		return
	}

	c.AbortWithStatus(204)
}

//	@Description	List shipments with filtering, pagination and sorting
//	@Produce		json
//	@Tags			Shipments
//	@Param			mawb			query		string	false	"MAWB filter"
//	@Param			awb				query		string	false	"AWB filter"
//	@Param			status			query		string	false	"Status filter"
//	@Param			client_id		query		int		false	"Client ID filter"
//	@Param			tracking_id		query		string	false	"Tracking ID filter"
//	@Param			shipment_type	query		string	false	"Shipment type filter"
//	@Param			origin			query		string	false	"Origin filter"
//	@Param			destination		query		string	false	"Destination filter"
//	@Param			start			query		int		false	"Pagination start index"
//	@Param			query_limit		query		int		false	"Number of items to return"
//	@Param			sort			query		string	false	"Field(s) to sort by"
//	@Success		200				{object}	object{data=[]object,pagination=object{total=int}}
//	@Failure		400				{object}	object{error=fplerror.errResponse}
//	@Failure		500				{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments [get]
func List(c *gin.Context) {
	queryParams := &shipmentRequest.Filter{}
	_ = c.ShouldBindQuery(queryParams)

	shipments, err := shipmentHandler.NewListHandler().GetList(
		c,
		queryParams,
		httpmodels.LimitFilter(c.GetInt("query_limit")),
		httpmodels.StartFilter(c.GetInt("start")),
		httpmodels.SortFilter{
			Request: c.GetStringSlice("sort"),
			Default: shipmentConstants.DefaultSort,
		},
	)
	if err != nil {
		_ = c.Error(err)
		return
	}

	utils.GetListResponder().JSON(c, http.StatusOK, &shipments)
}

//	@Description	Refresh inbound data for a shipment
//	@Tags			Shipments
//	@Param			shipment_id	path	uint	true	"Shipment ID"
//	@Success		204
//	@Failure		400	{object}	object{error=fplerror.errResponse}
//	@Failure		404	{object}	object{error=fplerror.errResponse}
//	@Failure		500	{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments/{shipment_id}/inbound-refresh [post]
func InboundRefresh(c *gin.Context) {
	filter := shipmentRequest.Filter{}
	if err := c.ShouldBindUri(&filter); err != nil {
		_ = c.Error(err)
		return
	}

	if err := c.ShouldBind(&filter); err != nil {
		_ = c.Error(err)
		return
	}

	inboundRefresher := shipmentHandler.NewInboundRefresher()
	if err := inboundRefresher.Refresh(c, filter); err != nil {
		_ = c.Error(err)
		return
	}

	c.AbortWithStatus(204)
}

//	@Description	Delete a shipment
//	@Tags			Shipments
//	@Param			shipment_id	path	uint	true	"Shipment ID"
//	@Success		204
//	@Failure		400	{object}	object{error=fplerror.errResponse}
//	@Failure		404	{object}	object{error=fplerror.errResponse}
//	@Failure		500	{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments/{shipment_id} [delete]
func (ctrl *shipmentController) delete(c *gin.Context) {
	var resourceURI shipmentRequest.ResourceURI
	if err := c.ShouldBindUri(&resourceURI); err != nil {
		_ = c.Error(fplerror.ErrBadRequest.NewWithoutStack("Shipment ID should be numeric and greater than 0"))
		return
	}

	userInfo := c.MustGet(middlewareCfg.UserInfoKey).(auth.UserInfo)
	if err := ctrl.deleter.Delete(c, resourceURI.ID, userInfo); err != nil {
		_ = c.Error(err)
		return
	}

	c.AbortWithStatus(http.StatusNoContent)
}

//	@Description	Split a shipment into multiple shipments
//	@Accept			json
//	@Produce		json
//	@Tags			Shipments
//	@Param			shipment_id	path		uint							true	"Shipment ID"
//	@Param			request		body		shipmentRequest.SplitRequest	true	"Split request data"
//	@Success		201			{object}	object{data=object}
//	@Failure		400			{object}	object{error=fplerror.errResponse}
//	@Failure		404			{object}	object{error=fplerror.errResponse}
//	@Failure		500			{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments/{shipment_id}/split [post]
func (ctrl *shipmentController) split(c *gin.Context) {
	filter := shipmentRequest.Filter{}
	if err := c.ShouldBindUri(&filter); err != nil {
		_ = c.Error(fplerror.ErrBadRequest.NewWithoutStack("Shipment ID should be numeric and greater than 0"))
		return
	}

	req := shipmentRequest.SplitRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		_ = c.Error(err)
		return
	}

	shipment, err := ctrl.splitor.Split(c, filter.ID, req)
	if err != nil {
		_ = c.Error(err)
		return
	}

	c.JSON(201, gin.H{"data": shipment})
}

//	@Description	Move bags between shipments
//	@Accept			json
//	@Tags			Shipments
//	@Param			shipment_id	path	uint							true	"Source Shipment ID"
//	@Param			request		body	shipmentRequest.MoveBagsRequest	true	"Move bags request data"
//	@Success		204
//	@Failure		400	{object}	object{error=fplerror.errResponse}
//	@Failure		404	{object}	object{error=fplerror.errResponse}
//	@Failure		500	{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments/{shipment_id}/move_bag [post]
func (ctrl *shipmentController) moveBags(c *gin.Context) {
	filter := shipmentRequest.Filter{}
	if err := c.ShouldBindUri(&filter); err != nil {
		_ = c.Error(fplerror.ErrBadRequest.NewWithoutStack("shipment.id.invalid"))
		return
	}

	req := shipmentRequest.MoveBagsRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		_ = c.Error(err)
		return
	}

	userInfo := c.MustGet(middlewareCfg.UserInfoKey).(auth.UserInfo)
	err := ctrl.moveBagHandler.Move(c, filter.ID, req, userInfo)
	if err != nil {
		_ = c.Error(err)
		return
	}

	c.AbortWithStatus(http.StatusNoContent)
}

//	@Description	Get supporting documents for a shipment
//	@Produce		json
//	@Tags			Shipments
//	@Param			shipment_id	path		uint	true	"Shipment ID"
//	@Param			start		query		int		false	"Pagination start index"
//	@Param			limit		query		int		false	"Number of items to return"
//	@Success		200			{object}	object{data=[]object,pagination=object{total=int}}
//	@Failure		400			{object}	object{error=fplerror.errResponse}
//	@Failure		404			{object}	object{error=fplerror.errResponse}
//	@Failure		500			{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments/{shipment_id}/supporting-documents [get]
func (ctrl *shipmentController) getSupportingDocs(c *gin.Context) {
	req := shipmentRequest.DocumentRequest{}
	if err := c.ShouldBindUri(&req.Uri); err != nil {
		_ = c.Error(err)
		return
	}

	if err := c.ShouldBindQuery(&req.Filters); err != nil {
		_ = c.Error(err)
		return
	}

	if err := c.ShouldBindQuery(&req.Paging); err != nil {
		_ = c.Error(err)
		return
	}

	req.Paging.SetDefaultValues()

	docs, err := ctrl.documentHandler.GetByDocumentRequest(c, &req)

	if err != nil {
		_ = c.Error(err)
		return
	}

	c.Set("start", *req.Paging.Start)
	c.Set("limit", *req.Paging.Limit)

	utils.GetListResponder().JSON(c, http.StatusOK, &docs)
}

//	@Description	Create a MAWB document for a shipment
//	@Accept			json
//	@Tags			Shipments
//	@Param			shipment_id	path	uint	true	"Shipment ID"
//	@Param			request		body	object	true	"Document data"
//	@Success		200
//	@Failure		400	{object}	object{error=fplerror.errResponse}
//	@Failure		404	{object}	object{error=fplerror.errResponse}
//	@Failure		500	{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments/{shipment_id}/mawb-document [post]
func (ctrl *shipmentController) createDocument(c *gin.Context) {
	req := shipmentRequest.DocumentCreateRequest{}
	if err := c.ShouldBindUri(&req.Uri); err != nil {
		_ = c.Error(err)
		return
	}

	if err := c.ShouldBindJSON(&req.Payload); err != nil {
		_ = c.Error(err)
		return
	}

	userInfo := c.MustGet(middlewareCfg.UserInfoKey).(auth.UserInfo)
	if err := ctrl.documentCreateHandler.Create(c.Request.Context(), req, userInfo); err != nil {
		_ = c.Error(err)
		return
	}

	c.AbortWithStatus(http.StatusOK)
}

//	@Description	Get MAWB document for a shipment
//	@Produce		json
//	@Tags			Shipments
//	@Param			shipment_id	path		uint	true	"Shipment ID"
//	@Success		200			{object}	object{data=object}
//	@Failure		400			{object}	object{error=fplerror.errResponse}
//	@Failure		404			{object}	object{error=fplerror.errResponse}
//	@Failure		500			{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments/{shipment_id}/mawb-document [get]
func (ctrl *shipmentController) getDocument(c *gin.Context) {
	req := shipmentRequest.DocumentCreateRequest{}
	if err := c.ShouldBindUri(&req.Uri); err != nil {
		_ = c.Error(err)
		return
	}

	mawbDocuments, err := ctrl.documentGetHandler.Get(c, req.Uri.ShipmentID)
	if err != nil {
		_ = c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": mawbDocuments})
}

//	@Description	Delete MAWB document for a shipment
//	@Tags			Shipments
//	@Param			shipment_id	path	uint	true	"Shipment ID"
//	@Success		200
//	@Failure		400	{object}	object{error=fplerror.errResponse}
//	@Failure		404	{object}	object{error=fplerror.errResponse}
//	@Failure		500	{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments/{shipment_id}/mawb-document [delete]
func (ctrl *shipmentController) deleteDocument(c *gin.Context) {
	req := shipmentRequest.DocumentCreateRequest{}
	if err := c.ShouldBindUri(&req.Uri); err != nil {
		_ = c.Error(err)
		return
	}

	userInfo := c.MustGet(middlewareCfg.UserInfoKey).(auth.UserInfo)
	if err := ctrl.documentDeleteHandler.Delete(c.Request.Context(), req.Uri.ShipmentID, userInfo); err != nil {
		_ = c.Error(err)
		return
	}

	c.AbortWithStatus(http.StatusOK)
}

// @Description	Get Issues and Reasons for All Shipment Movement Statuses
// @Produce		json
// @Tags			Shipments
// @Success		200	{object}	object{data=object}
// @Failure		500	{object}	object{error=fplerror.errResponse}
// @Router			/api/v1/shipment-movement-statuses/issues-reasons [get]gen
func (ctrl *shipmentController) getListIssuesAndReasons(c *gin.Context) {
	issueAndReasons, err := ctrl.shipmentIrregularityGetHandler.GetListIssuesAndReasons(c.Request.Context())
	if err != nil {
		_ = c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": issueAndReasons})
}

//	@Description	Get list operational issues for a shipment
//	@Produce		json
//	@Tags			Shipments
//	@Param			shipment_id	path		uint	true	"Shipment ID""
//	@Success		200			{object}	object{data=object}
//	@Failure		500			{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments/{shipment_id}/irregularities [get]
func (ctrl *shipmentController) getListOperationalIssues(c *gin.Context) {
	var resourceURI shipmentRequest.ResourceURI
	if err := c.ShouldBindUri(&resourceURI); err != nil {
		_ = c.Error(fplerror.ErrBadRequest.NewWithoutStack("Shipment ID should be numeric and greater than 0"))
		return
	}

	operationalIssues, err := ctrl.shipmentIrregularityGetHandler.GetListOperationalIssuesByShipmentID(c.Request.Context(), uint(resourceURI.ID))
	if err != nil {
		_ = c.Error(err)
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": operationalIssues})
}

//	@Description	Create a shipment irregularity
//	@Accept			json
//	@Produce		json
//	@Tags			Shipments
//	@Param			shipment_id	path		uint	true	"Shipment ID"
//	@Param			request		body		object	true	"Irregularity data"
//	@Success		201			{object}	object{data=object}
//	@Failure		400			{object}	object{error=fplerror.errResponse}
//	@Failure		404			{object}	object{error=fplerror.errResponse}
//	@Failure		500			{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments/{shipment_id}/irregularities [post]
func (ctrl *shipmentController) createIrregularity(c *gin.Context) {
	var req shipment_irregularity.CreateRequest

	if err := c.ShouldBindUri(&req.Uri); err != nil {
		_ = c.Error(err)
		return
	}

	if err := c.ShouldBindJSON(&req.Payload); err != nil {
		_ = c.Error(err)
		return
	}

	userInfo := c.MustGet(middlewareCfg.UserInfoKey).(auth.UserInfo)
	irregularity, err := ctrl.shipmentIrregularityCreator.Create(c.Request.Context(), &req, userInfo)
	if err != nil {
		_ = c.Error(err)
		return
	}

	c.JSON(http.StatusCreated, gin.H{"data": irregularity})
}

//	@Description	Delete a shipment irregularity
//	@Tags			Shipments
//	@Param			shipment_id		path	uint	true	"Shipment ID"
//	@Param			irregularity_id	path	uint	true	"Irregularity ID"
//	@Success		204
//	@Failure		400	{object}	object{error=fplerror.errResponse}
//	@Failure		404	{object}	object{error=fplerror.errResponse}
//	@Failure		500	{object}	object{error=fplerror.errResponse}
//	@Router			/api/v1/shipments/{shipment_id}/irregularities/{irregularity_id} [delete]
func (ctrl *shipmentController) deleteIrregularity(c *gin.Context) {
	var req shipment_irregularity.DeleteRequest

	if err := c.ShouldBindUri(&req.Uri); err != nil {
		_ = c.Error(err)
		return
	}

	err := ctrl.shipmentIrregularityDeleter.Delete(c.Request.Context(), req.Uri.ShipmentID, req.Uri.IrregularityID)
	if err != nil {
		_ = c.Error(err)
		return
	}

	c.AbortWithStatus(http.StatusNoContent)
}
