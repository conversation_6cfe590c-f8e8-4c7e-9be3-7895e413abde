package shipment

import (
	"bytes"
	"database/sql"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"regexp"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/volatiletech/null/v8"

	"git.ninjavan.co/3pl/configs"
	middlewareCfg "git.ninjavan.co/3pl/configs/middleware"
	"git.ninjavan.co/3pl/errors/fplerror"
	mocks1 "git.ninjavan.co/3pl/handlers/shipment/mocks"
	mocks2 "git.ninjavan.co/3pl/handlers/shipment/mocks"
	update_interface "git.ninjavan.co/3pl/handlers/shipment/shipment_interface"
	shipmentRequest "git.ninjavan.co/3pl/httpmodels/shipment"
	"git.ninjavan.co/3pl/middleware"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/services/auth"
	"git.ninjavan.co/3pl/utils"
)

func TestRouteGroup(t *testing.T) {
	t.Parallel()
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	_, r := gin.CreateTestContext(w)

	api := r.Group(configs.InternalApiPathPrefix, middleware.Auth())

	RouteGroup(api)
}

func Test_shipmentController_create(t *testing.T) {
	type (
		vendorFindMock struct {
			query string
			rows  *sqlmock.Rows
			err   error
		}
		shipmentCreateMock struct {
			query string
			err   error
		}
		shipmentLogCreateMock struct {
			query string
			err   error
		}
		shipmentFindMock struct {
			query string
			rows  *sqlmock.Rows
		}
		shipmentEventCreateMock struct {
			query string
		}
		shipmentEventFindMock struct {
			query string
		}
		asyncRequestCreateMock struct {
			query string
			err   error
		}
		asyncRequestSelectMock struct {
			query string
			rows  *sqlmock.Rows
		}

		mocks struct {
			vendorFind          vendorFindMock
			shipmentFind        shipmentFindMock
			shipmentCreate      shipmentCreateMock
			shipmentEventCreate shipmentEventCreateMock
			shipmentEventFind   shipmentEventFindMock
			asyncRequestCreate  asyncRequestCreateMock
			shipmentLogCreate   shipmentLogCreateMock
			asyncRequestSelect  asyncRequestSelectMock
		}

		testCase struct {
			name    string
			payload string
			mock    mocks
			wantErr bool
		}
	)

	testCases := []testCase{
		{
			name:    "the payload missing fields: origin_port, destination_port, origin_country, destination_country",
			payload: `{"reference_id":"abc"}`,
			wantErr: true,
		},
		{
			name:    "cannot find vendor",
			payload: `{"vendor_id": 1,"origin_port": "abc", "origin_country": "vn", "destination_port": "xys", "destination_country": "sg"}`,
			mock: mocks{
				vendorFind: vendorFindMock{
					query: "SELECT `vendors`.* FROM `vendors` WHERE (`vendors`.`id` = ?) LIMIT 1",
					err:   sql.ErrNoRows,
					rows:  sqlmock.NewRows([]string{}),
				},
			},
			wantErr: true,
		},
		// {
		// 	name:    "",
		// 	payload: `{"vendor_id": 1,"origin_port": "abc", "origin_country": "vn", "destination_port": "xys", "destination_country": "sg"}`,
		// 	mock: mocks{
		// 		vendorFind: vendorFindMock{
		// 			query: "SELECT `vendors`.* FROM `vendors` WHERE (`vendors`.`id` = ?) LIMIT 1",
		// 			rows:  sqlmock.NewRows([]string{"id"}).AddRow(1),
		// 		},
		// 		shipmentCreate: shipmentCreateMock{
		// 			query: "INSERT INTO `shipments` (`type`,`reference_id`,`shipper_id`,`vendor_id`,`consignee_id`,`origin_port`,`destination_port`,`origin_country`,`destination_country`,`etd`,`eta`,`closed_at`,`actual_vendor_inbound_date`,`no_of_parcels_expected`,`no_of_parcels_received`,`goods_category`,`gross_weight`,`chargeable_weight`,`wh_gross_weight`,`wh_chargeable_weight`,`status`,`ready_to_assign`,`actual_shipment_weight`,`actual_shipment_cbm`,`metadata`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
		// 		},
		// 		shipmentFind: shipmentFindMock{
		// 			query: "SELECT `id`,`no_of_bags`,`no_of_parcels`,`no_of_inbounded_parcels`,`created_at`,`updated_at` FROM `shipments` WHERE `id`=?",
		// 			rows: sqlmock.NewRows([]string{"id", "no_of_bags", "no_of_parcels", "no_of_inbounded_parcels", "created_at", "updated_at"}).
		// 				AddRow(1, 0, 0, 0, time.Now(), time.Now()),
		// 		},
		// 		shipmentEventCreate: shipmentEventCreateMock{
		// 			query: "INSERT INTO `shipment_events` (`shipment_id`,`movement_status_id`,`event_time`,`created_at`,`updated_at`) VALUES (?,?,?,?,?)",
		// 		},
		// 		shipmentEventFind: shipmentEventFindMock{
		// 			query: "SELECT `id` FROM `shipment_events` WHERE `id`=?",
		// 		},
		// 		shipmentLogCreate: shipmentLogCreateMock{
		// 			query: "INSERT INTO `shipment_log_tab` (`shipment_id`,`log_type`,`origin_state`,`update_state`,`created_by`,`parent_id`,`created_at`) VALUES (?,?,?,?,?,?,?)",
		// 		},
		// 		asyncRequestCreate: asyncRequestCreateMock{
		// 			query: "INSERT INTO `async_requests` (`type`,`object_id`,`url`,`header`,`payload`) VALUES (?,?,?,?,?)",
		// 		},
		// 		asyncRequestSelect: asyncRequestSelectMock{
		// 			query: "SELECT `id`,`retries`,`status`,`created_at`,`updated_at` FROM `async_requests` WHERE `id`=?",
		// 			rows: sqlmock.NewRows([]string{"id", "retries", "status", "created_at", "updated_at"}).
		// 				AddRow(1, 0, 0, time.Now(), time.Now()),
		// 		},
		// 	},
		// 	wantErr: false,
		// },
	}

	gin.SetMode(gin.TestMode)
	db, dbMock := utils.StartMockDB(false)
	defer func() {
		_ = db.Close()
	}()

	for _, testCase := range testCases {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Set(middlewareCfg.UserInfoKey, auth.UserInfo{})

		c.Request, _ = http.NewRequest("", "", ioutil.NopCloser(bytes.NewBufferString(testCase.payload)))

		if testCase.mock.vendorFind.query != "" {
			dbMock.ExpectQuery(regexp.QuoteMeta(testCase.mock.vendorFind.query)).
				WillReturnError(testCase.mock.vendorFind.err).
				WillReturnRows(testCase.mock.vendorFind.rows)
		}

		if testCase.mock.shipmentCreate.query != "" {
			dbMock.ExpectBegin()
			dbMock.ExpectExec(regexp.QuoteMeta(testCase.mock.shipmentCreate.query)).
				WillReturnError(testCase.mock.shipmentCreate.err).
				WillReturnResult(sqlmock.NewResult(1, 1))
		}

		if testCase.mock.shipmentLogCreate.query != "" {
			dbMock.ExpectExec(regexp.QuoteMeta(testCase.mock.shipmentLogCreate.query)).
				WillReturnError(testCase.mock.shipmentLogCreate.err).
				WillReturnResult(sqlmock.NewResult(1, 1))
		}

		if testCase.mock.shipmentFind.query != "" {
			dbMock.ExpectQuery(regexp.QuoteMeta(testCase.mock.shipmentFind.query)).
				WillReturnRows(testCase.mock.shipmentFind.rows)

			dbMock.ExpectExec(regexp.QuoteMeta(testCase.mock.shipmentEventCreate.query)).
				WithArgs(1, 7, nil, utils.AnyTime{}, utils.AnyTime{}).
				WillReturnResult(sqlmock.NewResult(7, 1))

			for i := 0; i < 6; i++ {
				if i == 1 {

					dbMock.ExpectExec(regexp.QuoteMeta(testCase.mock.shipmentEventCreate.query)).
						WithArgs(1, 9, nil, utils.AnyTime{}, utils.AnyTime{}).
						WillReturnResult(sqlmock.NewResult(9, 1))
				}

				if i == 4 {

					dbMock.ExpectExec(regexp.QuoteMeta(testCase.mock.shipmentEventCreate.query)).
						WithArgs(1, 10, nil, utils.AnyTime{}, utils.AnyTime{}).
						WillReturnResult(sqlmock.NewResult(10, 1))
				}

				newID := int64(i + 1)
				dbMock.ExpectExec(regexp.QuoteMeta(testCase.mock.shipmentEventCreate.query)).
					WithArgs(1, newID, nil, utils.AnyTime{}, utils.AnyTime{}).
					WillReturnResult(sqlmock.NewResult(newID, 1))
			}

			dbMock.ExpectExec(regexp.QuoteMeta(testCase.mock.shipmentEventCreate.query)).
				WithArgs(1, 8, nil, utils.AnyTime{}, utils.AnyTime{}).
				WillReturnResult(sqlmock.NewResult(8, 1))

			dbMock.ExpectExec(regexp.QuoteMeta(testCase.mock.shipmentEventCreate.query)).
				WithArgs(1, 11, nil, utils.AnyTime{}, utils.AnyTime{}).
				WillReturnResult(sqlmock.NewResult(11, 1))

			dbMock.ExpectExec(regexp.QuoteMeta(testCase.mock.shipmentEventCreate.query)).
				WithArgs(1, 12, nil, utils.AnyTime{}, utils.AnyTime{}).
				WillReturnResult(sqlmock.NewResult(12, 1))
		}

		if testCase.mock.asyncRequestCreate.query != "" {
			dbMock.ExpectExec(regexp.QuoteMeta(testCase.mock.asyncRequestCreate.query)).
				WillReturnError(testCase.mock.asyncRequestCreate.err).
				WillReturnResult(sqlmock.NewResult(1, 1))
		}

		if testCase.mock.asyncRequestSelect.query != "" {
			dbMock.ExpectQuery(regexp.QuoteMeta(testCase.mock.asyncRequestSelect.query)).
				WillReturnRows(testCase.mock.asyncRequestSelect.rows)
		}

		if !testCase.wantErr {
			dbMock.ExpectCommit()
		}

		Create(c)

		if c.Errors.Last() != nil && !testCase.wantErr {
			t.Errorf("expected err = nil, but got %v in %v", c.Errors.Last(), testCase.name)
		}

		assert.Equal(t, nil, dbMock.ExpectationsWereMet(), testCase.name+" failed")
	}
}

func Test_shipmentController_get(t *testing.T) {
	t.Parallel()
	type (
		mockGetOne struct {
			shipment *repo_interface.DetailedShipment
			err      error
		}

		testCase struct {
			name       string
			expected   error
			request    shipmentRequest.Filter
			mockGetOne mockGetOne
		}
	)

	testCases := []testCase{
		{
			name:     "test_1",
			expected: nil,
			request:  shipmentRequest.Filter{ID: 1},
			mockGetOne: mockGetOne{
				shipment: &repo_interface.DetailedShipment{ID: 1},
				err:      nil,
			},
		},
		{
			name: "test_2",
			expected: &gin.Error{
				Err:  fplerror.ErrEntityNotFound.NewWithoutStack("shipment.id.not_found", uint(2)),
				Type: gin.ErrorTypePrivate,
			},
			request: shipmentRequest.Filter{ID: 2},
			mockGetOne: mockGetOne{
				shipment: nil,
				err:      fplerror.ErrEntityNotFound.NewWithoutStack("shipment.id.not_found", uint(2)),
			},
		},
	}

	gin.SetMode(gin.TestMode)

	for _, testCase := range testCases {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		c.Params = []gin.Param{
			{
				Key:   "shipment_id",
				Value: fmt.Sprintf("%d", testCase.request.ID),
			},
		}

		handlerMock := mocks2.NewMockDetailsGettable(gomock.NewController(t))
		handlerMock.EXPECT().GetOne(
			c,
			testCase.request.ID,
		).Return(testCase.mockGetOne.shipment, testCase.mockGetOne.err)

		ctrl := shipmentController{
			getter: handlerMock,
		}
		ctrl.get(c)

		var actualErr error

		if len(c.Errors) > 0 {
			actualErr = c.Errors[0]
		} else {
			actualErr = nil
		}

		assert.Equal(t, testCase.expected, actualErr, testCase.name+" failed")
	}
}

func Test_shipmentController_update(t *testing.T) {
	type (
		shipmentFindMock struct {
			query string
			args  []driver.Value
			rows  *sqlmock.Rows
		}
		shipmentUpdateMock struct {
			query string
		}
		shipmentLogCreateMock struct {
			query string
		}
		mocks struct {
			shipmentFind       shipmentFindMock
			shipmentUpdate     shipmentUpdateMock
			shipmentLogCreate  shipmentLogCreateMock
			asyncRequestQuery  string
			asyncRequestUpdate string
		}

		testCase struct {
			name    string
			context *gin.Context
			mock    mocks
			wantErr bool
		}
	)

	contextWithParams := func(params gin.Params, payload string) *gin.Context {
		resp := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(resp)
		c.Request, _ = http.NewRequest("", "", strings.NewReader(payload))
		c.Params = params
		c.Set(middlewareCfg.UserInfoKey, auth.UserInfo{})
		return c
	}

	testCases := []testCase{
		{
			name: "invalid URL",
			context: contextWithParams(gin.Params{
				{Key: "shipment_id", Value: "abc"},
			}, `{"origin_port"`),
			wantErr: true,
		},
		{
			name: "invalid payload",
			context: contextWithParams(gin.Params{
				{Key: "shipment_id", Value: "1"},
			}, `{"origin_port"`),
			wantErr: true,
		},
		{
			name: "cannot update because the connection already closed",
			context: contextWithParams(gin.Params{
				{Key: "shipment_id", Value: "1"},
			}, `{"origin_port": "SGG"}`),
			wantErr: true,
		},
		// {
		// 	name: "update shipment successfully",
		// 	context: contextWithParams(gin.Params{
		// 		{Key: "shipment_id", Value: "123"},
		// 	}, `{"origin_port": "SGG"}`),
		// 	mock: mocks{
		// 		shipmentFind: shipmentFindMock{
		// 			query: "select * from `shipments` where `id`=?",
		// 			args:  []driver.Value{123},
		// 			rows:  sqlmock.NewRows([]string{"id", "origin_country"}).AddRow(123, "VN"),
		// 		},
		// 		shipmentUpdate: shipmentUpdateMock{
		// 			query: "UPDATE `shipments` SET `origin_port`=? WHERE `id`=?",
		// 		},
		// 		shipmentLogCreate: shipmentLogCreateMock{
		// 			query: "INSERT INTO `shipment_log_tab` (`shipment_id`,`log_type`,`created_by`) VALUES (?,?,?)",
		// 		},
		// 		asyncRequestQuery:  "SELECT `async_requests`.* FROM `async_requests` WHERE (`async_requests`.`type` = ?) AND (`async_requests`.`object_id` = ?) LIMIT 1;",
		// 		asyncRequestUpdate: "UPDATE `async_requests` SET `payload`=?,`retries`=?,`status`=?,`response`=? WHERE `id`=?",
		// 	},
		// 	wantErr: false,
		// },
	}

	gin.SetMode(gin.TestMode)
	db, dbMock := utils.StartMockDB(false)
	defer func() {
		_ = db.Close()
	}()

	for _, testCase := range testCases {
		if testCase.mock.shipmentFind.query != "" {
			dbMock.ExpectQuery(regexp.QuoteMeta(testCase.mock.shipmentFind.query)).
				WithArgs(testCase.mock.shipmentFind.args...).
				WillReturnRows(testCase.mock.shipmentFind.rows)
		}

		if testCase.mock.shipmentUpdate.query != "" {
			dbMock.ExpectBegin()
			dbMock.ExpectExec(regexp.QuoteMeta(testCase.mock.shipmentUpdate.query)).
				WillReturnResult(driver.RowsAffected(1))
		}

		if testCase.mock.shipmentLogCreate.query != "" {
			dbMock.ExpectExec(regexp.QuoteMeta(testCase.mock.shipmentLogCreate.query)).
				WillReturnResult(driver.RowsAffected(1))
			dbMock.ExpectCommit()
		}

		if testCase.mock.asyncRequestQuery != "" {
			dbMock.ExpectQuery(regexp.QuoteMeta(testCase.mock.asyncRequestQuery)).
				WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))
			dbMock.ExpectExec(regexp.QuoteMeta(testCase.mock.asyncRequestUpdate)).WillReturnResult(driver.RowsAffected(1))
		}

		Update(testCase.context)

		if testCase.context.Errors.Last() != nil && !testCase.wantErr {
			t.Errorf("expected err = nil, but got %v", testCase.context.Errors.Last())
		}

		if testCase.mock.shipmentFind.query != "" {
			assert.Equal(t, nil, dbMock.ExpectationsWereMet(), testCase.name+" failed")
		}
	}
}

func Test_list(t *testing.T) {
	type args struct {
		c *gin.Context
	}

	resp := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(resp)
	c.Request, _ = http.NewRequest("", "", strings.NewReader(string(``)))
	tests := []struct {
		name         string
		args         args
		expectResult bool
	}{
		{
			"get list successfully",
			args{
				c,
			},
			true,
		},
		{
			"failed",
			args{
				c,
			},
			false,
		},
	}

	expQuery := "SELECT shipments.*, shippers.remote_id AS shipper_remote_id, shippers.remote_legacy_id AS shipper_remote_legacy_id, shippers.name AS shipper_name, shippers.country AS shipper_country, shippers.type AS shipper_type, shippers.created_at AS shipper_created_at, shippers.updated_at AS shipper_updated_at, vendors.name AS vendor_name, vendors.country AS vendor_country, vendors.code AS vendor_code, vendors.handler AS vendor_handler, vendors.created_at AS vendor_created_at, vendors.updated_at AS vendor_updated_at, consignees.name AS consignee_name, consignees.country AS consignee_country, consignees.code AS consignee_code, consignees.handler AS consignee_handler, consignees.created_at AS consignee_created_at, consignees.updated_at AS consignee_updated_at, shipment_movement_statuses.name AS current_movement_status_name FROM `shipments` INNER JOIN shipments AS s2 ON s2.id = shipments.id LEFT JOIN shippers ON shipments.shipper_id = shippers.id LEFT JOIN vendors ON shipments.vendor_id = vendors.id LEFT JOIN vendors consignees ON shipments.consignee_id = consignees.id LEFT JOIN shipment_events se ON se.id = shipments.current_event_id LEFT JOIN shipment_movement_statuses ON shipment_movement_statuses.id = se.movement_status_id ORDER BY id DESC LIMIT 0;"
	expShipmentEventsQuery := "SELECT `shipment_events`.* FROM `shipment_events` WHERE (`shipment_events`.`shipment_id` IN (?));"
	gin.SetMode(gin.TestMode)
	db, dbMock := utils.StartMockDB(false)
	defer func() {
		_ = db.Close()
	}()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			expectedQuery := dbMock.
				ExpectQuery(regexp.QuoteMeta(expQuery))
			if tt.expectResult {
				expectedQuery.WillReturnRows(dbMock.NewRows([]string{"id"}).AddRow(1))

				dbMock.ExpectQuery(regexp.QuoteMeta(expShipmentEventsQuery)).WillReturnRows(dbMock.NewRows([]string{"id"}).AddRow(1))
			} else {
				expectedQuery.WillReturnError(sql.ErrConnDone)
			}

			List(tt.args.c)
			assert.Equal(t, nil, dbMock.ExpectationsWereMet(), " failed")
		})
	}
}

func Test_inboundRefresh(t *testing.T) {
	t.Parallel()
	type args struct {
		params gin.Params
	}

	tests := []struct {
		name              string
		args              args
		mockDBFunc        func(sqlmock.Sqlmock)
		selectShipmentErr error
		wantErr           bool
	}{
		{
			name:    "couldn't bind URI",
			args:    args{},
			wantErr: true,
		},
		{
			name: "inbounded refresher has processed unsuccessfully",
			args: args{
				params: gin.Params{
					{Key: "shipment_id", Value: "123"},
				},
			},
			wantErr: true,
		},
	}

	gin.SetMode(gin.TestMode)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(resp)
			c.Request, _ = http.NewRequest("", "", strings.NewReader(string(``)))
			c.Params = tt.args.params

			InboundRefresh(c)

			if !tt.wantErr && len(c.Errors) > 0 {
				t.Errorf("expected no error but got %v", c.Errors.Last())
			}
		})
	}
}

func Test_shipmentController_delete(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name       string
		shipmentID string
		deleteErr  error
		expectErr  bool
	}{
		{
			name:       "ID contains characters",
			shipmentID: "123abc",
			expectErr:  true,
		},
		{
			name:       "ID is 0",
			shipmentID: "0",
			expectErr:  true,
		},
		{
			name:       "delete got error",
			shipmentID: "123",
			deleteErr:  errors.New("db error"),
			expectErr:  true,
		},
		{
			name:       "delete successful",
			shipmentID: "123",
			expectErr:  false,
		},
	}

	mockCtrl := gomock.NewController(t)
	defer func() {
		mockCtrl.Finish()
	}()
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, _ := gin.CreateTestContext(w)
			c.Params = gin.Params{
				{
					Key:   "shipment_id",
					Value: tt.shipmentID,
				},
			}
			c.Set(middlewareCfg.UserInfoKey, auth.UserInfo{})

			mockDeleteHandler := mocks1.NewMockDeleter(mockCtrl)
			if tt.expectErr == (tt.deleteErr != nil) {
				mockDeleteHandler.EXPECT().Delete(c, gomock.Any(), gomock.Any()).Return(tt.deleteErr)
			}

			testShipmentController := &shipmentController{
				deleter: mockDeleteHandler,
			}

			testShipmentController.delete(c)

			if tt.expectErr {
				assert.NotNil(t, c.Errors.Last(), "want error but got none")
			} else {
				assert.Nil(t, c.Errors.Last(), "got unexpected error %v", c.Errors.Last())
			}
		})
	}
}

func Test_shipmentController_split(t *testing.T) {
	t.Parallel()
	type (
		input struct {
			shipmentID string
			payload    string
		}
		fields struct {
			splitor update_interface.Splitor
		}
	)

	mockCtrl := gomock.NewController(t)
	defer func() {
		mockCtrl.Finish()
	}()
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()

	tests := []struct {
		name    string
		input   input
		fields  fields
		wantErr bool
	}{
		{
			name: "ID contains characters",
			input: input{
				shipmentID: "123abc",
			},
			wantErr: true,
		},
		{
			name: "ID is 0",
			input: input{
				shipmentID: "0",
			},
			wantErr: true,
		},
		{
			name: "payload invalid",
			input: input{
				shipmentID: "123",
				payload: `
				{
					"no_of_parcels": 0
				}`,
			},
			wantErr: true,
		},
		{
			name: "split got error",
			input: input{
				shipmentID: "123",
				payload: `
				{
					"no_of_parcels": 1,
					"reference_id": "reference_id",
					"chargeable_weight": 1.0,
					"metadata": "{}"
				}`,
			},
			fields: fields{
				splitor: func() update_interface.Splitor {
					r := mocks1.NewMockSplitor(mockCtrl)
					r.EXPECT().Split(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("mock error"))

					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "split successful",
			input: input{
				shipmentID: "123",
				payload: `
				{
					"no_of_parcels": 1,
					"reference_id": "reference_id",
					"chargeable_weight": 1.0,
					"metadata": "{}"
				}`,
			},
			fields: fields{
				splitor: func() update_interface.Splitor {
					r := mocks1.NewMockSplitor(mockCtrl)
					r.EXPECT().Split(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Shipment{ID: 1}, nil)

					return r
				}(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, _ := gin.CreateTestContext(w)
			c.Params = gin.Params{
				{
					Key:   "shipment_id",
					Value: tt.input.shipmentID,
				},
			}
			c.Request, _ = http.NewRequest("POST", "", strings.NewReader(tt.input.payload))

			testShipmentController := &shipmentController{
				splitor: tt.fields.splitor,
			}
			testShipmentController.split(c)

			if tt.wantErr {
				assert.NotNil(t, c.Errors.Last(), "want error but got none")
			} else {
				assert.Nil(t, c.Errors.Last(), "got unexpected error %v", c.Errors.Last())
			}
		})
	}
}

func Test_shipmentController_moveBags(t *testing.T) {
	t.Parallel()
	type (
		input struct {
			shipmentID string
			payload    string
		}
		fields struct {
			moveBagHandler update_interface.MoveBagHandler
		}
	)

	mockCtrl := gomock.NewController(t)
	defer func() {
		mockCtrl.Finish()
	}()
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()

	tests := []struct {
		name    string
		input   input
		fields  fields
		wantErr bool
	}{
		{
			name: "ID contains characters",
			input: input{
				shipmentID: "123abc",
			},
			wantErr: true,
		},
		{
			name: "ID is 0",
			input: input{
				shipmentID: "0",
			},
			wantErr: true,
		},
		{
			name: "payload invalid",
			input: input{
				shipmentID: "1",
				payload:    `{}`,
			},
			wantErr: true,
		},
		{
			name: "move bags got error",
			input: input{
				shipmentID: "1",
				payload: `
				{
					"second_shipment_id": 2,
					"shipment_parcel_ids": [1]
				}`,
			},
			fields: fields{
				moveBagHandler: func() update_interface.MoveBagHandler {
					r := mocks1.NewMockMoveBagHandler(mockCtrl)
					r.EXPECT().Move(gomock.Any(), uint(1), shipmentRequest.MoveBagsRequest{
						SecondShipmentID:  2,
						ShipmentParcelIDs: []uint{1},
					}, auth.UserInfo{}).Return(errors.New("mock error"))

					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "split successful",
			input: input{
				shipmentID: "1",
				payload: `
				{
					"second_shipment_id": 2,
					"shipment_parcel_ids": [1]
				}`,
			},
			fields: fields{
				moveBagHandler: func() update_interface.MoveBagHandler {
					r := mocks1.NewMockMoveBagHandler(mockCtrl)
					r.EXPECT().Move(gomock.Any(), uint(1), shipmentRequest.MoveBagsRequest{
						SecondShipmentID:  2,
						ShipmentParcelIDs: []uint{1},
					}, auth.UserInfo{}).Return(nil)

					return r
				}(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, _ := gin.CreateTestContext(w)
			c.Params = gin.Params{
				{
					Key:   "shipment_id",
					Value: tt.input.shipmentID,
				},
			}
			c.Set(middlewareCfg.UserInfoKey, auth.UserInfo{})
			c.Request, _ = http.NewRequest("POST", "", strings.NewReader(tt.input.payload))

			testShipmentController := &shipmentController{
				moveBagHandler: tt.fields.moveBagHandler,
			}
			testShipmentController.moveBags(c)

			if tt.wantErr {
				assert.NotNil(t, c.Errors.Last(), "want error but got none")
			} else {
				assert.Nil(t, c.Errors.Last(), "got unexpected error %v", c.Errors.Last())
			}
		})
	}
}

func Test_shipmentController_getSupportingDocs(t *testing.T) {
	t.Parallel()
	type fields struct {
		documentHandler update_interface.DocumentHandler
	}
	type request struct {
		uri         int
		queryString string
	}
	type resp struct {
		Data   []update_interface.DownloadableDocument `json:"data"`
		Paging map[string]bool                         `json:"paging"`
	}
	type want struct {
		code int
		resp resp
	}

	gin.SetMode(gin.TestMode)

	ctrl := gomock.NewController(t)

	tests := []struct {
		name    string
		request request
		fields  fields
		want    want
		wantErr bool
	}{
		{
			name: "failed to bind uri",
			request: request{
				uri: 0,
			},
			wantErr: true,
		},
		{
			name: "failed to bind filters",
			request: request{
				uri:         1,
				queryString: "?type=abc",
			},
			wantErr: true,
		},
		{
			name: "failed to bind paging",
			request: request{
				uri:         1,
				queryString: "?limit=1001",
			},
			wantErr: true,
		},
		{
			name: "failed to get supporting docs due to db error",
			request: request{
				uri:         1,
				queryString: "?limit=246",
			},
			fields: fields{
				documentHandler: func() update_interface.DocumentHandler {
					m := mocks2.NewMockDocumentHandler(ctrl)
					m.EXPECT().GetByDocumentRequest(gomock.Any(), gomock.Any()).Return(nil, sql.ErrConnDone)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "happy case",
			request: request{
				uri:         1,
				queryString: "?start=0&limit=100",
			},
			fields: fields{
				documentHandler: func() update_interface.DocumentHandler {
					m := mocks2.NewMockDocumentHandler(ctrl)
					m.EXPECT().GetByDocumentRequest(gomock.Any(), gomock.Any()).Return([]update_interface.DownloadableDocument{
						{
							FileName: "abc.xyz",
							FileURL:  "https://abc.xyz",
							Type:     1,
						},
					}, nil)
					return m
				}(),
			},
			want: want{
				code: http.StatusOK,
				resp: resp{
					Data: []update_interface.DownloadableDocument{{
						FileName: "abc.xyz",
						FileURL:  "https://abc.xyz",
						Type:     1,
					}},
					Paging: utils.NewPager(100, 0, 1).GeneratePath(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			c.Params = []gin.Param{
				{
					Key:   "shipment_id",
					Value: fmt.Sprintf("%d", tt.request.uri),
				},
			}

			c.Request, _ = http.NewRequest("GET", "/api/v1/shipment_id/supporting-documents"+tt.request.queryString, nil)

			ctrl := &shipmentController{
				documentHandler: tt.fields.documentHandler,
			}

			ctrl.getSupportingDocs(c)

			if tt.wantErr {
				assert.NotNilf(t, c.Errors.Last(), "expect get supporting docs return error but got nil")
			} else {
				assert.Nilf(t, c.Errors.Last(), "expect get supporting docs success but got error")
				assert.Equal(t, tt.want.code, w.Code)
				want, _ := json.Marshal(tt.want.resp)
				assert.Equal(t, want, w.Body.Bytes(), "expect get supporting docs return json but miss match, want %v, got %s", string(want), w.Body.String())
			}
		})
	}
}

func Test_shipmentController_createDocument(t *testing.T) {
	t.Parallel()
	type (
		args struct {
			shipmentID string
			payload    string
		}
		fields struct {
			documentCreateHandler update_interface.DocumentCreateHandler
		}
	)

	mockCtrl := gomock.NewController(t)
	defer func() {
		mockCtrl.Finish()
	}()
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()

	tests := []struct {
		name    string
		args    args
		fields  fields
		wantErr bool
	}{
		{
			name: "ID contains characters",
			args: args{
				shipmentID: "123abc",
			},
			wantErr: true,
		},
		{
			name: "ID is 0",
			args: args{
				shipmentID: "0",
			},
			wantErr: true,
		},
		{
			name: "payload invalid",
			args: args{
				shipmentID: "1",
				payload: `{
					"transport_service": 3
				}`,
			},
			wantErr: true,
		},
		{
			name: "create document got error",
			args: args{
				shipmentID: "1",
				payload: `
				{
					"name": "mawb.pdf",
					"uri": "gs://nv-qa-services/3pl/commercial-invoices/mawb.pdf"
				}`,
			},
			fields: fields{
				documentCreateHandler: func() update_interface.DocumentCreateHandler {
					r := mocks1.NewMockDocumentCreateHandler(mockCtrl)
					r.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("create error"))

					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "create document successful",
			args: args{
				shipmentID: "1",
				payload: `
				{
					"name": "mawb.pdf",
					"uri": "gs://nv-qa-services/3pl/commercial-invoices/mawb.pdf"
				}`,
			},
			fields: fields{
				documentCreateHandler: func() update_interface.DocumentCreateHandler {
					r := mocks1.NewMockDocumentCreateHandler(mockCtrl)
					r.EXPECT().Create(gomock.Any(), shipmentRequest.DocumentCreateRequest{
						Uri: shipmentRequest.DocumentCreateURI{
							ShipmentID: 1,
						},
						Payload: shipmentRequest.DocumentCreatePayload{
							Name: null.StringFrom("mawb.pdf").Ptr(),
							URI:  null.StringFrom("gs://nv-qa-services/3pl/commercial-invoices/mawb.pdf").Ptr(),
						},
					}, gomock.Any()).Return(nil)

					return r
				}(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, _ := gin.CreateTestContext(w)
			c.Params = gin.Params{
				{
					Key:   "shipment_id",
					Value: tt.args.shipmentID,
				},
			}
			c.Set(middlewareCfg.UserInfoKey, auth.UserInfo{})
			c.Request, _ = http.NewRequest("POST", "", strings.NewReader(tt.args.payload))

			testShipmentController := &shipmentController{
				documentCreateHandler: tt.fields.documentCreateHandler,
			}
			testShipmentController.createDocument(c)

			if tt.wantErr {
				assert.NotNil(t, c.Errors.Last(), "want error but got none")
			} else {
				assert.Nil(t, c.Errors.Last(), "got unexpected error %v", c.Errors.Last())
			}
		})
	}
}

func Test_shipmentController_getDocument(t *testing.T) {
	t.Parallel()
	type (
		args struct {
			shipmentID string
		}
		fields struct {
			documentGetHandler update_interface.DocumentGetHandler
		}
	)

	mockCtrl := gomock.NewController(t)
	defer func() {
		mockCtrl.Finish()
	}()
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()

	tests := []struct {
		name    string
		args    args
		fields  fields
		wantErr bool
	}{
		{
			name: "ID contains characters",
			args: args{
				shipmentID: "123abc",
			},
			wantErr: true,
		},
		{
			name: "ID is 0",
			args: args{
				shipmentID: "0",
			},
			wantErr: true,
		},
		{
			name: "get document got error",
			args: args{
				shipmentID: "1",
			},
			fields: fields{
				documentGetHandler: func() update_interface.DocumentGetHandler {
					r := mocks1.NewMockDocumentGetHandler(mockCtrl)
					r.EXPECT().Get(gomock.Any(), gomock.Any()).Return(nil, errors.New("get error"))

					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "get document successful",
			args: args{
				shipmentID: "1",
			},
			fields: fields{
				documentGetHandler: func() update_interface.DocumentGetHandler {
					r := mocks1.NewMockDocumentGetHandler(mockCtrl)
					r.EXPECT().Get(gomock.Any(), uint(1)).Return([]*update_interface.MawbDocument{}, nil)

					return r
				}(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, _ := gin.CreateTestContext(w)
			c.Params = gin.Params{
				{
					Key:   "shipment_id",
					Value: tt.args.shipmentID,
				},
			}

			testShipmentController := &shipmentController{
				documentGetHandler: tt.fields.documentGetHandler,
			}
			testShipmentController.getDocument(c)

			if tt.wantErr {
				assert.NotNil(t, c.Errors.Last(), "want error but got none")
			} else {
				assert.Nil(t, c.Errors.Last(), "got unexpected error %v", c.Errors.Last())
			}
		})
	}
}

func Test_shipmentController_deleteDocument(t *testing.T) {
	t.Parallel()
	type (
		args struct {
			shipmentID string
		}
		fields struct {
			documentDeleteHandler update_interface.DocumentDeleteHandler
		}
	)

	mockCtrl := gomock.NewController(t)
	defer func() {
		mockCtrl.Finish()
	}()
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()

	tests := []struct {
		name    string
		args    args
		fields  fields
		wantErr bool
	}{
		{
			name: "ID contains characters",
			args: args{
				shipmentID: "123abc",
			},
			wantErr: true,
		},
		{
			name: "ID is 0",
			args: args{
				shipmentID: "0",
			},
			wantErr: true,
		},
		{
			name: "delete document got error",
			args: args{
				shipmentID: "1",
			},
			fields: fields{
				documentDeleteHandler: func() update_interface.DocumentDeleteHandler {
					r := mocks1.NewMockDocumentDeleteHandler(mockCtrl)
					r.EXPECT().Delete(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("delete error"))

					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "delete document successful",
			args: args{
				shipmentID: "1",
			},
			fields: fields{
				documentDeleteHandler: func() update_interface.DocumentDeleteHandler {
					r := mocks1.NewMockDocumentDeleteHandler(mockCtrl)
					r.EXPECT().Delete(gomock.Any(), uint(1), gomock.Any()).Return(nil)

					return r
				}(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, _ := gin.CreateTestContext(w)
			c.Params = gin.Params{
				{
					Key:   "shipment_id",
					Value: tt.args.shipmentID,
				},
			}
			c.Set(middlewareCfg.UserInfoKey, auth.UserInfo{})
			c.Request, _ = http.NewRequest("DELETE", "", nil)

			testShipmentController := &shipmentController{
				documentDeleteHandler: tt.fields.documentDeleteHandler,
			}
			testShipmentController.deleteDocument(c)

			if tt.wantErr {
				assert.NotNil(t, c.Errors.Last(), "want error but got none")
			} else {
				assert.Nil(t, c.Errors.Last(), "got unexpected error %v", c.Errors.Last())
			}
		})
	}
}

func Test_shipmentController_getListIssuesAndReasons(t *testing.T) {
	t.Parallel()
	type (
		fields struct {
			shipmentIrregularityGetHandler update_interface.ShipmentIrregularityGetHandler
		}
	)

	mockCtrl := gomock.NewController(t)
	defer func() {
		mockCtrl.Finish()
	}()
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()

	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "get issues and reasons got error",
			fields: fields{
				shipmentIrregularityGetHandler: func() update_interface.ShipmentIrregularityGetHandler {
					r := mocks1.NewMockShipmentIrregularityGetHandler(mockCtrl)
					r.EXPECT().GetListIssuesAndReasons(gomock.Any()).Return(nil, errors.New("get error"))

					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "get issues and reasons successful",
			fields: fields{
				shipmentIrregularityGetHandler: func() update_interface.ShipmentIrregularityGetHandler {
					r := mocks1.NewMockShipmentIrregularityGetHandler(mockCtrl)
					r.EXPECT().GetListIssuesAndReasons(gomock.Any()).Return(repo_interface.IssueAndReasonSlice{}, nil)

					return r
				}(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c, _ := gin.CreateTestContext(w)
			c.Request, _ = http.NewRequest("GET", "", nil)

			testShipmentController := &shipmentController{
				shipmentIrregularityGetHandler: tt.fields.shipmentIrregularityGetHandler,
			}
			testShipmentController.getListIssuesAndReasons(c)

			if tt.wantErr {
				assert.NotNil(t, c.Errors.Last(), "want error but got none")
			} else {
				assert.Nil(t, c.Errors.Last(), "got unexpected error %v", c.Errors.Last())
			}
		})
	}
}

func Test_shipmentController_getListOperationalIssues(t *testing.T) {
	t.Parallel()

	type (
		args struct {
			shipmentID string
		}
		fields struct {
			shipmentIrregularityGetHandler update_interface.ShipmentIrregularityGetHandler
		}
	)

	tests := []struct {
		name   string
		args   args
		fields func(ctrl *gomock.Controller) fields
		errStr string
	}{
		{
			name: "ID contains characters",
			args: args{
				shipmentID: "123abc",
			},
			fields: func(ctrl *gomock.Controller) fields {
				return fields{}
			},
			errStr: "Shipment ID should be numeric and greater than 0",
		},
		{
			name: "ID is 0",
			args: args{
				shipmentID: "0",
			},
			fields: func(ctrl *gomock.Controller) fields {
				return fields{}
			},
			errStr: "Shipment ID should be numeric and greater than 0",
		},
		{
			name: "get shipment operational issues got error",
			args: args{
				shipmentID: "1",
			},
			fields: func(mockCtrl *gomock.Controller) fields {
				return fields{
					shipmentIrregularityGetHandler: func() update_interface.ShipmentIrregularityGetHandler {
						r := mocks1.NewMockShipmentIrregularityGetHandler(mockCtrl)
						r.EXPECT().GetListOperationalIssuesByShipmentID(gomock.Any(), gomock.Any()).Return(nil, errors.New("get error"))

						return r
					}(),
				}
			},
			errStr: "get error",
		},
		{
			name: "get shipment operational issues successful",
			args: args{
				shipmentID: "1",
			},
			fields: func(mockCtrl *gomock.Controller) fields {
				return fields{
					shipmentIrregularityGetHandler: func() update_interface.ShipmentIrregularityGetHandler {
						r := mocks1.NewMockShipmentIrregularityGetHandler(mockCtrl)
						r.EXPECT().GetListOperationalIssuesByShipmentID(gomock.Any(), uint(1)).Return(repo_interface.OperationalIssueSlice{}, nil)

						return r
					}(),
				}
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockCtrl := gomock.NewController(t)
			defer func() {
				mockCtrl.Finish()
			}()
			gin.SetMode(gin.TestMode)
			w := httptest.NewRecorder()

			f := tt.fields(mockCtrl)

			c, _ := gin.CreateTestContext(w)
			c.Params = gin.Params{
				{
					Key:   "shipment_id",
					Value: tt.args.shipmentID,
				},
			}
			c.Request, _ = http.NewRequest("GET", "", nil)

			testShipmentController := &shipmentController{
				shipmentIrregularityGetHandler: f.shipmentIrregularityGetHandler,
			}
			testShipmentController.getListOperationalIssues(c)

			if tt.errStr != "" {
				assert.Equal(t, tt.errStr, c.Errors.Last().Error(), "want error but not match")
			} else {
				assert.Nil(t, c.Errors.Last(), "got unexpected error %v", c.Errors.Last())
			}
		})
	}
}

func Test_shipmentController_createIrregularity(t *testing.T) {
	t.Parallel()
	type (
		args struct {
			shipmentID string
			payload    string
		}
		fields struct {
			setupMock func(*mocks1.MockShipmentIrregularityCreator)
		}
	)

	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()

	tests := []struct {
		name    string
		args    args
		fields  fields
		wantErr bool
	}{
		{
			name: "ID contains characters",
			args: args{
				shipmentID: "123abc",
			},
			wantErr: true,
		},
		{
			name: "ID is 0",
			args: args{
				shipmentID: "0",
			},
			wantErr: true,
		},
		{
			name: "payload invalid",
			args: args{
				shipmentID: "123",
				payload:    `{"movement_status_id": 0}`,
			},
			wantErr: true,
		},
		{
			name: "create irregularity got error",
			args: args{
				shipmentID: "123",
				payload: `{
					"movement_status_id": 7,
					"link_type": 1,
					"linked_id": 357,
					"issue_id": 101,
					"reason_id": 201,
					"note": "Test note"
				}`,
			},
			fields: fields{
				setupMock: func(m *mocks1.MockShipmentIrregularityCreator) {
					m.EXPECT().Create(
						gomock.Any(),
						gomock.Any(),
						gomock.Any(),
					).Return(nil, fplerror.ErrEntityNotFound.NewWithoutStack("Shipment event not found"))
				},
			},
			wantErr: true,
		},
		{
			name: "create irregularity successful",
			args: args{
				shipmentID: "123",
				payload: `{
					"movement_status_id": 7,
					"link_type": 1,
					"linked_id": 357,
					"issue_id": 101,
					"reason_id": 201,
					"note": "Test note"
				}`,
			},
			fields: fields{
				setupMock: func(m *mocks1.MockShipmentIrregularityCreator) {
					m.EXPECT().Create(
						gomock.Any(),
						gomock.Any(),
						auth.UserInfo{
							Email: "<EMAIL>",
						},
					).Return(&models.ShipmentIrregularity{
						ID:               1,
						ShipmentID:       123,
						MovementStatusID: 7,
						LinkType:         1,
						LinkedID:         357,
						IssueID:          null.UintFrom(101),
						ReasonID:         null.UintFrom(201),
						Note:             null.StringFrom("Test note"),
						CreatedBy:        "<EMAIL>",
					}, nil)
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockCtrl := gomock.NewController(t)
			defer mockCtrl.Finish()

			mockCreator := mocks1.NewMockShipmentIrregularityCreator(mockCtrl)
			if tt.fields.setupMock != nil {
				tt.fields.setupMock(mockCreator)
			}

			c, _ := gin.CreateTestContext(w)
			c.Params = gin.Params{
				{
					Key:   "shipment_id",
					Value: tt.args.shipmentID,
				},
			}
			c.Request, _ = http.NewRequest("POST", "", strings.NewReader(tt.args.payload))
			c.Set(middlewareCfg.UserInfoKey, auth.UserInfo{
				Email: "<EMAIL>",
			})

			testShipmentController := &shipmentController{
				shipmentIrregularityCreator: mockCreator,
			}
			testShipmentController.createIrregularity(c)

			if tt.wantErr {
				assert.NotNil(t, c.Errors.Last(), "want error but got none")
			} else {
				assert.Nil(t, c.Errors.Last(), "got unexpected error %v", c.Errors.Last())
				assert.Equal(t, http.StatusCreated, w.Code)
			}
		})
	}
}

func Test_shipmentController_deleteIrregularity(t *testing.T) {
	t.Parallel()

	type args struct {
		shipmentID     string
		irregularityID string
	}

	type fields struct {
		setupMock func(*mocks1.MockShipmentIrregularityDeleter)
	}

	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()

	tests := []struct {
		name     string
		args     args
		fields   fields
		wantErr  error
		wantCode int
	}{
		{
			name: "invalid shipment ID",
			args: args{
				shipmentID:     "abc",
				irregularityID: "456",
			},
			wantErr:  fplerror.ErrBadRequest.NewWithoutStack("strconv.ParseUint: parsing \"abc\": invalid syntax"),
			wantCode: http.StatusBadRequest,
		},
		{
			name: "shipment ID is 0",
			args: args{
				shipmentID:     "0",
				irregularityID: "456",
			},
			wantErr:  fplerror.ErrBadRequest.NewWithoutStack("Key: 'DeleteIrregularityUri.ShipmentID' Error:Field validation for 'ShipmentID' failed on the 'required' tag"),
			wantCode: http.StatusBadRequest,
		},
		{
			name: "invalid irregularity ID",
			args: args{
				shipmentID:     "123",
				irregularityID: "abc",
			},
			wantErr:  fplerror.ErrBadRequest.NewWithoutStack("strconv.ParseUint: parsing \"abc\": invalid syntax"),
			wantCode: http.StatusBadRequest,
		},
		{
			name: "irregularity ID is 0",
			args: args{
				shipmentID:     "123",
				irregularityID: "0",
			},
			wantErr:  fplerror.ErrBadRequest.NewWithoutStack("Key: 'DeleteIrregularityUri.IrregularityID' Error:Field validation for 'IrregularityID' failed on the 'required' tag"),
			wantCode: http.StatusBadRequest,
		},
		{
			name: "shipment not found",
			args: args{
				shipmentID:     "123",
				irregularityID: "456",
			},
			fields: fields{
				setupMock: func(m *mocks1.MockShipmentIrregularityDeleter) {
					m.EXPECT().Delete(
						gomock.Any(),
						uint(123),
						uint(456),
					).Return(fplerror.ErrEntityNotFound.NewWithoutStack("shipment.id.not_found", uint(123)))
				},
			},
			wantErr:  fplerror.ErrEntityNotFound.NewWithoutStack("shipment.id.not_found", uint(123)),
			wantCode: http.StatusNotFound,
		},
		{
			name: "irregularity not found",
			args: args{
				shipmentID:     "123",
				irregularityID: "456",
			},
			fields: fields{
				setupMock: func(m *mocks1.MockShipmentIrregularityDeleter) {
					m.EXPECT().Delete(
						gomock.Any(),
						uint(123),
						uint(456),
					).Return(fplerror.ErrEntityNotFound.NewWithoutStack("Shipment irregularity not found"))
				},
			},
			wantErr:  fplerror.ErrEntityNotFound.NewWithoutStack("Shipment irregularity not found"),
			wantCode: http.StatusNotFound,
		},
		{
			name: "irregularity does not belong to shipment",
			args: args{
				shipmentID:     "123",
				irregularityID: "456",
			},
			fields: fields{
				setupMock: func(m *mocks1.MockShipmentIrregularityDeleter) {
					m.EXPECT().Delete(
						gomock.Any(),
						uint(123),
						uint(456),
					).Return(fplerror.ErrBadRequest.NewWithoutStack("Cannot delete an irregularity that does not belong to the shipment"))
				},
			},
			wantErr:  fplerror.ErrBadRequest.NewWithoutStack("Cannot delete an irregularity that does not belong to the shipment"),
			wantCode: http.StatusBadRequest,
		},
		{
			name: "internal server error",
			args: args{
				shipmentID:     "123",
				irregularityID: "456",
			},
			fields: fields{
				setupMock: func(m *mocks1.MockShipmentIrregularityDeleter) {
					m.EXPECT().Delete(
						gomock.Any(),
						uint(123),
						uint(456),
					).Return(errors.New("internal server error"))
				},
			},
			wantErr:  errors.New("internal server error"),
			wantCode: http.StatusInternalServerError,
		},
		{
			name: "delete successful",
			args: args{
				shipmentID:     "123",
				irregularityID: "456",
			},
			fields: fields{
				setupMock: func(m *mocks1.MockShipmentIrregularityDeleter) {
					m.EXPECT().Delete(
						gomock.Any(),
						uint(123),
						uint(456),
					).Return(nil)
				},
			},
			wantErr:  nil,
			wantCode: http.StatusNoContent,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockCtrl := gomock.NewController(t)
			defer mockCtrl.Finish()

			mockDeleter := mocks1.NewMockShipmentIrregularityDeleter(mockCtrl)
			if tt.fields.setupMock != nil {
				tt.fields.setupMock(mockDeleter)
			}

			c, _ := gin.CreateTestContext(w)
			c.Params = gin.Params{
				{
					Key:   "shipment_id",
					Value: tt.args.shipmentID,
				},
				{
					Key:   "irregularity_id",
					Value: tt.args.irregularityID,
				},
			}
			c.Request, _ = http.NewRequest("DELETE", "", nil)

			testShipmentController := &shipmentController{
				shipmentIrregularityDeleter: mockDeleter,
			}
			testShipmentController.deleteIrregularity(c)

			if tt.wantErr != nil {
				assert.NotNil(t, c.Errors.Last(), "want error but got none")
				assert.Equal(t, tt.wantErr.Error(), c.Errors.Last().Error())
			} else {
				assert.Nil(t, c.Errors.Last(), "got unexpected error %v", c.Errors.Last())
				assert.Equal(t, tt.wantCode, w.Code)
			}
		})
	}
}
