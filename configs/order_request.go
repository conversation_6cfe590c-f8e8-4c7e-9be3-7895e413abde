package configs

import (
	"net/http"
)

type OrderRequestStatus uint8

const (
	OrderRequestProcessing OrderRequestStatus = iota
	OrderRequestSuccess
	OrderRequestErrBadRequest
	OrderRequestErrInternal
	OrderRequestDeleted
	OrderRequestProcessingInCore // internal to BE
)

type RequestIdScheme string

const (
	RequestIdSchemeLazadaMmccOC     RequestIdScheme = "LAZADA_MMCC_OC"
	RequestIdSchemeShopeeMmccOC     RequestIdScheme = "SHOPEE_MMCC_OC"
	RequestIdSchemeShopeeMmccCancel RequestIdScheme = "SHOPEE_MMCC_CANCEL"

	RequestIdSchemeLazadaMMOC       RequestIdScheme = "LAZADA_MM_BAG_OC"
	RequestIdSchemeLazadaMMParcelOC RequestIdScheme = "LAZADA_AIDC_MM_PARCEL_OC"

	RequestIdSchemeLazadaCCOC RequestIdScheme = "LAZADA_CC_PARCEL_OC"
)

func (r RequestIdScheme) String() string {
	return string(r)
}

func NewOrderRequestStatus(httpCode int) OrderRequestStatus {
	switch httpCode {
	case http.StatusOK:
		return OrderRequestSuccess
	case http.StatusBadRequest, http.StatusForbidden:
		return OrderRequestErrBadRequest
	default:
		return OrderRequestErrInternal
	}
}

func (s OrderRequestStatus) Uint8() uint8 {
	return uint8(s)
}

type RequestType string

const (
	BagOC        RequestType = "BAG_OC"
	MMCCParcelOC RequestType = "MMCC_PARCEL_OC"
	BagUpdate    RequestType = "BAG_UPDATE"
	BagCancel    RequestType = "BAG_CANCEL"
)
