package shipment

import (
	"fmt"
)

type (
	Status uint16
	Type   uint8
)

const (
	DefaultSort = "id DESC"
)

const (
	// List of shipment statuses
	StatusDraft              Status = iota // 0: shipment is not ready, still in assignment bag/parcel process
	StatusConfirmed                        // 1: shipment is ready to go
	StatusInTransit                        // 2: shipment is in progress
	StatusCompleted                        // 3: shipment is completed, all the parcels/bags were scanned successfully in the last mile
	StatusPartiallyCompleted               // 4: bag/parcel is in customs held or other reasons like truck was exploded..
	StatusCancelled                        // 5: TBD
	StatusFailed                           // 6: TBD
)

func (s Status) Uint16() uint16 {
	return uint16(s)
}

func (s Status) String() string {
	return fmt.Sprintf("%d", s.Uint16())
}

const (
	// List of shipment movement statuses
	LinehaulScheduled    uint = iota + 1 // 1
	ExportCleared                        // 2
	LinehaulDeparted                     // 3
	LinehaulArrived                      // 4
	ImportCleared                        // 5
	HandedOverToLastMile                 // 6
	HandedOverToLinehaul                 // 7
	Completed                            // 8
	ExportStarted                        // 9
	ImportStarted                        // 10
	TruckDispatched                      // 11
	ContainerLoaded                      // 12
)

const (
	TypeUnknown Type = iota // 0
	TypeLand                // 1
	TypeAir                 // 2
	TypeSea                 // 3
)

type ShipmentLogType uint8

const (
	MoveBags ShipmentLogType = iota + 1
	CreateShipment
	UpdateShipment
	AssignShipmentParcel
	UnassignShipmentParcel
	UpdateShipmentEvent
	DeleteShipment
	ChangeUpliftStatus
	MAWBDocument
)

type MAWBDocumentLogAction uint8

const (
	MAWBDocumentLogActionAdd MAWBDocumentLogAction = iota + 1
	MAWBDocumentLogActionDelete
)

var (
	// MovementStatusMap - Map of shipment movement status ids with meanings
	MovementStatusMap = map[uint]string{
		LinehaulScheduled:    "Linehaul Scheduled",
		ExportCleared:        "Export Cleared",
		LinehaulDeparted:     "Linehaul Departed",
		LinehaulArrived:      "Linehaul Arrived",
		ImportCleared:        "Customs Cleared",
		HandedOverToLastMile: "Handed Over to Last Mile",
		HandedOverToLinehaul: "Handed Over to Linehaul",
		Completed:            "Completed",
		ExportStarted:        "Export Started",
		ImportStarted:        "Import Started",
		TruckDispatched:      "Truck Dispatched",
		ContainerLoaded:      "Container Loaded",
	}

	// MovementStatusList - Array of shipment movement status ids
	MovementStatusList = []uint{
		HandedOverToLinehaul,
		LinehaulScheduled,
		ExportStarted,
		ExportCleared,
		LinehaulDeparted,
		LinehaulArrived,
		ImportStarted,
		ImportCleared,
		HandedOverToLastMile,
		Completed,
		TruckDispatched,
		ContainerLoaded,
	}

	MovementStatusToInternalStatusMap = map[uint]uint{
		LinehaulScheduled:    20,
		ExportCleared:        21,
		LinehaulDeparted:     22,
		LinehaulArrived:      23,
		ImportCleared:        15,
		HandedOverToLastMile: 24,
		HandedOverToLinehaul: 30,
		Completed:            8,
		ExportStarted:        44,
		ImportStarted:        43,
		TruckDispatched:      45,
		ContainerLoaded:      46,
	}

	TransportTypeMap = map[Type]string{
		TypeUnknown: "UNKNOWN",
		TypeLand:    "LAND",
		TypeAir:     "AIR",
		TypeSea:     "SEA",
	}
)

var ShipmentEventIDNameMapping = map[uint]string{
	1:  "Linehaul Scheduled",
	2:  "Export Cleared",
	3:  "Linehaul Departed",
	4:  "Linehaul Arrived",
	5:  "Customs Cleared",
	6:  "Handed Over to Last Mile",
	7:  "Handed Over to Linehaul",
	8:  "Completed",
	9:  "Export Started",
	10: "Import Started",
	11: "Truck Dispatched",
	12: "Container Loaded",
}

type OverallUpliftStatus uint8

const (
	OverallUpliftStatusFullUplift OverallUpliftStatus = iota + 1
	OverallUpliftStatusPartialUplift
	OverallUpliftStatusFullOffload
)
