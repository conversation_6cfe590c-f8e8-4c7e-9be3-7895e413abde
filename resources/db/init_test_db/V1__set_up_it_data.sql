CREATE TABLE IF NOT EXISTS consumed_messages_tab
(
    id
    INT
    AUTO_INCREMENT
    PRIMARY
    KEY,
    topic
    VARCHAR
(
    1024
) NOT NULL,
    message TEXT NOT NULL,
    consumed_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

INSERT INTO `partners` (`id`, `code`, `type`, `auth_client_id`, `source_platform_id`, `event_export_method`,
                        `order_creation_method`, `is_polling`, `created_at`, `updated_at`)
VALUES (6, '4PL_OC_EXTERNAL', NULL, '4PL_OC_EXTERNAL', NULL, 1, 1, 1, '2021-05-18 09:40:55', '2023-10-02 06:28:39'),
       (88426, 'LAZADAMM-CC', NULL, NULL, NULL, 1, 1, 1, '2025-04-04 04:51:20', '2025-04-04 04:51:20'),
       (6586, 'TIKTOK', 3, '<PERSON><PERSON><PERSON>', NULL, 1, 1, 1, '2022-05-10 09:38:01', '2024-09-04 04:24:21');



INSERT INTO `partner_settings` (`id`, `partner_id`, `criteria`, `key`, `value`, `created_at`, `updated_at`)
VALUES (1767, 6, NULL, 'OC_CONFIG',
        '{\"services\":[12351,12353,12350,25,199,219,78130,3,13,19,21,23,27,1785,12322,12352,12976,17084,18504,18505,18837,18838,18839,33972,38778,60651,62273,62624,64233,70922,75347,75356],\"is_used_consolidated_shipper\":false}',
        '2023-09-27 10:59:14', '2025-03-04 00:54:36'),
       (11846, 88426, NULL, 'OC_CONFIG', '{"services":[81904,81905,83096],"is_used_consolidated_shipper":false}',
        '2023-09-27 10:59:14', '2025-03-04 00:54:36'),
       (48, 6586, NULL, 'OC_CONFIG',
        '{\"services\":[81904,3,7,13,21,23,25,91,121,123,133,149,161,173,191,201,203,209,219,307,341,534,539,670,1556,1785,12239,12268,12269,12285,12287,12311,12322,12323,12350,12351,12352,12353,12370,12371,12372,12373,12374,12375,12376,12377,12378,12379,12380,12381,12382,12383,12384,12385,12386,12387,12388,12510,12520,13304,13305,17084,17668,17752,17756,17911,18076,18077,18337,18505,22441,22590,23859,27194,27195,27196,27197,48729,70001,70009,71977,79956,82101,87608],\"is_used_consolidated_shipper\":false}',
        '2022-05-16 04:22:16', '2025-05-29 08:32:25');



INSERT INTO `partner_shippers` (`id`, `partner_id`, `shipper_id`, `is_default`, `created_at`, `updated_at`)
VALUES (20394, 6, 6340935, 1, '2024-04-02 12:36:51', '2024-05-30 04:24:54'),
       (20395, 6, 6367706, 1, '2024-04-02 12:38:07', '2024-09-29 15:13:06'),
       (20401, 6, 6309203, 1, '2024-04-02 12:38:26', '2024-05-30 03:34:23'),
       (20403, 6, 6324578, 1, '2024-04-02 12:39:05', '2024-09-29 15:13:06'),
       (45859, 6, 6334113, 1, '2024-07-11 08:10:24', '2024-07-12 02:38:59'),
       (100019, 6, 6348682, 0, '2025-03-04 00:06:47', '2025-03-04 00:07:35'),
       (109091, 88426, 7012943, 1, '2025-04-22 03:41:38', '2025-04-22 03:41:38'),
       (109092, 88426, 7019994, 0, '2025-04-22 03:42:38', '2025-04-22 03:42:38'),
       (966, 6586, 907039, 1, '2022-09-30 03:44:13', '2022-09-30 03:44:18'),
       (1090, 6586, 900501, 1, '2023-01-17 03:16:02', '2023-01-17 03:16:31'),
       (1142, 6586, 907040, 1, '2023-02-15 08:08:56', '2023-02-15 08:09:06'),
       (1557, 6586, 6326006, 1, '2023-06-01 12:53:51', '2023-07-24 10:07:15'),
       (1644, 6586, 907048, 1, '2023-07-04 07:55:37', '2023-07-04 07:58:20'),
       (1645, 6586, 6362775, 1, '2023-07-04 07:58:03', '2023-07-04 07:58:22'),
       (17706, 6586, 6503541, 0, '2024-03-21 03:34:57', '2024-03-21 03:34:57'),
       (53508, 6586, 6792219, 0, '2024-08-14 08:36:49', '2024-08-14 08:36:49'),
       (53511, 6586, 911042, 0, '2024-08-14 11:12:35', '2024-08-14 11:12:35'),
       (53634, 6586, 6788342, 0, '2024-08-15 03:29:25', '2024-08-15 03:29:25'),
       (57097, 6586, 6823794, 0, '2024-09-04 04:24:07', '2024-09-04 04:24:07'),
       (108348, 6586, 7018113, 0, '2025-04-16 00:05:10', '2025-04-16 00:05:10');



INSERT INTO `shippers` (`id`, `service_id`, `remote_id`, `remote_legacy_id`, `name`, `country`, `type`, `tax_numbers`,
                        `metadata`, `created_at`, `updated_at`)
VALUES (6340935, NULL, 5606689, 29992, '4PL External PH', 'PH', 1, '{}', NULL, '2023-06-10 13:51:04',
        '2024-03-29 02:29:32'),
       (6367706, NULL, 5633772, 77456, '4PL External ID', 'ID', 0, '{}', NULL, '2023-07-11 09:24:17',
        '2023-07-14 04:16:52'),
       (6309203, 13, 5574349, 142653, '4PL External MY', 'MY', 1,
        '{\"gst\":\"GSTSHIPPER123\",\"lvg\":\"LVGSHIPPER123\"}', '{\"show_shipper_details\":true,\"show_cod\":true}',
        '2023-05-12 04:08:21', '2025-02-26 06:46:05'),
       (6324578, NULL, 5589995, 15678, '4PL External VN', 'VN', 1, '{}',
        '{\"show_shipper_details\":true,\"show_cod\":true}', '2023-05-24 08:10:41', '2023-10-27 01:58:19'),
       (6334113, 3, 5599715, 946046, '4PL External SG', 'SG', 1, '{}',
        '{\"show_shipper_details\":true,\"show_cod\":true}', '2023-06-02 10:04:43', '2025-03-04 00:55:47'),
       (6348682, NULL, 5614556, 143358, '4PL External TH', 'TH', 1, '{}',
        '{\"show_shipper_details\":true,\"show_cod\":false}', '2023-06-19 06:35:46', '2024-05-21 02:07:17'),
       (7012943, NULL, 6407724, 188853, '4PL Lazada MM MY', 'MY', 0, '{}',
        '{\"show_shipper_details\":true,\"show_cod\":false}', '2023-06-19 06:35:46', '2024-05-21 02:07:17'),
       (7019994, NULL, 6416166, 52919, 'Lazada CC PH', 'PH', 0, '{}',
        '{\"show_shipper_details\":true,\"show_cod\":false}', '2023-06-19 06:35:46', '2024-05-21 02:07:17'),
       (8842549, NULL, 10416620, 736378, 'TOKGISTIC PTE. LTD', 'PH', 0, '{}',
        '{\"show_shipper_details\":true,\"show_cod\":false}', '2023-06-19 06:35:46', '2024-05-21 02:07:17'),
       (8146613, NULL, 9717974, 847340, 'TOKGISTIC PTE. LTD', 'MY', 1, '{}',
        '{\"show_shipper_details\":true,\"show_cod\":false}', '2023-06-19 06:35:46', '2024-05-21 02:07:17'),
       (907039, 173, 4720914, 12910, '4PL_shippernamePH', 'PH', 1, '{}', NULL, '2021-04-09 02:54:27',
        '2023-06-21 10:11:26'),
       (900501, 12268, 4714201, 127405, '4PL_shippernameMY_1', 'MY', 1, '{}',
        '{\"show_shipper_details\":true,\"show_cod\":false}', '2021-02-10 07:26:44', '2025-05-29 00:06:45'),
       (907040, NULL, 4720915, 110753, '4PL_shippernameTH', 'TH', 1, '{}',
        '{\"show_shipper_details\":true,\"show_cod\":false}', '2021-04-09 02:59:53', '2025-05-29 00:07:02'),
       (6326006, NULL, 5591433, 940874, 'TIKTOK SHIPPER SG', 'SG', 1, '{}',
        '{\"show_shipper_details\":false,\"show_cod\":false}', '2023-05-25 08:00:00', '2025-05-29 00:06:29'),
       (907048, NULL, 4720921, 975, '4PL_shippernameVN', 'VN', 1, '{}', NULL, '2021-04-09 03:16:22',
        '2023-04-26 06:30:21'),
       (6362775, NULL, 5628839, 76854, 'TT_ID1688457427 id', 'ID', 0, '{}', NULL, '2023-07-04 07:57:07',
        '2023-07-04 07:57:36'),
       (6503541, 27358, 5740678, 151052, 'QA - TikTok XB MY LM', 'MY', 1, '{}',
        '{\"show_shipper_details\":false,\"show_cod\":false}', '2023-10-30 04:22:00', '2024-07-25 05:02:04'),
       (6792219, 173, 6081686, 46070, 'Lazada PH QA (LZD-Sender-ZTWNM2C2141Z9-K0HJPKB1O)', 'PH', 1, NULL, NULL,
        '2024-07-29 08:53:37', '2024-08-14 08:33:36'),
       (911042, NULL, 4724988, 12964, 'QA-PH-Lazada Master Account (ASC)', 'PH', 0, '{}', NULL, '2021-05-03 08:05:45',
        '2023-07-13 04:26:09'),
       (6788342, NULL, 6077772, 45902, 'QA - TikTok XB PH LM', 'PH', 0, '{}',
        '{\"show_shipper_details\":false,\"show_cod\":false}', '2024-07-25 06:37:16', '2024-07-25 06:39:06'),
       (6823794, 23, 6113720, 29604, 'QA - TikTok XB VN LM', 'VN', 1, '{}',
        '{\"show_shipper_details\":false,\"show_cod\":false}', '2024-08-27 09:19:10', '2024-09-04 04:25:24'),
       (7018113, NULL, 6414176, 1365737, ' (NMS001 Shop)', 'SG', 0, '{}',
        '{\"show_shipper_details\":false,\"show_cod\":false}', '2025-04-15 03:55:30', '2025-04-15 03:55:30');



INSERT INTO `services` (`id`, `product_id`, `code`, `type`, `description`, `sla`, `is_domestic`, `created_at`,
                        `updated_at`)
VALUES (3, 37, 'SG0011', 1, 'Service for OC SG LM', 1.1, 0, '2020-04-06 14:43:30', '2023-09-26 03:46:50'),
       (13, 39, 'MY0011', 1, 'MY LM Standard', NULL, 0, '2020-04-06 14:43:30', '2024-06-03 06:49:44'),
       (19, 41, 'ID0011', 1, 'ID LM', NULL, 0, '2020-04-06 14:43:30', '2020-04-06 14:43:30'),
       (21, 43, 'PH0011', 1, 'PH LM', NULL, 0, '2020-04-06 14:43:30', '2020-04-06 14:43:30'),
       (23, 45, 'VN0011', 1, 'VN LM', NULL, 0, '2020-04-06 14:43:30', '2020-04-06 14:43:30'),
       (25, 47, 'TH0011', 1, 'TH LM', NULL, 0, '2020-04-06 14:43:30', '2020-04-06 14:43:30'),
       (27, 49, 'SG1000', 2, 'SG CC FAF', NULL, 1, '2020-04-06 14:43:30', '2023-01-18 09:58:25'),
       (199, 177, 'MYSG0015', 6, 'MYSG E2E', NULL, 0, '2020-04-06 14:43:34', '2021-05-13 05:12:26'),
       (219, 193, 'SGMY0015', 6, 'SGMY E2E', NULL, 0, '2020-04-06 14:43:35', '2020-04-06 14:43:35'),
       (1785, 3307, 'SVKJZ0BV18', 6, 'description of service', NULL, 0, '2021-01-16 01:03:36', '2021-01-16 01:03:36'),
       (12322, 37, 'SG0014', 1, 'SG LM Standard', NULL, 1, '2020-04-06 14:43:30', '2023-10-10 03:45:06'),
       (12350, 28585, '4PLTHFMONLY', 6, '4PL AUTO PLS DONT TOUCH TH FM', NULL, 0, '2023-04-03 08:21:23',
        '2023-04-03 08:21:23'),
       (12351, 28581, '4PLAUTOSGTHE2E', 6, '4PL AUTO PLS DONT TOUCH SGTH E2E', NULL, 0, '2023-04-03 08:25:43',
        '2024-03-15 03:46:41'),
       (12352, 28582, '4PLAUTOSGFM', 6, '4PL AUTO PLS DONT TOUCH SG FM', NULL, 0, '2023-04-03 08:26:30',
        '2023-04-03 08:26:30'),
       (12353, 28583, '4PLAUTOSGLM', 1, 'Service for External OC SG LM', 1.1, 0, '2023-04-03 08:31:26',
        '2025-03-04 00:05:06'),
       (12976, 29792, 'SVLN3EZ3F9', 6, 'description of service', NULL, 0, '2023-09-28 16:53:10', '2023-09-28 16:53:10'),
       (17084, 38615, 'FPLMMCC', 5, 'FPLMMCC', NULL, 0, '2023-11-15 03:54:37', '2023-12-04 08:03:42'),
       (18504, 39444, 'THSG-MMCC', 5, 'THSG-MMCC', NULL, 0, '2023-12-08 03:02:13', '2023-12-08 03:02:13'),
       (18505, 39445, 'SGTH-MMCC', 5, 'SGTH-MMCC', NULL, 0, '2023-12-08 04:14:16', '2023-12-08 04:14:16'),
       (18837, 40026, 'CNSG-MMCC', 5, 'CNSG-MMCC', NULL, 0, '2023-12-11 06:28:27', '2023-12-11 06:28:27'),
       (18838, 40027, 'SGPH-MMCC', 5, 'SGPH-MMCC', NULL, 0, '2023-12-11 06:31:04', '2023-12-11 06:31:04'),
       (18839, 40028, 'VNID-MMCC', 5, 'VNID-MMCC', NULL, 0, '2023-12-11 06:32:30', '2023-12-11 06:32:30'),
       (33972, 66573, 'SGTWNN001', 6, 'SGTWNN001', NULL, 0, '2024-05-23 08:42:15', '2024-05-23 08:42:15'),
       (38778, 74667, 'USSGNN003', 6, 'USSGNN003', NULL, 0, '2024-06-11 05:25:01', '2024-06-11 05:25:01'),
       (60651, 40026, 'CNSG-MMCC-B2B', 8, 'CNSG-MMCC-B2B', NULL, 0, '2024-11-12 07:55:33', '2024-11-12 07:55:33'),
       (62273, 193, 'SGMYB2BBUNDLE', 9, 'SGMY B2B BUNDLE', NULL, 0, '2024-11-25 09:15:01', '2024-11-25 09:15:01'),
       (62624, 113960, 'CNMY-B2B-BUNDLE', 9, 'CNMY-B2B-BUNDLE', NULL, 0, '2024-11-28 08:38:37', '2024-11-28 08:38:37'),
       (64233, 37, 'SG-LM-B2B-BUNDLE', 9, 'SG-LM-B2B-BUNDLE', NULL, 0, '2024-12-09 04:22:12', '2024-12-09 04:22:12'),
       (70922, 122784, 'YUNAU', 6, 'Yun NV SG AU', NULL, 0, '2025-01-02 09:58:52', '2025-01-02 09:58:52'),
       (75347, 130072, 'YUNSGTW', 6, 'NV-YUN SG-TW', NULL, 0, '2025-02-07 06:18:34', '2025-02-07 06:18:34'),
       (75356, 130073, 'YUNSGHK', 6, 'NV-YUN SG-HK', NULL, 0, '2025-02-07 06:21:44', '2025-02-07 06:21:44'),
       (78130, 134868, 'SGHK-MMCC001', 6, 'SGHK-MMCC', NULL, 0, '2025-03-03 10:31:42', '2025-03-03 10:31:42'),
       (81904, 141019, 'CNMY-A-S-BKI-AF-2', 5, 'Malaysia -BKI (Sensitive cargo) CNMY-A-S-BKI-AF-2', NULL, 0,
        '2025-04-04 06:22:21', '2025-04-04 06:22:21'),
       (81905, 141019, 'CNMY-A-S-KCH-AF-2', 5, 'Malaysia -KCH (Sensitive cargo) CNMY-A-S-KCH-AF-2', NULL, 0,
        '2025-04-04 06:20:52', '2025-04-04 06:20:52'),
       (83096, 143001, 'PH-CRK-CC-1', 2, 'PH-CRK-CC-1', NULL, 0, '2025-04-04 06:21:21', '2025-04-04 06:21:21'),
       (87608, 150720, 'CNPHMMCC-A-S-1', 5, 'CNPHMMCC-A-S-1', NULL, 0, '2025-05-29 08:14:00', '2025-05-29 08:14:00');


INSERT INTO `products` (`id`, `name`, `code`, `description`, `origin_port`, `origin_country`, `destination_port`,
                        `destination_country`, `transport_type`, `clearance_type`, `freight_type`, `lead_time`,
                        `status`, `is_default`, `is_deleted`, `created_at`, `updated_at`)
VALUES (37, 'SG00', 'SG00', NULL, NULL, 'SG', NULL, 'SG', 2, NULL, NULL, NULL, 1, 0, 0, '2020-03-18 09:05:20',
        '2023-08-07 09:25:36'),
       (39, 'MY00', 'MY00', NULL, NULL, 'MY', NULL, 'MY', NULL, NULL, NULL, NULL, 1, 0, 0, '2020-03-18 09:05:20',
        '2020-10-15 15:00:46'),
       (41, 'ID00', 'ID00', NULL, NULL, 'ID', NULL, 'ID', NULL, NULL, NULL, NULL, 1, 0, 0, '2020-03-18 09:05:20',
        '2020-10-15 15:00:46'),
       (43, 'PH00', 'PH00', NULL, NULL, 'PH', NULL, 'PH', NULL, NULL, NULL, NULL, 1, 0, 0, '2020-03-18 09:05:20',
        '2020-10-15 15:00:46'),
       (45, 'VN00', 'VN00', NULL, NULL, 'VN', NULL, 'VN', NULL, NULL, NULL, NULL, 1, 0, 0, '2020-03-18 09:05:20',
        '2020-10-15 15:00:46'),
       (47, 'TH00', 'TH00', NULL, NULL, 'TH', NULL, 'TH', NULL, NULL, NULL, NULL, 1, 0, 0, '2020-03-18 09:05:20',
        '2020-10-15 15:00:46'),
       (49, 'SG1000X', 'SG1000X', NULL, NULL, 'SG', NULL, 'SG', NULL, NULL, NULL, NULL, 1, 0, 0, '2020-03-18 09:05:20',
        '2020-10-15 15:00:46'),
       (177, 'MYSG001', 'MYSG001', NULL, 'KUL', 'MY', 'SIN', 'SG', 3, 2, 1, NULL, 1, 0, 0, '2020-03-18 09:05:29',
        '2025-02-17 07:29:56'),
       (193, 'SGMY001', 'SGMY001', NULL, 'SIN', 'SG', 'KUL', 'MY', 3, 2, 2, NULL, 1, 0, 0, '2020-03-18 09:05:31',
        '2020-10-15 15:00:46'),
       (3307, '3PL-E2E-KJZ0BUOB', 'THID-KJZ0BUOB', NULL, NULL, 'TH', NULL, 'ID', 1, 1, 1, NULL, 0, 0, 0,
        '2021-01-16 01:03:36', '2021-01-16 01:03:36'),
       (28585, '4PL AUTO PLS DONT TOUCH THFM', '4PLAUTO-THSG-FM', NULL, NULL, 'TH', NULL, 'SG', 1, 1, 1, NULL, 1, 0, 0,
        '2023-04-03 08:19:45', '2023-04-03 08:19:45'),
       (28581, '4PL AUTO PLS DONT TOUCH SGTH E2E', 'SGTH003', NULL, '', 'SG', '', 'TH', 1, 1, 1, NULL, 1, 0, 0,
        '2023-04-03 08:00:32', '2023-09-26 04:40:33'),
       (28582, '4PL AUTO PLS DONT TOUCH SGMY FM', '4PLAUTO-SGMY-FM', NULL, '', 'SG', '', 'TH', 1, 1, 1, NULL, 1, 0, 0,
        '2023-04-03 08:02:42', '2023-09-27 02:13:48'),
       (28583, 'External OC SG LM', '4PLAUTO-THSG-LM', NULL, '', 'TH', '', 'SG', 1, 1, 1, NULL, 1, 0, 0,
        '2023-04-03 08:10:55', '2024-09-10 23:36:14'),
       (29792, '4PL-OC-LN3EZ2LN', '4plCodeOC-LN3EZ2LN', NULL, NULL, 'CN', NULL, 'TL', 1, 1, 1, NULL, 0, 0, 0,
        '2023-09-28 16:53:10', '2023-09-28 16:53:10'),
       (38615, 'SGMY-MMCC', 'SGMY-MMCC', NULL, '', 'CN', '', 'MY', 2, 1, 4, NULL, 1, 0, 0, '2023-12-04 08:03:03',
        '2024-04-29 07:06:28'),
       (39444, 'THSG-MMCC', 'THSG-MMCC', NULL, NULL, 'TH', NULL, 'SG', 1, 1, 1, NULL, 1, 0, 0, '2023-12-08 03:00:58',
        '2023-12-08 03:00:58'),
       (39445, 'SGTH-MMCC', 'SGTH-MMCC', NULL, NULL, 'SG', NULL, 'TH', 1, 1, 1, NULL, 1, 0, 0, '2023-12-08 04:14:02',
        '2023-12-08 04:14:02'),
       (40026, 'CNSG-MMCC', 'CNSG-MMCC', NULL, NULL, 'CN', NULL, 'SG', 2, 2, 1, NULL, 1, 0, 0, '2023-12-11 06:28:16',
        '2023-12-11 06:28:16'),
       (40027, 'SGPH-MMCC', 'SGPH-MMCC', NULL, NULL, 'SG', NULL, 'PH', 3, 2, 2, NULL, 1, 0, 0, '2023-12-11 06:30:46',
        '2023-12-11 06:30:46'),
       (40028, 'VNID-MMCC', 'VNID-MMCC', NULL, NULL, 'VN', NULL, 'ID', 2, 2, 1, NULL, 1, 0, 0, '2023-12-11 06:32:21',
        '2023-12-11 06:32:21'),
       (66573, 'SGTW-NN-001', 'SGTWNG001', NULL, '', 'SG', '', 'TW', 1, 1, 1, NULL, 1, 0, 0, '2024-05-23 08:41:35',
        '2024-05-27 04:13:00'),
       (74667, 'USSGNN003', 'USSGNN003', NULL, NULL, 'US', NULL, 'SG', 1, 1, 1, NULL, 1, 0, 0, '2024-06-11 05:24:31',
        '2024-06-11 05:24:31'),
       (113960, 'CNMY-B2B-BUNDLE', 'CNMY-B2B-BUNDLE', NULL, NULL, 'CN', NULL, 'MY', 1, 1, 1, NULL, 1, 0, 0,
        '2024-11-26 04:34:34', '2024-11-26 04:34:34'),
       (122784, 'NV-Yun SG AU', 'YUNAU', NULL, '', 'SG', '', 'AU', 1, 1, 1, NULL, 1, 0, 0, '2025-01-02 09:58:24',
        '2025-01-08 07:59:00'),
       (130072, 'NV-YUN SG-TW', 'YUNSGTW', NULL, NULL, 'SG', NULL, 'TW', 1, 1, 1, NULL, 1, 0, 0, '2025-02-07 06:09:53',
        '2025-02-07 06:09:53'),
       (130073, 'NV-YUN SG-HK', 'YUNSGHK', NULL, NULL, 'SG', NULL, 'HK', 1, 1, 1, NULL, 1, 0, 0, '2025-02-07 06:10:53',
        '2025-02-07 06:10:53'),
       (134868, 'SGHK-MMCC', 'SGHK-MMCC', NULL, '', 'SG', '', 'HK', 1, 1, 1, NULL, 1, 0, 0, '2025-03-03 10:30:23',
        '2025-03-03 10:31:34'),
       (141019, 'CNMY-A-N-MM', 'CNMY-A-N-MM', NULL, '', 'XX', '', 'MY', 1, 1, 1, NULL, 1, 0, 0, '2025-03-03 10:30:23',
        '2025-03-03 10:31:34'),
       (143001, 'PH-CRK-CC-1', 'PH-CRK-CC-1', NULL, '', 'CN', '', 'PH', 1, 1, 1, NULL, 1, 0, 0, '2025-03-03 10:30:23',
        '2025-03-03 10:31:34'),
       (150720, 'CNPHMMCC', 'CNPHMMCC', NULL, 'EHU', 'CN', 'MNL', 'PH', 1, 1, 1, NULL, 1, 0, 0, '2025-05-29 08:13:27',
        '2025-05-29 08:13:27');



INSERT INTO `product_vendors` (`id`, `product_id`, `stage`, `vendor_id`, `sequence_no`, `has_handle_oc`,
                               `has_handle_tracking`, `nv_order_service_type`, `created_at`, `updated_at`)
VALUES (55, 37, 'LM', 1, 1, 0, 0, NULL, '2020-04-06 14:10:59', '2020-10-27 06:09:15'),
       (57, 39, 'LM', 2, 1, 0, 0, NULL, '2020-04-06 14:10:59', '2020-10-27 06:09:15'),
       (59, 41, 'LM', 3, 1, 0, 0, NULL, '2020-04-06 14:10:59', '2020-10-27 06:09:15'),
       (61, 43, 'LM', 5, 1, 0, 0, NULL, '2020-04-06 14:10:59', '2020-10-27 06:09:15'),
       (63, 45, 'LM', 6, 1, 0, 0, NULL, '2020-04-06 14:10:59', '2020-10-27 06:09:15'),
       (65, 47, 'LM', 4, 1, 0, 0, NULL, '2020-04-06 14:10:59', '2020-10-27 06:09:15'),
       (67, 49, 'CC', 11, 1, 0, 0, NULL, '2020-04-06 14:10:59', '2020-04-06 14:10:59'),
       (431, 177, 'LM', 1, 4, 0, 0, NULL, '2020-04-06 14:11:07', '2023-03-29 08:57:04'),
       (433, 177, 'FM', 2, 1, 0, 0, NULL, '2020-04-06 14:11:07', '2020-10-27 06:09:15'),
       (435, 177, 'CC', 11, 3, 0, 0, NULL, '2020-04-06 14:11:07', '2023-03-29 08:57:07'),
       (437, 177, 'MM', 97, 2, 0, 0, NULL, '2020-04-06 14:11:07', '2020-04-06 14:11:07'),
       (485, 193, 'FM', 1, 1, 0, 0, NULL, '2020-04-06 14:11:08', '2020-10-27 06:09:15'),
       (487, 193, 'LM', 2, 4, 0, 0, NULL, '2020-04-06 14:11:08', '2020-10-27 06:09:15'),
       (491, 193, 'MM', 7, 2, 0, 0, NULL, '2020-04-06 14:11:08', '2023-03-30 08:15:28'),
       (489, 193, 'CC', 8, 3, 0, 0, NULL, '2020-04-06 14:11:08', '2023-03-30 08:15:25'),
       (4471, 3307, 'FM', 7, 1, 0, 0, NULL, '2021-01-16 01:03:36', '2023-09-16 09:33:49'),
       (4473, 3307, 'CC', 13, 3, 0, 0, NULL, '2021-01-16 01:03:36', '2021-01-16 01:03:36'),
       (4472, 3307, 'MM', 13, 2, 0, 0, NULL, '2021-01-16 01:03:36', '2021-01-16 01:03:36'),
       (4474, 3307, 'LM', 166, 4, 0, 0, NULL, '2021-01-16 01:03:36', '2021-01-16 01:03:36'),
       (24914, 28585, 'FM', 4, 1, 0, 0, NULL, '2023-04-03 08:19:45', '2023-04-03 08:19:45'),
       (24915, 28585, 'LM', 251, 2, 0, 0, NULL, '2023-04-03 08:19:45', '2023-04-03 08:19:45'),
       (25711, 28581, 'FM', 1, 1, 0, 0, NULL, '2023-09-26 04:40:33', '2023-09-26 04:40:33'),
       (25714, 28581, 'LM', 4, 4, 0, 0, NULL, '2023-09-26 04:40:33', '2023-09-26 04:40:33'),
       (25713, 28581, 'CC', 199, 3, 0, 0, NULL, '2023-09-26 04:40:33', '2023-09-26 04:40:33'),
       (25712, 28581, 'MM', 251, 2, 0, 0, NULL, '2023-09-26 04:40:33', '2023-09-26 04:40:33'),
       (25935, 28582, 'FM', 1, 1, 0, 0, NULL, '2023-09-27 02:11:58', '2023-09-27 02:11:58'),
       (25937, 28582, 'CC', 7, 3, 0, 0, NULL, '2023-09-27 02:11:58', '2023-09-27 02:11:58'),
       (25938, 28582, 'LM', 7, 4, 0, 0, NULL, '2023-09-27 02:11:58', '2023-09-27 02:11:58'),
       (25936, 28582, 'MM', 7, 2, 0, 0, NULL, '2023-09-27 02:11:58', '2023-09-27 02:11:58'),
       (25254, 28583, 'LM', 1, 4, 0, 0, NULL, '2023-07-23 08:09:11', '2023-07-23 08:09:11'),
       (25253, 28583, 'CC', 7, 3, 0, 0, NULL, '2023-07-23 08:09:11', '2023-07-23 08:09:11'),
       (25251, 28583, 'FM', 7, 1, 0, 0, NULL, '2023-07-23 08:09:11', '2023-07-23 08:09:11'),
       (25252, 28583, 'MM', 7, 2, 0, 0, NULL, '2023-07-23 08:09:11', '2023-07-23 08:09:11'),
       (26580, 29792, 'FM', 3, 1, 0, 0, NULL, '2023-09-28 16:53:10', '2023-09-28 16:53:10'),
       (39532, 38615, 'CC', 7, 2, 0, 0, NULL, '2023-12-05 04:46:44', '2023-12-07 09:57:00'),
       (39531, 38615, 'MM', 7, 1, 0, 0, NULL, '2023-12-05 04:46:44', '2023-12-05 04:46:44'),
       (40661, 39444, 'CC', 7, 2, 0, 0, NULL, '2023-12-08 03:00:59', '2023-12-08 03:00:59'),
       (40660, 39444, 'MM', 7, 1, 0, 0, NULL, '2023-12-08 03:00:59', '2023-12-08 03:00:59'),
       (40663, 39445, 'CC', 7, 2, 0, 0, NULL, '2023-12-08 04:14:03', '2023-12-08 04:14:03'),
       (40662, 39445, 'MM', 7, 1, 0, 0, NULL, '2023-12-08 04:14:03', '2023-12-08 04:14:03'),
       (41503, 40026, 'CC', 7, 2, 0, 0, NULL, '2023-12-11 06:28:16', '2023-12-11 06:28:16'),
       (41502, 40026, 'MM', 7, 1, 0, 0, NULL, '2023-12-11 06:28:16', '2023-12-11 06:28:16'),
       (41505, 40027, 'CC', 7, 2, 0, 0, NULL, '2023-12-11 06:30:47', '2023-12-11 06:30:47'),
       (41504, 40027, 'MM', 7, 1, 0, 0, NULL, '2023-12-11 06:30:47', '2023-12-11 06:30:47'),
       (41507, 40028, 'CC', 7, 2, 0, 0, NULL, '2023-12-11 06:32:22', '2023-12-11 06:32:22'),
       (41506, 40028, 'MM', 7, 1, 0, 0, NULL, '2023-12-11 06:32:22', '2023-12-11 06:32:22'),
       (81646, 66573, 'FM', 1, 1, 0, 0, NULL, '2024-05-27 04:13:00', '2024-05-27 04:13:00'),
       (81648, 66573, 'CC', 65, 3, 0, 0, NULL, '2024-05-27 04:13:00', '2024-05-27 04:13:00'),
       (81649, 66573, 'LM', 65, 4, 0, 0, NULL, '2024-05-27 04:13:00', '2024-05-27 04:13:00'),
       (81647, 66573, 'MM', 65, 2, 0, 0, NULL, '2024-05-27 04:13:00', '2024-05-27 04:13:00'),
       (92440, 74667, 'LM', 1, 4, 0, 0, NULL, '2024-06-11 05:24:31', '2024-06-11 05:24:31'),
       (92439, 74667, 'CC', 65, 3, 0, 0, NULL, '2024-06-11 05:24:31', '2024-06-11 05:24:31'),
       (92437, 74667, 'FM', 65, 1, 0, 0, NULL, '2024-06-11 05:24:31', '2024-06-11 05:24:31'),
       (92438, 74667, 'MM', 65, 2, 0, 0, NULL, '2024-06-11 05:24:31', '2024-06-11 05:24:31'),
       (150676, 113960, 'LM', 2, 4, 0, 0, NULL, '2024-11-26 04:34:34', '2024-11-26 04:34:34'),
       (150675, 113960, 'CC', 95, 3, 0, 0, NULL, '2024-11-26 04:34:34', '2024-11-26 04:34:34'),
       (150673, 113960, 'FM', 95, 1, 0, 0, NULL, '2024-11-26 04:34:34', '2024-11-26 04:34:34'),
       (150674, 113960, 'MM', 95, 2, 0, 0, NULL, '2024-11-26 04:34:34', '2024-11-26 04:34:34'),
       (166456, 122784, 'FM', 1, 1, 0, 0, NULL, '2025-01-08 07:59:01', '2025-01-08 07:59:01'),
       (166458, 122784, 'CC', 5918, 3, 0, 0, NULL, '2025-01-08 07:59:01', '2025-01-08 07:59:01'),
       (166459, 122784, 'LM', 5918, 4, 0, 0, NULL, '2025-01-08 07:59:01', '2025-01-08 07:59:01'),
       (166457, 122784, 'MM', 5918, 2, 0, 0, NULL, '2025-01-08 07:59:01', '2025-01-08 07:59:01'),
       (175217, 130072, 'FM', 1, 1, 0, 0, NULL, '2025-02-07 06:09:54', '2025-02-07 06:09:54'),
       (175219, 130072, 'CC', 5918, 3, 0, 0, NULL, '2025-02-07 06:09:54', '2025-02-07 06:09:54'),
       (175220, 130072, 'LM', 5918, 4, 0, 0, NULL, '2025-02-07 06:09:54', '2025-02-07 06:09:54'),
       (175218, 130072, 'MM', 5918, 2, 0, 0, NULL, '2025-02-07 06:09:54', '2025-02-07 06:09:54'),
       (175221, 130073, 'FM', 1, 1, 0, 0, NULL, '2025-02-07 06:10:53', '2025-02-07 06:10:53'),
       (175223, 130073, 'CC', 5918, 3, 0, 0, NULL, '2025-02-07 06:10:53', '2025-02-07 06:10:53'),
       (175224, 130073, 'LM', 5918, 4, 0, 0, NULL, '2025-02-07 06:10:53', '2025-02-07 06:10:53'),
       (175222, 130073, 'MM', 5918, 2, 0, 0, NULL, '2025-02-07 06:10:53', '2025-02-07 06:10:53'),
       (182182, 134868, 'FM', 1, 1, 0, 0, NULL, '2025-03-03 10:31:34', '2025-03-03 10:31:34'),
       (182184, 134868, 'CC', 7402, 3, 0, 0, NULL, '2025-03-03 10:31:34', '2025-03-03 10:31:34'),
       (182185, 134868, 'LM', 7402, 4, 0, 0, NULL, '2025-03-03 10:31:34', '2025-03-03 10:31:34'),
       (182183, 134868, 'MM', 7402, 2, 0, 0, NULL, '2025-03-03 10:31:34', '2025-03-03 10:31:34'),
       (191296, 141019, 'MM', 2709, 1, 0, 0, NULL, '2025-04-04 04:57:19', '2025-04-04 04:57:19'),
       (194144, 143001, 'CC', 8, 1, 0, 0, NULL, '2025-04-04 04:57:19', '2025-04-04 04:57:19');

INSERT INTO `vendors` (`id`, `name`, `country`, `code`, `handler`, `created_at`, `updated_at`)
VALUES (65, 'ACS', 'TW', 'ACS', 'ACS', '2020-04-06 13:57:29', '2024-05-24 09:39:14'),
       (95, 'SKYWIN', 'XX', 'SKYWIN', 'manual', '2020-04-06 13:57:30', '2020-04-06 13:57:30'),
       (97, 'TCW', 'XX', 'TCW', 'manual', '2020-04-06 13:57:30', '2020-04-06 13:57:30'),
       (166, 'LinexID', 'ID', 'LNID', 'manual', '2020-08-19 04:22:58', '2020-08-19 04:22:58'),
       (199, '4PL System', 'XX', '4PL', 'system', '2021-06-17 10:35:05', '2021-06-17 10:35:05'),
       (251, 'YUNDAO', 'XX', NULL, 'manual', '2023-03-15 05:51:09', '2023-03-15 05:51:09'),
       (2709, 'XX Vendor', 'XX', 'XXCODE', 'manual', '2023-03-15 05:51:09', '2023-03-15 05:51:09'),
       (5918, 'Yun Express', 'HK', 'YUN', 'YUN', '2025-01-02 09:54:12', '2025-01-02 09:55:25'),
       (7402, 'testCC', 'XX', '123456', 'manual', '2025-02-27 02:21:09', '2025-02-27 02:21:09');

DELETE
FROM `internal_statuses`;

INSERT INTO `internal_statuses` (`id`, `name`, `code`, `is_terminal`, `is_internal`, `created_at`, `updated_at`)
VALUES (1, 'Staging', 'STAGING', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (2, 'Pending Pickup', 'PENDING_PICKUP', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (3, 'En-route to Pickup', 'EN_ROUTE_TO_PICKUP', 0, 0, '2019-07-22 11:02:42', '2021-03-29 04:31:17'),
       (4, 'En-route to Sorting Hub', 'EN_ROUTE_TO_SORTING_HUB', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (5, 'Arrived at Sorting Hub', 'ARRIVED_AT_SORTING_HUB', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (6, 'Arrived at Origin Hub', 'ARRIVED_AT_ORIGIN_HUB', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (7, 'On Vehicle for Delivery', 'ON_VEHICLE_FOR_DELIVERY', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (8, 'Completed', 'COMPLETED', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (9, 'Pending Reschedule', 'PENDING_RESCHEDULE', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (10, 'Pickup Fail', 'PICKUP_FAIL', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (11, 'Cancelled', 'CANCELLED', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (12, 'Returned to Sender', 'RETURNED_TO_SENDER', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (13, 'Arrived at Distribution Point', 'ARRIVED_AT_DISTRIBUTION_POINT', 0, 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (14, 'Cross Border Transit', 'CROSS_BORDER_TRANSIT', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (15, 'Customs Cleared', 'CUSTOMS_CLEARED', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (16, 'Customs Held', 'CUSTOMS_HELD', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (17, 'Pending Pickup at Distribution Point', 'PENDING_PICKUP_AT_DISTRIBUTION_POINT', 0, 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (18, 'Arrived at Origin Facility', 'ARRIVED_AT_ORIGIN_FACILITY', 0, 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (19, 'Processed at Origin Facility', 'PROCESSED_AT_ORIGIN_FACILITY', 0, 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (20, 'Linehaul Scheduled', 'LINEHAUL_SCHEDULED', 0, 0, '2019-07-22 11:02:42', '2020-05-15 15:39:29'),
       (21, 'Export Cleared', 'EXPORT_CLEARED', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (22, 'Linehaul Departed', 'LINEHAUL_DEPARTED', 0, 0, '2019-07-22 11:02:42', '2020-05-15 15:39:29'),
       (23, 'Linehaul Arrived', 'LINEHAUL_ARRIVED', 0, 0, '2019-07-22 11:02:42', '2020-05-15 15:39:29'),
       (24, 'Handed Over to Last Mile', 'HANDED_OVER_TO_LAST_MILE', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (25, 'Unknown', 'UNKNOWN', 0, 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (26, 'On Hold', 'ON_HOLD', 0, 0, '2020-06-19 08:46:49', '2020-07-06 06:28:11'),
       (27, 'RTS Triggered', 'RTS_TRIGGERED', 0, 0, '2021-02-23 06:58:04', '2021-02-23 06:58:17'),
       (28, 'En-route to Origin Facility', 'EN_ROUTE_TO_ORIGIN_FACILITY', 0, 0, '2021-03-16 07:43:51',
        '2021-03-18 07:10:47'),
       (29, 'RTS at Origin Facility', 'RTS_AT_ORIGIN_FACILITY', 0, 0, '2021-04-25 09:04:07', '2021-04-28 06:53:33'),
       (30, 'Handed Over to Linehaul', 'HANDED_OVER_TO_LH', 0, 0, '2021-06-24 08:55:01', '2021-06-24 08:55:01'),
       (31, 'First Attempt Delivery Fail', 'FIRST_ATTEMPT_DELIVERY_FAIL', 0, 0, '2022-05-24 10:22:20',
        '2022-05-24 10:22:20'),
       (32, 'Parcel Lost', 'PARCEL_LOST', 0, 0, '2022-09-22 08:00:33', '2022-09-22 08:00:33'),
       (33, 'Parcel Damaged', 'PARCEL_DAMAGED', 0, 0, '2022-09-22 08:00:33', '2022-09-22 08:00:33'),
       (34, 'Sorted at Origin Facility', 'SORTED_AT_ORIGIN_FACILITY', 0, 1, '2022-10-13 07:57:53',
        '2023-01-09 10:15:16'),
       (35, 'En-route to Intermediate Origin Facility', 'EN_ROUTE_TO_INTERMEDIATE_ORIGIN_FACILITY', 0, 1,
        '2022-12-30 07:47:49', '2023-01-09 10:15:17'),
       (36, 'Arrived at Intermediate Origin Facility', 'ARRIVED_AT_INTERMEDIATE_ORIGIN_FACILITY', 0, 1,
        '2022-12-30 07:51:17', '2023-01-09 10:15:18'),
       (37, 'Handed Over to Origin Facility', 'HANDED_OVER_TO_ORIGIN_FACILITY', 0, 1, '2022-12-30 07:52:29',
        '2023-01-09 10:15:19'),
       (38, 'Returned to XB Warehouse', 'RETURNED_TO_XB_WAREHOUSE', 0, 0, '2022-12-30 07:52:29', '2023-05-10 04:10:33'),
       (39, 'Fulfillment Request Submitted', 'FULFILLMENT_REQUEST_SUBMITTED', 0, 0, '2022-12-30 07:52:29',
        '2023-05-10 04:10:37'),
       (40, 'Fulfillment Packed', 'FULFILLMENT_PACKED', 0, 0, '2022-12-30 07:52:29', '2023-05-10 04:10:40'),
       (41, 'Parcel Exception', 'PARCEL_EXCEPTION', 0, 0, '2024-01-17 09:29:59', '2024-01-17 09:29:59'),
       (42, 'Parcel Disposed', 'PARCEL_DISPOSED', 0, 0, '2024-01-17 09:29:59', '2024-01-17 09:29:59'),
       (43, 'Import Started', 'IMPORT_STARTED', 0, 0, '2024-04-04 09:16:44', '2024-04-04 09:17:11'),
       (44, 'Export Started', 'EXPORT_STARTED', 0, 0, '2024-04-04 09:16:44', '2024-04-04 09:17:15'),
       (45, 'Truck Dispatched', 'TRUCK_DISPATCHED', 0, 0, '2024-08-07 09:21:31', '2024-08-08 07:51:07'),
       (46, 'Parcel Loaded Into Container', 'PARCEL_LOADED_INTO_CONTAINER', 0, 0, '2024-08-07 09:21:31',
        '2024-08-08 07:51:07'),
       (47, 'Rejected at PUDO', 'REJECTED_AT_PUDO', 0, 0, '2024-08-27 09:07:56', '2024-08-27 09:29:26'),
       (48, 'Arrived at Transit Hub', 'ARRIVED_AT_TRANSIT_HUB', 0, 0, '2025-02-12 08:35:04', '2025-02-12 08:35:04'),
       (49, 'In Transit to next Sorting Hub', 'IN_TRANSIT_TO_NEXT_SORTING_HUB', 0, 0, '2025-02-12 08:35:04',
        '2025-02-12 08:35:04'),
       (50, 'Arrived at Destination Hub', 'ARRIVED_AT_DESTINATION_HUB', 0, 0, '2025-02-12 08:35:04',
        '2025-02-12 08:35:04');

DELETE
FROM `vendor_statuses`;

INSERT INTO `vendor_statuses` (`id`, `vendor_id`, `name`, `reference_code`, `is_terminal`, `created_at`, `updated_at`)
VALUES (1, 1, 'Default', 'ORDER_STATUS_DEFAULT', 0, '2019-07-22 11:02:42', '2020-03-31 05:06:16'),
       (2, 1, 'Staging', 'STAGING', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (3, 1, 'Pending Pickup', 'PENDING_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (4, 1, 'Van En-route to Pickup', 'VAN_ENROUTE_TO_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (5, 1, 'Arrived at Sorting Hub', 'ARRIVED_AT_SORTING_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (6, 1, 'En-route to Sorting Hub', 'ENROUTE_TO_SORTING_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (7, 1, 'On Vehicle to Delivery', 'ON_VEHICLE_TO_DELIVERY', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (8, 1, 'Completed', 'COMPLETED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (9, 1, 'Pending Reschedule', 'PENDING_RESCHEDULE', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (10, 1, 'Returned to Sender', 'RETURNED_TO_SENDER', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (11, 1, 'Cancelled', 'CANCELLED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (12, 1, 'Pickup Fail', 'PICKUP_FAIL', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (13, 1, 'Arrived at Distribution Point', 'ARRIVED_AT_DISTRIBUTION_POINT', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (14, 1, 'Successful Pickup', 'SUCCESSFUL_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (15, 1, 'Successful Delivery', 'SUCCESSFUL_DELIVERY', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (16, 1, 'Pending Pickup at Distribution Point', 'PENDING_PICKUP_AT_DISTRIBUTION_POINT', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (17, 1, 'Parcel Weight', 'PARCEL_WEIGHT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (18, 1, 'Crossborder Transit', 'CROSS_BORDER_TRANSIT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (19, 1, 'Customs Cleared', 'CUSTOMS_CLEARED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (20, 1, 'Customs Held', 'CUSTOMS_HELD', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (21, 1, 'RTS Triggered', 'RTS_TRIGGERED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (22, 1, 'On Vehicle for Delivery RTS', 'ON_VEHICLE_FOR_DELIVERY_RTS', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (23, 1, 'First Attempt Delivery Fail', 'FIRST_ATTEMPT_DELIVERY_FAIL', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (24, 1, 'Transferred to 3PL', 'TRANSFERRED_TO_3PL', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (25, 1, 'Arrived at Origin Hub', 'ARRIVED_AT_ORIGIN_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (26, 1, 'Parcel Rejected at Pickup Location', 'PARCEL_REJECTED_AT_PICKUP_LOCATION', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (27, 2, 'Default', 'ORDER_STATUS_DEFAULT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (28, 2, 'Staging', 'STAGING', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (29, 2, 'Pending Pickup', 'PENDING_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (30, 2, 'Van En-route to Pickup', 'VAN_ENROUTE_TO_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (31, 2, 'Arrived at Sorting Hub', 'ARRIVED_AT_SORTING_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (32, 2, 'En-route to Sorting Hub', 'ENROUTE_TO_SORTING_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (33, 2, 'On Vehicle to Delivery', 'ON_VEHICLE_TO_DELIVERY', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (34, 2, 'Completed', 'COMPLETED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (35, 2, 'Pending Reschedule', 'PENDING_RESCHEDULE', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (36, 2, 'Returned to Sender', 'RETURNED_TO_SENDER', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (37, 2, 'Cancelled', 'CANCELLED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (38, 2, 'Pickup Fail', 'PICKUP_FAIL', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (39, 2, 'Arrived at Distribution Point', 'ARRIVED_AT_DISTRIBUTION_POINT', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (40, 2, 'Successful Pickup', 'SUCCESSFUL_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (41, 2, 'Successful Delivery', 'SUCCESSFUL_DELIVERY', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (42, 2, 'Pending Pickup at Distribution Point', 'PENDING_PICKUP_AT_DISTRIBUTION_POINT', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (43, 2, 'Parcel Weight', 'PARCEL_WEIGHT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (44, 2, 'Crossborder Transit', 'CROSS_BORDER_TRANSIT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (45, 2, 'Customs Cleared', 'CUSTOMS_CLEARED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (46, 2, 'Customs Held', 'CUSTOMS_HELD', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (47, 2, 'RTS Triggered', 'RTS_TRIGGERED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (48, 2, 'On Vehicle for Delivery RTS', 'ON_VEHICLE_FOR_DELIVERY_RTS', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (49, 2, 'First Attempt Delivery Fail', 'FIRST_ATTEMPT_DELIVERY_FAIL', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (50, 2, 'Transferred to 3PL', 'TRANSFERRED_TO_3PL', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (51, 2, 'Arrived at Origin Hub', 'ARRIVED_AT_ORIGIN_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (52, 2, 'Parcel Rejected at Pickup Location', 'PARCEL_REJECTED_AT_PICKUP_LOCATION', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (53, 3, 'Default', 'ORDER_STATUS_DEFAULT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (54, 3, 'Staging', 'STAGING', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (55, 3, 'Pending Pickup', 'PENDING_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (56, 3, 'Van En-route to Pickup', 'VAN_ENROUTE_TO_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (57, 3, 'Arrived at Sorting Hub', 'ARRIVED_AT_SORTING_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (58, 3, 'En-route to Sorting Hub', 'ENROUTE_TO_SORTING_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (59, 3, 'On Vehicle to Delivery', 'ON_VEHICLE_TO_DELIVERY', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (60, 3, 'Completed', 'COMPLETED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (61, 3, 'Pending Reschedule', 'PENDING_RESCHEDULE', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (62, 3, 'Returned to Sender', 'RETURNED_TO_SENDER', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (63, 3, 'Cancelled', 'CANCELLED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (64, 3, 'Pickup Fail', 'PICKUP_FAIL', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (65, 3, 'Arrived at Distribution Point', 'ARRIVED_AT_DISTRIBUTION_POINT', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (66, 3, 'Successful Pickup', 'SUCCESSFUL_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (67, 3, 'Successful Delivery', 'SUCCESSFUL_DELIVERY', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (68, 3, 'Pending Pickup at Distribution Point', 'PENDING_PICKUP_AT_DISTRIBUTION_POINT', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (69, 3, 'Parcel Weight', 'PARCEL_WEIGHT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (70, 3, 'Crossborder Transit', 'CROSS_BORDER_TRANSIT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (71, 3, 'Customs Cleared', 'CUSTOMS_CLEARED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (72, 3, 'Customs Held', 'CUSTOMS_HELD', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (73, 3, 'RTS Triggered', 'RTS_TRIGGERED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (74, 3, 'On Vehicle for Delivery RTS', 'ON_VEHICLE_FOR_DELIVERY_RTS', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (75, 3, 'First Attempt Delivery Fail', 'FIRST_ATTEMPT_DELIVERY_FAIL', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (76, 3, 'Transferred to 3PL', 'TRANSFERRED_TO_3PL', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (77, 3, 'Arrived at Origin Hub', 'ARRIVED_AT_ORIGIN_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (78, 3, 'Parcel Rejected at Pickup Location', 'PARCEL_REJECTED_AT_PICKUP_LOCATION', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (79, 4, 'Default', 'ORDER_STATUS_DEFAULT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (80, 4, 'Staging', 'STAGING', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (81, 4, 'Pending Pickup', 'PENDING_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (82, 4, 'Van En-route to Pickup', 'VAN_ENROUTE_TO_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (83, 4, 'Arrived at Sorting Hub', 'ARRIVED_AT_SORTING_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (84, 4, 'En-route to Sorting Hub', 'ENROUTE_TO_SORTING_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (85, 4, 'On Vehicle to Delivery', 'ON_VEHICLE_TO_DELIVERY', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (86, 4, 'Completed', 'COMPLETED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (87, 4, 'Pending Reschedule', 'PENDING_RESCHEDULE', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (88, 4, 'Returned to Sender', 'RETURNED_TO_SENDER', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (89, 4, 'Cancelled', 'CANCELLED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (90, 4, 'Pickup Fail', 'PICKUP_FAIL', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (91, 4, 'Arrived at Distribution Point', 'ARRIVED_AT_DISTRIBUTION_POINT', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (92, 4, 'Successful Pickup', 'SUCCESSFUL_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (93, 4, 'Successful Delivery', 'SUCCESSFUL_DELIVERY', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (94, 4, 'Pending Pickup at Distribution Point', 'PENDING_PICKUP_AT_DISTRIBUTION_POINT', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (95, 4, 'Parcel Weight', 'PARCEL_WEIGHT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (96, 4, 'Crossborder Transit', 'CROSS_BORDER_TRANSIT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (97, 4, 'Customs Cleared', 'CUSTOMS_CLEARED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (98, 4, 'Customs Held', 'CUSTOMS_HELD', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (99, 4, 'RTS Triggered', 'RTS_TRIGGERED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (100, 4, 'On Vehicle for Delivery RTS', 'ON_VEHICLE_FOR_DELIVERY_RTS', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (101, 4, 'First Attempt Delivery Fail', 'FIRST_ATTEMPT_DELIVERY_FAIL', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (102, 4, 'Transferred to 3PL', 'TRANSFERRED_TO_3PL', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (103, 4, 'Arrived at Origin Hub', 'ARRIVED_AT_ORIGIN_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (104, 4, 'Parcel Rejected at Pickup Location', 'PARCEL_REJECTED_AT_PICKUP_LOCATION', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (105, 5, 'Default', 'ORDER_STATUS_DEFAULT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (106, 5, 'Staging', 'STAGING', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (107, 5, 'Pending Pickup', 'PENDING_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (108, 5, 'Van En-route to Pickup', 'VAN_ENROUTE_TO_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (109, 5, 'Arrived at Sorting Hub', 'ARRIVED_AT_SORTING_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (110, 5, 'En-route to Sorting Hub', 'ENROUTE_TO_SORTING_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (111, 5, 'On Vehicle to Delivery', 'ON_VEHICLE_TO_DELIVERY', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (112, 5, 'Completed', 'COMPLETED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (113, 5, 'Pending Reschedule', 'PENDING_RESCHEDULE', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (114, 5, 'Returned to Sender', 'RETURNED_TO_SENDER', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (115, 5, 'Cancelled', 'CANCELLED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (116, 5, 'Pickup Fail', 'PICKUP_FAIL', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (117, 5, 'Arrived at Distribution Point', 'ARRIVED_AT_DISTRIBUTION_POINT', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (118, 5, 'Successful Pickup', 'SUCCESSFUL_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (119, 5, 'Successful Delivery', 'SUCCESSFUL_DELIVERY', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (120, 5, 'Pending Pickup at Distribution Point', 'PENDING_PICKUP_AT_DISTRIBUTION_POINT', 0,
        '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (121, 5, 'Parcel Weight', 'PARCEL_WEIGHT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (122, 5, 'Crossborder Transit', 'CROSS_BORDER_TRANSIT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (123, 5, 'Customs Cleared', 'CUSTOMS_CLEARED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (124, 5, 'Customs Held', 'CUSTOMS_HELD', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (125, 5, 'RTS Triggered', 'RTS_TRIGGERED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (126, 5, 'On Vehicle for Delivery RTS', 'ON_VEHICLE_FOR_DELIVERY_RTS', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (127, 5, 'First Attempt Delivery Fail', 'FIRST_ATTEMPT_DELIVERY_FAIL', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (128, 5, 'Transferred to 3PL', 'TRANSFERRED_TO_3PL', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (129, 5, 'Arrived at Origin Hub', 'ARRIVED_AT_ORIGIN_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (130, 5, 'Parcel Rejected at Pickup Location', 'PARCEL_REJECTED_AT_PICKUP_LOCATION', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (131, 6, 'Default', 'ORDER_STATUS_DEFAULT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (132, 6, 'Staging', 'STAGING', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (133, 6, 'Pending Pickup', 'PENDING_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (134, 6, 'Van En-route to Pickup', 'VAN_ENROUTE_TO_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (135, 6, 'Arrived at Sorting Hub', 'ARRIVED_AT_SORTING_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (136, 6, 'En-route to Sorting Hub', 'ENROUTE_TO_SORTING_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (137, 6, 'On Vehicle to Delivery', 'ON_VEHICLE_TO_DELIVERY', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (138, 6, 'Completed', 'COMPLETED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (139, 6, 'Pending Reschedule', 'PENDING_RESCHEDULE', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (140, 6, 'Returned to Sender', 'RETURNED_TO_SENDER', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (141, 6, 'Cancelled', 'CANCELLED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (142, 6, 'Pickup Fail', 'PICKUP_FAIL', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (143, 6, 'Arrived at Distribution Point', 'ARRIVED_AT_DISTRIBUTION_POINT', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (144, 6, 'Successful Pickup', 'SUCCESSFUL_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (145, 6, 'Successful Delivery', 'SUCCESSFUL_DELIVERY', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (146, 6, 'Pending Pickup at Distribution Point', 'PENDING_PICKUP_AT_DISTRIBUTION_POINT', 0,
        '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (147, 6, 'Parcel Weight', 'PARCEL_WEIGHT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (148, 6, 'Crossborder Transit', 'CROSS_BORDER_TRANSIT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (149, 6, 'Customs Cleared', 'CUSTOMS_CLEARED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (150, 6, 'Customs Held', 'CUSTOMS_HELD', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (151, 6, 'RTS Triggered', 'RTS_TRIGGERED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (152, 6, 'On Vehicle for Delivery RTS', 'ON_VEHICLE_FOR_DELIVERY_RTS', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (153, 6, 'First Attempt Delivery Fail', 'FIRST_ATTEMPT_DELIVERY_FAIL', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (154, 6, 'Transferred to 3PL', 'TRANSFERRED_TO_3PL', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (155, 6, 'Arrived at Origin Hub', 'ARRIVED_AT_ORIGIN_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (156, 6, 'Parcel Rejected at Pickup Location', 'PARCEL_REJECTED_AT_PICKUP_LOCATION', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (157, 8, 'Default', 'ORDER_STATUS_DEFAULT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (158, 8, 'Staging', 'STAGING', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (159, 8, 'Pending Pickup', 'PENDING_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (160, 8, 'Van En-route to Pickup', 'VAN_ENROUTE_TO_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (161, 8, 'Arrived at Sorting Hub', 'ARRIVED_AT_SORTING_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (162, 8, 'En-route to Sorting Hub', 'ENROUTE_TO_SORTING_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (163, 8, 'On Vehicle to Delivery', 'ON_VEHICLE_TO_DELIVERY', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (164, 8, 'Completed', 'COMPLETED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (165, 8, 'Pending Reschedule', 'PENDING_RESCHEDULE', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (166, 8, 'Returned to Sender', 'RETURNED_TO_SENDER', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (167, 8, 'Cancelled', 'CANCELLED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (168, 8, 'Pickup Fail', 'PICKUP_FAIL', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (169, 8, 'Arrived at Distribution Point', 'ARRIVED_AT_DISTRIBUTION_POINT', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (170, 8, 'Successful Pickup', 'SUCCESSFUL_PICKUP', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (171, 8, 'Successful Delivery', 'SUCCESSFUL_DELIVERY', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (172, 8, 'Pending Pickup at Distribution Point', 'PENDING_PICKUP_AT_DISTRIBUTION_POINT', 0,
        '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (173, 8, 'Parcel Weight', 'PARCEL_WEIGHT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (174, 8, 'Crossborder Transit', 'CROSS_BORDER_TRANSIT', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (175, 8, 'Customs Cleared', 'CUSTOMS_CLEARED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (176, 8, 'Customs Held', 'CUSTOMS_HELD', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (177, 8, 'RTS Triggered', 'RTS_TRIGGERED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (178, 8, 'On Vehicle for Delivery RTS', 'ON_VEHICLE_FOR_DELIVERY_RTS', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (179, 8, 'First Attempt Delivery Fail', 'FIRST_ATTEMPT_DELIVERY_FAIL', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (180, 8, 'Transferred to 3PL', 'TRANSFERRED_TO_3PL', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (181, 8, 'Arrived at Origin Hub', 'ARRIVED_AT_ORIGIN_HUB', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (182, 8, 'Parcel Rejected at Pickup Location', 'PARCEL_REJECTED_AT_PICKUP_LOCATION', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (183, 7, 'Flight Scheduled', 'YKF_FLIGHT_SCHEDULED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (184, 7, 'Export Cleared', 'YKF_EXPORT_CLEARED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (185, 7, 'Flight Departed', 'YKF_FLIGHT_DEPARTED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (186, 7, 'Flight Arrived', 'YKF_FLIGHT_ARRIVED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (187, 7, 'Customs Cleared', 'YKF_CUSTOMS_CLEARED', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (188, 7, 'Customs Held', 'YKF_CUSTOMS_HELD', 0, '2019-07-22 11:02:42', '2019-07-22 11:02:42'),
       (189, 7, 'Handed Over to Last Mile', 'YKF_HANDED_OVER_TO_LAST_MILE', 0, '2019-07-22 11:02:42',
        '2019-07-22 11:02:42'),
       (190, 46, 'Electronic Notification Received', 'Electronic Notification Received', 0, '2020-10-01 06:38:34',
        '2020-10-01 06:39:41'),
       (192, 46, 'Arrived at Facility', 'Arrived at Facility', 0, '2020-10-01 06:38:34', '2020-10-01 06:39:41'),
       (194, 46, 'Departed Origin Facility', 'Departed Origin Facility', 1, '2020-10-01 06:38:34',
        '2020-10-01 06:39:41'),
       (196, 46, 'Pending Pickup', 'Pending Pickup', 0, '2020-10-01 06:38:34', '2020-10-01 06:39:41'),
       (198, 46, 'Export Clearance Succeed', 'Export Clearance Succeed', 0, '2020-10-01 06:38:34',
        '2020-10-01 06:39:41'),
       (200, 46, 'The Flight Schedule', 'The Flight Schedule', 0, '2020-10-01 06:38:34', '2020-10-01 06:39:41'),
       (202, 46, 'Flight Arrived', 'Flight Arrived', 0, '2020-10-01 06:38:34', '2020-10-01 06:39:41'),
       (204, 46, 'Customs Collection', 'Customs Collection', 0, '2020-10-01 06:38:34', '2020-10-01 06:39:41'),
       (206, 46, 'Import Clearance Succeed', 'Import Clearance Succeed', 0, '2020-10-01 06:38:34',
        '2020-10-01 06:39:41'),
       (208, 46, 'Parcel Measurements Update', 'Parcel Measurements Update', 0, '2020-10-01 06:38:34',
        '2020-10-01 06:39:41'),
       (210, 46, 'Arrived at Sorting Hub', 'Arrived at Sorting Hub', 0, '2020-10-01 06:38:34', '2020-10-01 06:39:41'),
       (212, 46, 'On Vehicle for Delivery', 'On Vehicle for Delivery', 0, '2020-10-01 06:38:34', '2020-10-01 06:39:41'),
       (214, 46, 'Completed', 'Completed', 0, '2020-10-01 06:38:34', '2020-10-01 06:39:41'),
       (216, 46, 'Successful Delivery', 'Successful Delivery', 0, '2020-10-01 06:38:34', '2020-10-01 06:39:41'),
       (219, 1, 'On Hold', 'ON_HOLD', 0, '2019-07-22 11:02:42', '2021-03-02 03:48:10'),
       (220, 2, 'On Hold', 'ON_HOLD', 0, '2020-06-19 08:50:13', '2020-06-19 08:50:13'),
       (221, 3, 'On Hold', 'ON_HOLD', 0, '2020-06-19 08:50:13', '2020-06-19 08:50:13'),
       (222, 4, 'On Hold', 'ON_HOLD', 0, '2020-06-19 08:50:13', '2020-06-19 08:50:13'),
       (223, 5, 'On Hold', 'ON_HOLD', 0, '2020-06-19 08:50:13', '2020-06-19 08:50:13'),
       (224, 6, 'On Hold', 'ON_HOLD', 0, '2020-06-19 08:50:13', '2020-06-19 08:50:13'),
       (225, 1, 'Parcel Lost', 'PARCEL_LOST', 0, '2022-09-22 03:55:27', '2022-09-22 03:55:27'),
       (226, 2, 'Parcel Lost', 'PARCEL_LOST', 0, '2022-09-22 03:55:27', '2022-09-22 03:55:27'),
       (227, 3, 'Parcel Lost', 'PARCEL_LOST', 0, '2022-09-22 03:55:27', '2022-09-22 03:55:27'),
       (228, 4, 'Parcel Lost', 'PARCEL_LOST', 0, '2022-09-22 03:55:27', '2022-09-22 03:55:27'),
       (229, 5, 'Parcel Lost', 'PARCEL_LOST', 0, '2022-09-22 03:55:27', '2022-09-22 03:55:27'),
       (230, 6, 'Parcel Lost', 'PARCEL_LOST', 0, '2022-09-22 03:55:27', '2022-09-22 03:55:27'),
       (231, 8, 'Parcel Lost', 'PARCEL_LOST', 0, '2022-09-22 03:55:27', '2022-09-22 03:55:27'),
       (232, 1, 'Parcel Damaged', 'PARCEL_DAMAGED', 0, '2022-09-22 04:05:10', '2022-09-22 04:05:10'),
       (233, 2, 'Parcel Damaged', 'PARCEL_DAMAGED', 0, '2022-09-22 04:05:10', '2022-09-22 04:05:10'),
       (234, 3, 'Parcel Damaged', 'PARCEL_DAMAGED', 0, '2022-09-22 04:05:10', '2022-09-22 04:05:10'),
       (235, 4, 'Parcel Damaged', 'PARCEL_DAMAGED', 0, '2022-09-22 04:05:10', '2022-09-22 04:05:10'),
       (236, 5, 'Parcel Damaged', 'PARCEL_DAMAGED', 0, '2022-09-22 04:05:10', '2022-09-22 04:05:10'),
       (237, 6, 'Parcel Damaged', 'PARCEL_DAMAGED', 0, '2022-09-22 04:05:10', '2022-09-22 04:05:10'),
       (238, 8, 'Parcel Damaged', 'PARCEL_DAMAGED', 0, '2022-09-22 04:05:10', '2022-09-22 04:05:10'),
       (239, 1, 'Sorted at Origin Facility', 'SORTED_AT_ORIGIN_FACILITY', 0, '2022-10-13 07:57:53',
        '2022-10-13 07:57:53'),
       (240, 2, 'Sorted at Origin Facility', 'SORTED_AT_ORIGIN_FACILITY', 0, '2022-10-13 07:57:53',
        '2022-10-13 07:57:53'),
       (241, 3, 'Sorted at Origin Facility', 'SORTED_AT_ORIGIN_FACILITY', 0, '2022-10-13 07:57:53',
        '2022-10-13 07:57:53'),
       (242, 4, 'Sorted at Origin Facility', 'SORTED_AT_ORIGIN_FACILITY', 0, '2022-10-13 07:57:53',
        '2022-10-13 07:57:53'),
       (243, 5, 'Sorted at Origin Facility', 'SORTED_AT_ORIGIN_FACILITY', 0, '2022-10-13 07:57:53',
        '2022-10-13 07:57:53'),
       (244, 6, 'Sorted at Origin Facility', 'SORTED_AT_ORIGIN_FACILITY', 0, '2022-10-13 07:57:53',
        '2022-10-13 07:57:53'),
       (245, 8, 'Sorted at Origin Facility', 'SORTED_AT_ORIGIN_FACILITY', 0, '2022-10-13 07:57:53',
        '2022-10-13 07:57:53'),
       (246, 1, 'Rejected at PUDO', 'REJECTED_AT_PUDO', 0, '2024-08-27 09:07:56', '2024-08-27 09:07:56'),
       (247, 2, 'Rejected at PUDO', 'REJECTED_AT_PUDO', 0, '2024-08-27 09:07:56', '2024-08-27 09:07:56'),
       (248, 3, 'Rejected at PUDO', 'REJECTED_AT_PUDO', 0, '2024-08-27 09:07:56', '2024-08-27 09:07:56'),
       (249, 4, 'Rejected at PUDO', 'REJECTED_AT_PUDO', 0, '2024-08-27 09:07:56', '2024-08-27 09:07:56'),
       (250, 5, 'Rejected at PUDO', 'REJECTED_AT_PUDO', 0, '2024-08-27 09:07:56', '2024-08-27 09:07:56'),
       (251, 6, 'Rejected at PUDO', 'REJECTED_AT_PUDO', 0, '2024-08-27 09:07:56', '2024-08-27 09:07:56'),
       (252, 8, 'Rejected at PUDO', 'REJECTED_AT_PUDO', 0, '2024-08-27 09:07:56', '2024-08-27 09:07:56'),
       (253, 5918, 'Linehaul Scheduled', 'LINEHAUL_SCHEDULED', 0, '2025-02-13 08:01:13', '2025-02-13 08:01:13'),
       (254, 5918, 'Handed Over to Last Mile', 'HANDED_OVER_TO_LAST_MILE', 0, '2025-02-13 08:01:13',
        '2025-02-13 08:01:13'),
       (255, 5918, 'Customs Cleared', 'CUSTOMS_CLEARED', 0, '2025-02-13 08:01:13', '2025-02-13 08:01:13'),
       (256, 5918, 'Customs Held', 'CUSTOMS_HELD', 0, '2025-02-13 08:01:13', '2025-02-13 08:01:13'),
       (257, 5918, 'Linehaul Departed', 'LINEHAUL_DEPARTED', 0, '2025-02-13 08:01:13', '2025-02-13 08:01:13'),
       (258, 5918, 'Linehaul Arrived', 'LINEHAUL_ARRIVED', 0, '2025-02-13 08:01:13', '2025-02-13 08:01:13'),
       (259, 5918, 'Parcel Exception', 'PARCEL_EXCEPTION', 0, '2025-02-13 08:01:13', '2025-02-13 08:01:13'),
       (260, 5918, 'Parcel Disposed', 'PARCEL_DISPOSED', 0, '2025-02-13 08:01:13', '2025-02-13 08:01:13'),
       (261, 5918, 'Returned to XB Warehouse', 'RETURNED_TO_XB_WAREHOUSE', 0, '2025-02-13 08:01:13',
        '2025-02-13 08:01:13'),
       (262, 5918, 'Import Started', 'IMPORT_STARTED', 0, '2025-02-13 08:01:13', '2025-02-13 08:01:13'),
       (263, 5918, 'Completed', 'COMPLETED', 0, '2025-02-13 08:01:13', '2025-02-13 08:01:13'),
       (264, 5918, 'On Vehicle for Delivery', 'ON_VEHICLE_FOR_DELIVERY', 0, '2025-02-13 08:01:13',
        '2025-02-13 08:01:13'),
       (265, 5918, 'Pending Reschedule', 'PENDING_RESCHEDULE', 0, '2025-02-13 08:01:13', '2025-02-13 08:01:13'),
       (266, 5918, 'Arrived at Transit Hub', 'ARRIVED_AT_TRANSIT_HUB', 0, '2025-02-13 08:01:13', '2025-02-13 08:01:13'),
       (267, 5918, 'In Transit to Next Sorting Hub', 'IN_TRANSIT_TO_NEXT_SORTING_HUB', 0, '2025-02-13 08:01:13',
        '2025-02-13 08:01:13'),
       (268, 5918, 'Pending Pickup', 'PENDING_PICKUP', 0, '2025-02-13 08:01:13', '2025-02-13 08:01:13'),
       (269, 5918, 'Parcel Lost', 'PARCEL_LOST', 0, '2025-02-13 08:01:13', '2025-02-13 08:01:13'),
       (270, 5918, 'RTS Triggered', 'RTS_TRIGGERED', 0, '2025-02-13 08:01:13', '2025-02-13 08:01:13'),
       (271, 5918, 'Arrived at Destination Hub', 'ARRIVED_AT_DESTINATION_HUB', 0, '2025-02-13 08:01:13',
        '2025-02-13 08:01:13'),
       (272, 65, 'Linehaul Scheduled', 'LINEHAUL_SCHEDULED', 0, '2025-02-18 04:02:37', '2025-02-18 04:02:37'),
       (273, 65, 'Handed Over to Last Mile', 'HANDED_OVER_TO_LAST_MILE', 0, '2025-02-18 04:02:37',
        '2025-02-18 04:02:37'),
       (274, 65, 'Customs Cleared', 'CUSTOMS_CLEARED', 0, '2025-02-18 04:02:37', '2025-02-18 04:02:37'),
       (275, 65, 'Customs Held', 'CUSTOMS_HELD', 0, '2025-02-18 04:02:37', '2025-02-18 04:02:37'),
       (276, 65, 'Linehaul Departed', 'LINEHAUL_DEPARTED', 0, '2025-02-18 04:02:37', '2025-02-18 04:02:37'),
       (277, 65, 'Linehaul Arrived', 'LINEHAUL_ARRIVED', 0, '2025-02-18 04:02:37', '2025-02-18 04:02:37'),
       (278, 65, 'Parcel Exception', 'PARCEL_EXCEPTION', 0, '2025-02-18 04:02:37', '2025-02-18 04:02:37'),
       (279, 65, 'Parcel Disposed', 'PARCEL_DISPOSED', 0, '2025-02-18 04:02:37', '2025-02-18 04:02:37'),
       (280, 65, 'Returned to XB Warehouse', 'RETURNED_TO_XB_WAREHOUSE', 0, '2025-02-18 04:02:37',
        '2025-02-18 04:02:37'),
       (281, 65, 'Import Started', 'IMPORT_STARTED', 0, '2025-02-18 04:02:37', '2025-02-18 04:02:37');

