INSERT INTO `parcels` (`id`, `service_id`, `product_id`, `origin_vendor_id`, `partner_id`, `shipper_id`, `tracking_id`,
                       `ref_tracking_id`, `parent_id`, `source_order_id`, `partner_unique_key`, `current_vendor_id`,
                       `current_internal_status_id`, `current_event_time`, `source`, `source_platform_id`, `type`,
                       `actual_weight`, `from_name`, `from_address_line1`, `from_address_line2`, `from_address_line3`,
                       `from_address_line4`, `from_city`, `from_state_province`, `from_country_code`, `from_postcode`,
                       `from_contact_number`, `from_contact_email`, `to_name`, `to_address_line1`, `to_address_line2`,
                       `to_address_line3`, `to_address_line4`, `to_city`, `to_state_province`, `to_country_code`,
                       `to_postcode`, `to_contact_number`, `to_contact_email`, `return_address`, `metadata`,
                       `pickup_info`, `delivery_info`, `created_at`, `updated_at`)
VALUES (*********, 17084, 38615, 7, 6, 6334113, 'FPLV2OKYTT5X5RY5CSJJ', NULL, NULL, 'ITTEST211033', NULL, NULL, NULL,
        NULL, 8, NULL, 5, NULL, 'Sender', 'AD1', NULL, NULL, NULL, NULL, NULL, 'SG', '067112', '+123551',
        '<EMAIL>', 'Reตึก 55 อาคารพนาสินเพลสceiver',
        '55/6 ตึก 55 อาคารพนาสินเพลส 55/6 ตึก 55 อาคารพนาสินเพลส 55 อาคารพนาสินเพลส 55 อาคารพนาสินเพลส 55 อาคารพนาสินเพลส 55 อาคารพนาสินเพลส 55 อาคารพนาสินเพลส 55 อาคารพนาสินเพลส 55 อาคารพนาสินเพลส 55 อาคาร2, 40300 Shah Alam, Selango',
        'AD02', 'AD03', 'AD04', 'cityTo', 'stateTest', 'MY', '56311', '+625218', '<EMAIL>', NULL,
        '{\"shipper_submitted_weight\":1,\"shipper_submitted_weight_unit\":null,\"weight\":1,\"actual_weight\":null,\"battery_packing\":0,\"battery_type\":0,\"is_relabel\":false,\"is_high_value\":false,\"warehouse_code\":null,\"routing_type\":1,\"value\":119.88,\"customs_currency\":\"VND\",\"tax_id\":null,\"invoice_url\":null,\"trade_terms\":null,\"customs_description\":\"This is a box test\",\"customs_native_description\":null,\"customs_description_translated\":\"This is a box test\",\"customs_native_description_translated\":\"这是一个盒子测试\",\"hs_code\":null,\"origin_country\":null,\"metadata\":\"\",\"quantity\":1,\"shipper_submitted_dimensions\":{\"length\":1,\"width\":20,\"height\":20,\"unit\":null},\"status\":1,\"sku_id\":null,\"lazada\":null,\"b2b_bundle\":null,\"bag_movement\":null,\"battery\":null,\"consignee_company\":null,\"locker_pick_up\":null,\"shipper_address\":null,\"shipper_name\":null,\"shipper_contact\":null,\"manifest_weight\":null,\"goods_value\":null,\"return_label_url\":null}',
        '{\"pickup_date\":\"2025-06-15\",\"pickup_timeslot\":{\"start_time\":\"15:00\",\"end_time\":\"18:00\",\"timezone\":\"Asia/Singapore\"},\"pickup_address\":{\"name\":\"Eric\",\"address_line1\":\"70 BUKIT PANJANG RING ROAD #06-03\",\"address_line2\":null,\"address_line3\":null,\"address_line4\":null,\"city\":null,\"state_province\":null,\"country_code\":\"SG\",\"post_code\":\"679941\",\"contact_number\":\"62540935\",\"contact_email\":null,\"collection_point\":null},\"pickup_approx_volume\":4,\"pickup_instructions\":\"\"}',
        '{\"delivery_start_date\":\"2025-06-15\",\"delivery_timeslot\":{\"start_time\":\"09:00\",\"end_time\":\"22:00\",\"timezone\":\"Asia/Kuala_Lumpur\"},\"delivery_instructions\":\"\",\"cash_on_delivery\":0,\"insured_value\":0,\"allow_self_collection\":false}',
        '2025-05-28 06:55:45', '2025-05-28 06:55:46'),
       (105638265, 17084, 38615, 7, 6, 6334113, 'FPLDUOIQQ0BYVYO7PQZRHHYF', 'IT116873453', *********, 'ITTEST211033',
        NULL, NULL, NULL, NULL, 8, NULL, 4, NULL, 'Sender', 'AD1', NULL, NULL, NULL, NULL, NULL, 'SG', '067112',
        '+123551', NULL, 'IT test', 'add1 ad, cc', NULL, NULL, NULL, NULL, NULL, 'MY', NULL, '**********', NULL, NULL,
        '{\"shipper_submitted_weight\":null,\"shipper_submitted_weight_unit\":null,\"weight\":null,\"actual_weight\":null,\"battery_packing\":0,\"battery_type\":0,\"is_relabel\":false,\"warehouse_code\":null,\"routing_type\":0,\"value\":105,\"customs_currency\":\"VND\",\"tax_id\":null,\"invoice_url\":null,\"trade_terms\":null,\"customs_description\":\"This is items second test, This is item second test, This is item third test\",\"customs_native_description\":null,\"customs_description_translated\":\"This is items second test, This is item second test, This is item third test\",\"customs_native_description_translated\":\"这是项目的第二次测试，这是项目的第二次测试，这是项目的第三次测试\",\"hs_code\":null,\"origin_country\":null,\"metadata\":\"\",\"quantity\":105,\"shipper_submitted_dimensions\":null,\"status\":1,\"sku_id\":null,\"lazada\":null,\"b2b_bundle\":null,\"bag_movement\":null,\"battery\":null,\"consignee_company\":null,\"locker_pick_up\":null,\"shipper_address\":null,\"shipper_name\":null,\"shipper_contact\":null,\"manifest_weight\":null,\"goods_value\":null,\"return_label_url\":null}',
        NULL, NULL, '2025-05-28 06:55:45', '2025-05-28 06:55:45');

INSERT INTO `shipments` (`id`, `type`, `reference_id`, `shipper_id`, `vendor_id`, `consignee_id`, `origin_port`,
                         `destination_port`, `origin_country`, `destination_country`, `etd`, `eta`, `closed_at`,
                         `actual_vendor_inbound_date`, `current_event_id`, `status`, `no_of_bags`, `no_of_parcels`,
                         `no_of_parcels_expected`, `no_of_parcels_received`, `goods_category`, `gross_weight`,
                         `chargeable_weight`, `wh_gross_weight`, `wh_chargeable_weight`, `no_of_inbounded_parcels`,
                         `ready_to_assign`, `actual_shipment_weight`, `actual_shipment_cbm`, `overall_uplift_status`,
                         `metadata`, `created_at`, `updated_at`)
VALUES (379489, 3, '828-17111234', NULL, NULL, NULL, 'SIN', 'MYY', 'SG', 'MY', '2025-05-30 03:30:00',
        '2025-05-31 03:30:00', NULL, NULL, NULL, 0, 1, 3, NULL, NULL, NULL, 11, 11, 10, 10, 0, 0, NULL, NULL, NULL,
        '{\"booking_cbm\":null,\"booking_weight\":null,\"eta_tz\":\"Asia/Ho_Chi_Minh\",\"etd_tz\":\"Asia/Ho_Chi_Minh\",\"freight_type\":2,\"notes\":\"\",\"vessel_no\":\"123456\"}',
        '2025-05-29 03:30:57', '2025-05-29 03:33:49');


INSERT INTO `shipment_parcels` (`shipment_id`, `parcel_id`, `parcel_parent_id`, `requested_tracking_id`,
                                `requested_parent_tracking_id`, `bag_id`, `requested_bag_tracking_id`, `status`,
                                `error_type`, `error_metadata`, `origin_verification_status`,
                                `origin_verification_error_code`, `origin_verification_metadata`, `created_at`,
                                `updated_at`)
VALUES (379489, NULL, NULL, NULL, NULL, 1922221, 'FPLV2OKYTT5X5RY5CSJJ', 1, NULL, NULL, NULL, NULL, NULL,
        '2025-05-29 03:33:49', '2025-05-29 03:33:49'),
       (379489, 105638265, NULL, 'FPLDUOIQQ0BYVYO7PQZRHHYF', NULL, 1922221, 'FPLV2OKYTT5X5RY5CSJJ', 1, 14, NULL, NULL,
        NULL, NULL, '2025-05-29 03:33:49', '2025-05-29 03:33:49');
