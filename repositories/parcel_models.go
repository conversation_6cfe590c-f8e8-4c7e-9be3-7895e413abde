package repositories

import (
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	parcelConst "git.ninjavan.co/3pl/configs/parcel"
	"git.ninjavan.co/3pl/models"
)

var (
	// parcelWhitelistColumns uses for create parcel as internal using purpose
	// the addresses are not required to provide
	parcelWhitelistColumns = boil.Whitelist(
		models.ParcelColumns.ServiceID,
		models.ParcelColumns.ProductID,
		models.ParcelColumns.TrackingID,
		models.ParcelColumns.RefTrackingID,
		models.ParcelColumns.PartnerID,
		models.ParcelColumns.OriginVendorID,
		models.ParcelColumns.Source,
		models.ParcelColumns.Type,
		models.ParcelColumns.ShipperID,
		models.ParcelColumns.SourceOrderID,
		models.ParcelColumns.SourcePlatformID,
		models.ParcelColumns.Metadata,
		models.ParcelColumns.ActualWeight,
	)

	// parcelOrderWhitelist uses for create a parcel as an order
	parcelOrderWhitelist = boil.Whitelist(
		models.ParcelColumns.ServiceID,
		models.ParcelColumns.ProductID,
		models.ParcelColumns.TrackingID,
		models.ParcelColumns.RefTrackingID,
		models.ParcelColumns.ParentID,
		models.ParcelColumns.PartnerID,
		models.ParcelColumns.PartnerUniqueKey,
		models.ParcelColumns.OriginVendorID,
		models.ParcelColumns.ShipperID,
		models.ParcelColumns.Source,
		models.ParcelColumns.Type,
		models.ParcelColumns.SourceOrderID,
		models.ParcelColumns.Metadata,
		models.ParcelColumns.ActualWeight,
		models.ParcelColumns.PickupInfo,

		models.ParcelColumns.FromName,
		models.ParcelColumns.FromAddressLine1,
		models.ParcelColumns.FromAddressLine2,
		models.ParcelColumns.FromAddressLine3,
		models.ParcelColumns.FromAddressLine4,
		models.ParcelColumns.FromCity,
		models.ParcelColumns.FromStateProvince,
		models.ParcelColumns.FromCountryCode,
		models.ParcelColumns.FromPostcode,
		models.ParcelColumns.FromContactNumber,
		models.ParcelColumns.FromContactEmail,

		models.ParcelColumns.ToName,
		models.ParcelColumns.ToAddressLine1,
		models.ParcelColumns.ToAddressLine2,
		models.ParcelColumns.ToAddressLine3,
		models.ParcelColumns.ToAddressLine4,
		models.ParcelColumns.ToCity,
		models.ParcelColumns.ToStateProvince,
		models.ParcelColumns.ToCountryCode,
		models.ParcelColumns.ToPostcode,
		models.ParcelColumns.ToContactNumber,
		models.ParcelColumns.ToContactEmail,

		models.ParcelColumns.ReturnAddress,
		models.ParcelColumns.DeliveryInfo,
	)

	ParcelEventUpdateWhitelist = boil.Whitelist(
		models.ParcelColumns.CurrentVendorID,
		models.ParcelColumns.CurrentEventTime,
		models.ParcelColumns.CurrentInternalStatusID,
	)

	ParcelUpdateWhiteList = boil.Whitelist(
		models.ParcelColumns.ActualWeight,
		models.ParcelColumns.Metadata,
	)

	// parcelAfterCompleteOrderCreationWhitelist uses for updating first vendor tracking ID to parcel
	parcelAfterCompleteOrderCreationWhitelist = boil.Whitelist(
		models.ParcelColumns.TrackingID,
		models.ParcelColumns.DeliveryInfo,
		models.ParcelColumns.Metadata,
	)

	// parcelAutoCompleteWhitelist uses for force-backfill parcel information feature
	parcelAutoCompleteWhitelist = boil.Whitelist(
		models.ParcelColumns.Metadata,

		models.ParcelColumns.FromName,
		models.ParcelColumns.FromAddressLine1,
		models.ParcelColumns.FromAddressLine2,
		models.ParcelColumns.FromAddressLine3,
		models.ParcelColumns.FromAddressLine4,
		models.ParcelColumns.FromCity,
		models.ParcelColumns.FromStateProvince,
		models.ParcelColumns.FromCountryCode,
		models.ParcelColumns.FromPostcode,
		models.ParcelColumns.FromContactNumber,
		models.ParcelColumns.FromContactEmail,

		models.ParcelColumns.ToName,
		models.ParcelColumns.ToAddressLine1,
		models.ParcelColumns.ToAddressLine2,
		models.ParcelColumns.ToAddressLine3,
		models.ParcelColumns.ToAddressLine4,
		models.ParcelColumns.ToCity,
		models.ParcelColumns.ToStateProvince,
		models.ParcelColumns.ToCountryCode,
		models.ParcelColumns.ToPostcode,
		models.ParcelColumns.ToContactNumber,
		models.ParcelColumns.ToContactEmail,
	)
)

type ParcelId uint

func (id ParcelId) AsQms() []qm.QueryMod {
	return []qm.QueryMod{models.ParcelWhere.ID.EQ(uint(id))}
}

type TrackingId string

func (id TrackingId) AsQms() []qm.QueryMod {
	return []qm.QueryMod{models.ParcelWhere.TrackingID.EQ(string(id))}
}

type MmccBagIdentifier struct {
	partnerId     uint64
	sourceOrderId string
}

func NewMmccBagIdentifier(partnerId uint64, sourceOrderId string) *MmccBagIdentifier {
	return &MmccBagIdentifier{partnerId: partnerId, sourceOrderId: sourceOrderId}
}

func (id MmccBagIdentifier) AsQms() []qm.QueryMod {
	return []qm.QueryMod{
		models.ParcelWhere.PartnerID.EQ(null.Uint64From(id.partnerId)),
		models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(id.sourceOrderId)),
		models.ParcelWhere.Type.IN([]uint16{parcelConst.BagB2B, parcelConst.BagB2CV2}),
	}
}

type ParcelIdentifier struct {
	partnerId     uint64
	sourceOrderId string
}

func NewParcelIdentifier(partnerId uint64, sourceOrderId string) *ParcelIdentifier {
	return &ParcelIdentifier{partnerId: partnerId, sourceOrderId: sourceOrderId}
}

func (id ParcelIdentifier) AsQms() []qm.QueryMod {
	return []qm.QueryMod{
		models.ParcelWhere.PartnerID.EQ(null.Uint64From(id.partnerId)),
		models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(id.sourceOrderId)),
		models.ParcelWhere.Type.EQ(parcelConst.Parcel),
	}
}

type MMCCParcelIdentifier struct {
	partnerId        uint64
	PartnerUniqueKey string
}

func NewMMCCParcelIdentifier(partnerId uint64, partnerUniqueKey string) *MMCCParcelIdentifier {
	return &MMCCParcelIdentifier{partnerId: partnerId, PartnerUniqueKey: partnerUniqueKey}
}

func (id MMCCParcelIdentifier) AsQms() []qm.QueryMod {
	return []qm.QueryMod{
		models.ParcelWhere.PartnerID.EQ(null.Uint64From(id.partnerId)),
		models.ParcelWhere.PartnerUniqueKey.EQ(null.StringFrom(id.PartnerUniqueKey)),
		models.ParcelWhere.Type.EQ(parcelConst.MMCCParcel),
	}
}
