package auth

import (
	"bytes"
	"context"
	"crypto/md5"
	"crypto/sha256"
	"database/sql"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/golang-jwt/jwt/v5"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/volatiletech/null/v8"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"

	"bitbucket.ninjavan.co/cg/base-commons---go/systemid"
	"bitbucket.ninjavan.co/cg/datadog-agent---go/nvtracer"
	"bitbucket.ninjavan.co/cg/request-commons---go/response"

	"git.ninjavan.co/3pl/cache"
	middlewareCfg "git.ninjavan.co/3pl/configs/middleware"
	"git.ninjavan.co/3pl/envs"
	errors2 "git.ninjavan.co/3pl/errors"
	"git.ninjavan.co/3pl/errors/fplerror"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/utils/loggerutils"
	"git.ninjavan.co/3pl/utils/monitor"
)

type ClientID string

const (
	Tiktok       ClientID = "Tiktok"
	Lazada       ClientID = "Lazada"
	LazadaMM     ClientID = "Lazadamm"
	LazadaMMAIDC ClientID = "Lazadammaidc"
	LazadaCC     ClientID = "Lazadacc"
	Shopee       ClientID = "Shopee"

	HashAlgoAcs3HmacSha256 = "ACS3-HMAC-SHA256"
)

type Handleable interface {
	Authenticate(*gin.Context) (*models.Partner, error)
}

type WishAuth struct {
	apiKey      string
	partnerRepo repo_interface.PartnerRepository
}

const moduleName = "auth-handler"

func GetWishAuth() *WishAuth {
	return &WishAuth{
		apiKey:      envs.Instance.WishFplApiKey,
		partnerRepo: repositories.NewPartnerRepository(),
	}
}

func (o *WishAuth) Authenticate(c *gin.Context) (*models.Partner, error) {
	request := order.WishAuthRequest{}
	_ = c.ShouldBindBodyWith(&request, binding.JSON)

	if o.apiKey != request.APIKey {
		return nil, &errors2.WishError{
			OriginError: fplerror.ErrUnauthorized.New(),
		}
	}

	partner, err := o.partnerRepo.GetOne(c, models.PartnerWhere.ID.EQ(envs.Instance.WishPartnerId))
	if err != nil {
		if err != sql.ErrNoRows {
			loggerutils.Ctx(c.Request.Context(), moduleName).Err(err).Msg("failed to get partner")
		}

		return nil, &errors2.WishError{
			OriginError: fplerror.ErrUnauthorized.New(),
		}
	}

	return partner, nil
}

type tiktokCredential struct {
	AppKey    string
	AppSecret string
	Country   string
}

type tiktokAuth struct {
	credentials []*tiktokCredential
	authType    int
	version     string
	mmccVersion string
	partnerRepo repo_interface.PartnerRepository
	cacheClient cache.Handler
}

const (
	TiktokStandardOC = 1
	TiktokMMCCOC     = 2
)

var (
	tiktokAppKeySG     = envs.Instance.TiktokFplAppKeySg
	tiktokAppKeyMY     = envs.Instance.TiktokFplAppKeyMy
	tiktokAppKeyTH     = envs.Instance.TiktokFplAppKeyTh
	tiktokAppKeyVN     = envs.Instance.TiktokFplAppKeyVn
	tiktokAppKeyPH     = envs.Instance.TiktokFplAppKeyPh
	tiktokAppKeyMMCCSG = envs.Instance.TiktokFplAppKeyMmccSg
	tiktokAppKeyMMCCMY = envs.Instance.TiktokFplAppKeyMmccMy
	tiktokAppKeyMMCCTH = envs.Instance.TiktokFplAppKeyMmccTh
	tiktokAppKeyMMCCVN = envs.Instance.TiktokFplAppKeyMmccVn
	tiktokAppKeyMMCCPH = envs.Instance.TiktokFplAppKeyMmccPh

	tiktokAppSecretSG     = envs.Instance.TiktokFplAppSecretSg
	tiktokAppSecretMY     = envs.Instance.TiktokFplAppSecretMy
	tiktokAppSecretTH     = envs.Instance.TiktokFplAppSecretTh
	tiktokAppSecretVN     = envs.Instance.TiktokFplAppSecretVn
	tiktokAppSecretPH     = envs.Instance.TiktokFplAppSecretPh
	tiktokAppSecretMMCCSG = envs.Instance.TiktokFplAppSecretMmccSg
	tiktokAppSecretMMCCMY = envs.Instance.TiktokFplAppSecretMmccMy
	tiktokAppSecretMMCCTH = envs.Instance.TiktokFplAppSecretMmccTh
	tiktokAppSecretMMCCVN = envs.Instance.TiktokFplAppSecretMmccVn
	tiktokAppSecretMMCCPH = envs.Instance.TiktokFplAppSecretMmccPh

	tiktokCredentials = []*tiktokCredential{
		{AppKey: tiktokAppKeySG, AppSecret: tiktokAppSecretSG, Country: systemid.SG.Country},
		{AppKey: tiktokAppKeyMY, AppSecret: tiktokAppSecretMY, Country: systemid.MY.Country},
		{AppKey: tiktokAppKeyTH, AppSecret: tiktokAppSecretTH, Country: systemid.TH.Country},
		{AppKey: tiktokAppKeyVN, AppSecret: tiktokAppSecretVN, Country: systemid.VN.Country},
		{AppKey: tiktokAppKeyPH, AppSecret: tiktokAppSecretPH, Country: systemid.PH.Country},
		{AppKey: tiktokAppKeyMMCCSG, AppSecret: tiktokAppSecretMMCCSG, Country: systemid.SG.Country},
		{AppKey: tiktokAppKeyMMCCMY, AppSecret: tiktokAppSecretMMCCMY, Country: systemid.MY.Country},
		{AppKey: tiktokAppKeyMMCCTH, AppSecret: tiktokAppSecretMMCCTH, Country: systemid.TH.Country},
		{AppKey: tiktokAppKeyMMCCVN, AppSecret: tiktokAppSecretMMCCVN, Country: systemid.VN.Country},
		{AppKey: tiktokAppKeyMMCCPH, AppSecret: tiktokAppSecretMMCCPH, Country: systemid.PH.Country},
	}

	TiktokCountryCredentials = map[string]*tiktokCredential{
		systemid.SG.Country: {AppKey: tiktokAppKeySG, AppSecret: tiktokAppSecretSG},
		systemid.MY.Country: {AppKey: tiktokAppKeyMY, AppSecret: tiktokAppSecretMY},
		systemid.TH.Country: {AppKey: tiktokAppKeyTH, AppSecret: tiktokAppSecretTH},
		systemid.VN.Country: {AppKey: tiktokAppKeyVN, AppSecret: tiktokAppSecretVN},
		systemid.PH.Country: {AppKey: tiktokAppKeyPH, AppSecret: tiktokAppSecretPH},
	}
)

const tiktokAuthModuleName = "tiktok-auth-handler"

func NewTiktokAuth(authType int) *tiktokAuth {
	return &tiktokAuth{
		credentials: tiktokCredentials,
		authType:    authType,
		version:     envs.Instance.TiktokFplVersion,
		mmccVersion: envs.Instance.TiktokFplMmccVersion,
		partnerRepo: repositories.NewPartnerRepository(),
		cacheClient: cache.NewHandler(),
	}
}

func (t *tiktokAuth) Authenticate(c *gin.Context) (*models.Partner, error) {
	r := order.TiktokRequest{}
	if err := c.ShouldBindWith(&r, binding.Form); err != nil {
		return nil, c.Error(errors2.ErrTiktokAppKeyNull)
	}

	var credential *tiktokCredential
	err := checkAuthenInfo(r, t, &credential)
	if err != nil {
		return nil, c.Error(err)
	}

	logger := loggerutils.Ctx(c.Request.Context(), tiktokAuthModuleName).With().Str("request_id", c.GetHeader("_reqID")).Logger()

	maxDelaysTime := time.Minute * 5
	reqTimestamp, _ := strconv.ParseInt(r.Timestamp, 10, 64)
	if time.Since(time.Unix(reqTimestamp, 0)) > maxDelaysTime {
		logger.Error().Msg("request expired")
		return nil, c.Error(errors2.ErrTiktokRequestExpired)
	}

	if gSign := r.CreateSign(credential.AppSecret); r.Sign != gSign && !envs.Instance.NvBypassAuthValidation {
		logger.Error().Str("expected-sign", gSign).Str("actual-sign", r.Sign).Msg("invalid signature")
		return nil, c.Error(errors2.ErrTiktokSignVerificationFailed)
	}
	c.Set(middlewareCfg.CountryKey, credential.Country)

	ctx := c.Request.Context()

	cacheKey := fmt.Sprintf(cache.ShipperPartnerCacheKeyTmpl, Tiktok)
	cached := &CacheWrapper{}
	err = t.cacheClient.GetObj(ctx, cacheKey, cached)
	if err == nil && cached.Partner != nil {
		monitor.AuthPartnerCacheHitInc()
		return cached.Partner, nil
	}
	monitor.AuthPartnerCacheMissInc()

	partner, err := t.partnerRepo.GetOne(ctx, models.PartnerWhere.ID.EQ(envs.Instance.Tiktok.PartnerId))
	if err != nil {
		logger.Err(err).Msg("get partner failed")
		return nil, c.Error(errors2.ErrTiktokServiceError)
	}

	if partner.OrderCreationMethod.IsZero() {
		logger.Err(err).Msg("partner hasn't enabled order-creation yet")
		return nil, c.Error(&errors2.TiktokError{
			OriginError: fplerror.ErrInternal.NewWithoutStack("Partner hasn't enabled order-creation yet"),
		})
	}

	go func() {
		_ = t.cacheClient.SetObj(context.Background(), cacheKey, CacheWrapper{Partner: partner}, cacheTTL)
	}()

	return partner, nil
}

func checkAuthenInfo(r order.TiktokRequest, t *tiktokAuth, credential **tiktokCredential) error {
	if r.AppKey == "" {
		return errors2.ErrTiktokAppKeyNull
	}

	for _, tc := range t.credentials {
		if tc.AppKey == r.AppKey {
			*credential = tc
			break
		}
	}

	if credential == nil {
		return errors2.ErrTiktokAppKeyIllegal
	}

	if r.Sign == "" {
		return errors2.ErrTiktokSignNull
	}

	if (t.authType == TiktokStandardOC && r.Version != t.version) || (t.authType == TiktokMMCCOC && r.Version != t.mmccVersion) {
		return errors2.ErrTiktokVersionIllegal
	}

	return nil
}

type epiAuth struct {
	partnerRepo repo_interface.PartnerRepository
}

const epiAuthModuleName = "epi-auth-handler"

func NewEPIAuth() *epiAuth {
	return &epiAuth{
		partnerRepo: repositories.NewPartnerRepository(),
	}
}

// Factory functions for dependencies - can be replaced in tests
var (
	newCacheHandler = func() cache.Handler {
		return cache.NewHandler()
	}

	newPartnerRepository = func() repo_interface.PartnerRepository {
		return repositories.NewPartnerRepository()
	}
)

func (t *epiAuth) Authenticate(c *gin.Context) (*models.Partner, error) {
	request := order.EPIOrderCreationRequest{}
	_ = c.ShouldBindBodyWith(&request, binding.JSON)

	logger := loggerutils.Ctx(c.Request.Context(), epiAuthModuleName).With().Str("request_id", c.GetHeader("_reqID")).Logger()
	if request.PartnerID == 0 {
		return nil, c.Error(fplerror.ErrBadRequest.NewWithoutStack("Partner ID is a required field"))
	}

	partner, err := t.partnerRepo.GetOne(c, models.PartnerWhere.ID.EQ(request.PartnerID))
	if err == sql.ErrNoRows {
		return nil, c.Error(fplerror.ErrEntityNotFound.NewWithoutStack("Partner %d not found", request.PartnerID))
	}

	if err != nil {
		logger.Err(err).Msg("failed to get partner")
		return nil, c.Error(fplerror.ErrInternal.WithMsgInternalDB())
	}

	return partner, nil
}

func LazadaMMCCAuthHandler() gin.HandlerFunc {
	cacheClient := cache.NewHandler()
	partnerRepo := repositories.NewPartnerRepository()
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		_, span := nvtracer.CreateSpanFromContext(ctx, tracer.ResourceName("LazadaAuth"))
		defer span.Finish()

		logger := log.Ctx(ctx)

		req := &order.LazadaRequest{}
		if err := c.ShouldBindWith(req, binding.Form); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
			return
		}
		if req.LogisticsInterface == "" {
			c.AbortWithStatusJSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, "empty payload"))
			return
		}

		sign := calculateLazadaSign(req.LogisticsInterface, envs.Instance.Lazada.AppSecret)
		if req.DataDigest != sign && !envs.Instance.NvBypassAuthValidation {
			logger.Error().Msgf(
				"invalid lazada sign. expected sign: %s, req sign: %s, req data: %s",
				sign, req.DataDigest, req.LogisticsInterface,
			)
			c.AbortWithStatusJSON(http.StatusUnauthorized, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidSign, "invalid sign"))
			return
		}

		cacheKey := fmt.Sprintf(cache.ShipperPartnerCacheKeyTmpl, Lazada)
		loadAndInjectPartner(c, cacheClient, cacheKey, partnerRepo, envs.Instance.Lazada.PartnerId)
	}
}

func LazadaCainiaoMMAuthHandler() gin.HandlerFunc {
	cacheClient := cache.NewHandler()
	partnerRepo := repositories.NewPartnerRepository()
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		_, span := nvtracer.CreateSpanFromContext(ctx, tracer.ResourceName("LazadaMMAuth"))
		defer span.Finish()

		logger := log.Ctx(ctx)

		req := &order.LazadaRequest{}
		if err := c.ShouldBindWith(req, binding.Form); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, err.Error()))
			return
		}
		if req.LogisticsInterface == "" {
			c.AbortWithStatusJSON(http.StatusBadRequest, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidRequest, "empty payload"))
			return
		}

		sign := calculateLazadaSign(req.LogisticsInterface, envs.Instance.LazadaMM.AppSecret)
		if req.DataDigest != sign && !envs.Instance.NvBypassAuthValidation {
			logger.Error().Msgf(
				"invalid lazada mm sign. expected sign: %s, req sign: %s, req data: %s",
				sign, req.DataDigest, req.LogisticsInterface,
			)
			c.AbortWithStatusJSON(http.StatusUnauthorized, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidSign, "invalid sign"))
			return
		}

		cacheKey := fmt.Sprintf(cache.ShipperPartnerCacheKeyTmpl, LazadaMM)
		loadAndInjectPartner(c, cacheClient, cacheKey, partnerRepo, envs.Instance.LazadaMM.PartnerId)
	}
}

func LazadaCCAuthHandler() gin.HandlerFunc {
	cacheClient := newCacheHandler()
	partnerRepo := newPartnerRepository()
	cacheKey := fmt.Sprintf(cache.ShipperPartnerCacheKeyTmpl, LazadaCC)
	return lazadaAuthHandler(envs.Instance.LazadaCC.PartnerId, cacheKey, envs.Instance.LazadaCC.AppKey, envs.Instance.LazadaCC.AppSecret, cacheClient, partnerRepo)
}

func LazadaMMAuthHandler() gin.HandlerFunc {
	cacheClient := newCacheHandler()
	partnerRepo := newPartnerRepository()
	cacheKey := fmt.Sprintf(cache.ShipperPartnerCacheKeyTmpl, LazadaMMAIDC)
	// using the same app key and secret as lazada cc
	return lazadaAuthHandler(envs.Instance.LazadaMMAIDC.PartnerId, cacheKey, envs.Instance.LazadaCC.AppKey, envs.Instance.LazadaCC.AppSecret, cacheClient, partnerRepo)
}

func lazadaAuthHandler(partnerId uint64, partnerCacheKey string, appKey string, appSecret string, cacheClient cache.Handler, partnerRepo repo_interface.PartnerRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		_, span := nvtracer.CreateSpanFromContext(ctx, tracer.ResourceName("LazadaAuth"))
		defer span.Finish()

		logger := log.Ctx(ctx)

		payload, _ := c.GetRawData()
		c.Set(gin.BodyBytesKey, payload)
		// Restore the request body for subsequent reads
		c.Request.Body = io.NopCloser(bytes.NewBuffer(payload))

		lazadaCcReq := getLazadaRequest(c.Request)

		hash := sha256.Sum256(payload)
		payloadHashed := hex.EncodeToString(hash[:])

		authString := null.StringFromPtr(getAuthorization(lazadaCcReq,
			null.StringFrom(HashAlgoAcs3HmacSha256).Ptr(),
			null.StringFrom(payloadHashed).Ptr(),
			null.StringFrom(appKey).Ptr(),
			null.StringFrom(appSecret).Ptr(),
		)).String

		headerAuth := c.Request.Header.Get("Authorization")
		if headerAuth != authString && !envs.Instance.NvBypassAuthValidation {
			logger.Error().Msgf(
				"invalid lazada sign. expected sign: %s, got sign: %s, req data: %s",
				authString, headerAuth, string(payload),
			)
			c.AbortWithStatusJSON(http.StatusUnauthorized, order.NewLazadaErrorResponse(order.LazadaErrorCodeInvalidSign, "invalid sign"))
			return
		}

		loadAndInjectPartner(c, cacheClient, partnerCacheKey, partnerRepo, partnerId)
	}
}

func calculateLazadaSign(content, appSecret string) string {
	input := content + appSecret

	hash := md5.New()
	hash.Write([]byte(input))
	hashBytes := hash.Sum(nil)
	sign := base64.StdEncoding.EncodeToString(hashBytes)

	return sign
}

func ShopeeAuthHandler() gin.HandlerFunc {
	cacheClient := cache.NewHandler()
	partnerRepo := repositories.NewPartnerRepository()
	return shopeeAuthHandler(cacheClient, partnerRepo)
}

func shopeeAuthHandler(cacheClient cache.Handler, partnerRepo repo_interface.PartnerRepository) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		_, span := nvtracer.CreateSpanFromContext(ctx, tracer.ResourceName("ShopeeAuth"))
		defer span.Finish()

		logger := log.Ctx(ctx)

		req := &order.ShopeeRequest{}
		if err := c.ShouldBindBodyWith(req, binding.JSON); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, order.NewShopeeResponse(order.ShopeeRetCodeBadRequest, err.Error()))
			return
		}

		if !envs.Instance.NvBypassAuthValidation && !validateShopeeJWT(c, req, logger) {
			return
		}

		cacheKey := fmt.Sprintf(cache.ShipperPartnerCacheKeyTmpl, Shopee)
		loadAndInjectPartner(c, cacheClient, cacheKey, partnerRepo, envs.Instance.Shopee.PartnerId)
	}
}

func validateShopeeJWT(c *gin.Context, req *order.ShopeeRequest, logger *zerolog.Logger) bool {
	token, err := jwt.Parse(req.Jwt, func(token *jwt.Token) (interface{}, error) {
		if m, ok := token.Method.(*jwt.SigningMethodHMAC); !ok || m != jwt.SigningMethodHS256 {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		if token.Header["account"] != envs.Instance.Shopee.AppKey {
			return nil, fmt.Errorf("unexpected account method: %v", token.Header["account"])
		}

		return []byte(envs.Instance.Shopee.AppSecret), nil
	})

	if err != nil {
		logger.Err(err).Msg("parse JWT failed")
		c.AbortWithStatusJSON(http.StatusUnauthorized, order.NewShopeeResponse(order.ShopeeRetCodeJWTInvalid, "parse JWT failed"))
		return false
	}
	if !token.Valid {
		logger.Error().Msg("invalid JWT")
		c.AbortWithStatusJSON(http.StatusUnauthorized, order.NewShopeeResponse(order.ShopeeRetCodeJWTInvalid, "invalid JWT"))
		return false
	}
	return true
}

func loadAndInjectPartner(
	c *gin.Context, cacheClient cache.Handler, cacheKey string,
	partnerRepo repo_interface.PartnerRepository, partnerId uint64,
) {
	ctx := c.Request.Context()

	cached := &CacheWrapper{}
	err := cacheClient.GetObj(ctx, cacheKey, cached)
	if err == nil && cached.Partner != nil {
		monitor.AuthPartnerCacheHitInc()
		c.Set(middlewareCfg.PartnerKey, cached.Partner)
		return
	}
	monitor.AuthPartnerCacheMissInc()

	partner, err := partnerRepo.GetOne(ctx, models.PartnerWhere.ID.EQ(partnerId))
	if err != nil {
		log.Ctx(ctx).Err(err).Msg("get partner failed")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response.Response{
			Error: &response.Error{Message: "db error"},
		})
		return
	}

	if partner.OrderCreationMethod.IsZero() {
		log.Ctx(ctx).Err(err).Msg("partner hasn't enabled order-creation yet")
		c.AbortWithStatusJSON(http.StatusInternalServerError, response.Response{
			Error: &response.Error{Message: "Partner hasn't enabled order-creation yet"},
		})
		return
	}

	go func() {
		_ = cacheClient.SetObj(context.Background(), cacheKey, CacheWrapper{Partner: partner}, cacheTTL)
	}()

	c.Set(middlewareCfg.PartnerKey, partner)
}
