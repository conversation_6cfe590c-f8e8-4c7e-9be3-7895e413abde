package auth

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/golang/mock/gomock"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/volatiletech/null/v8"

	"git.ninjavan.co/3pl/cache"
	mocks1 "git.ninjavan.co/3pl/cache/mocks"
	middlewareCfg "git.ninjavan.co/3pl/configs/middleware"
	"git.ninjavan.co/3pl/envs"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	mocks2 "git.ninjavan.co/3pl/repositories/mocks"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/utils"
)

func TestNewTiktokAuth(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name string
		want *tiktokAuth
	}{
		{
			name: "tiktok_auth_handler_should_be_initialized_properly",
			want: &tiktokAuth{
				credentials: tiktokCredentials,
				authType:    TiktokStandardOC,
				version:     envs.Instance.TiktokFplVersion,
				mmccVersion: envs.Instance.TiktokFplMmccVersion,
				partnerRepo: repositories.NewPartnerRepository(),
				cacheClient: cache.NewHandler(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewTiktokAuth(TiktokStandardOC); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewTiktokAuth() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_tiktokAuth_Authenticate(t *testing.T) {
	t.Parallel()
	var ttDefaultPartnerId uint64 = 67
	tiktokCacheKey := "4pl:shipper_partner:Tiktok"

	errCache := func(c *gomock.Controller) cache.Handler {
		m := mocks1.NewMockHandler(c)
		m.EXPECT().GetObj(gomock.Any(), tiktokCacheKey, gomock.Any()).Return(errors.New("failed"))

		return m
	}

	type fields struct {
		credentials []*tiktokCredential
		version     string
		mmccVersion string
		authType    int
		partnerRepo func(*gomock.Controller) repo_interface.PartnerRepository
		cacheClient func(c *gomock.Controller) cache.Handler
		logger      zerolog.Logger
	}

	gin.SetMode(gin.TestMode)

	type args struct {
		c *gin.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *models.Partner
		wantErr bool
	}{
		{
			name: "couldn't bind form",
			fields: fields{
				partnerRepo: func(c *gomock.Controller) repo_interface.PartnerRepository {
					return nil
				},
			},
			args: args{
				c: func() *gin.Context {
					ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
					ctx.Request, _ = http.NewRequest("POST", "/label", strings.NewReader(`{"tracking_id":"123"}`))
					ctx.Request.Header.Add("Content-Type", "text/plain; boundary=")

					return ctx
				}(),
			},
			wantErr: true,
		},
		{
			name: "app key is empty",
			fields: fields{
				partnerRepo: func(c *gomock.Controller) repo_interface.PartnerRepository { return nil },
			},
			args: args{
				c: func() *gin.Context {
					ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
					ctx.Request, _ = http.NewRequest("POST", "/label", strings.NewReader(`{"tracking_id":"123"}`))

					return ctx
				}(),
			},
			wantErr: true,
		},
		{
			name: "app key does not match",
			fields: fields{
				credentials: []*tiktokCredential{
					{AppKey: "right-app-key", AppSecret: "secret-sg"},
					{AppKey: "correct-app-key", AppSecret: "secret-ph"},
				},
				partnerRepo: func(c *gomock.Controller) repo_interface.PartnerRepository { return nil },
			},
			args: args{
				c: func() *gin.Context {
					formVals := url.Values{}
					formVals.Add("app_key", "wrong-app-key")

					ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
					ctx.Request, _ = http.NewRequest("POST", "/label", strings.NewReader(formVals.Encode()))
					ctx.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")

					return ctx
				}(),
			},
			wantErr: true,
		},
		{
			name: "signature is empty",
			fields: fields{
				credentials: []*tiktokCredential{
					{AppKey: "right-app-key", AppSecret: "secret-sg"},
					{AppKey: "correct-app-key", AppSecret: "secret-ph"},
				},
				partnerRepo: func(c *gomock.Controller) repo_interface.PartnerRepository { return nil },
			},
			args: args{
				c: func() *gin.Context {
					formVals := url.Values{}
					formVals.Add("app_key", "correct-app-key")

					ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
					ctx.Request, _ = http.NewRequest("POST", "/label", strings.NewReader(formVals.Encode()))
					ctx.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")

					return ctx
				}(),
			},
			wantErr: true,
		},
		{
			name: "version does not match",
			fields: fields{
				credentials: []*tiktokCredential{
					{AppKey: "right-app-key", AppSecret: "secret-sg"},
					{AppKey: "correct-app-key", AppSecret: "secret-ph"},
				},
				version:     "2.0",
				mmccVersion: "3.0",
				authType:    TiktokStandardOC,
				partnerRepo: func(c *gomock.Controller) repo_interface.PartnerRepository { return nil },
			},
			args: args{
				c: func() *gin.Context {
					formVals := url.Values{}
					formVals.Add("app_key", "right-app-key")
					formVals.Add("sign", "a-secure-signature")
					formVals.Add("version", "2.0.1")

					ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
					ctx.Request, _ = http.NewRequest("POST", "/label", strings.NewReader(formVals.Encode()))
					ctx.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")

					return ctx
				}(),
			},
			wantErr: true,
		},
		{
			name: "timestamp is expired",
			fields: fields{
				credentials: []*tiktokCredential{
					{AppKey: "right-app-key", AppSecret: "secret-sg"},
					{AppKey: "correct-app-key", AppSecret: "secret-ph"},
				},
				version:     "2.0",
				partnerRepo: func(c *gomock.Controller) repo_interface.PartnerRepository { return nil },
				logger:      utils.NewLogger("tiktok-auth-handler"),
			},
			args: args{
				c: func() *gin.Context {
					formVals := url.Values{}
					formVals.Add("app_key", "correct-app-key")
					formVals.Add("sign", "a-secure-signature")
					formVals.Add("version", "2.0")
					formVals.Add("timestamp", fmt.Sprintf("%d", time.Now().Add(-301*time.Second).Unix()))

					ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
					ctx.Request, _ = http.NewRequest("POST", "/label", strings.NewReader(formVals.Encode()))
					ctx.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")

					return ctx
				}(),
			},
			wantErr: true,
		},
		{
			name: "signature is incorrect",
			fields: fields{
				credentials: []*tiktokCredential{
					{AppKey: "right-app-key", AppSecret: "secret-sg"},
					{AppKey: "correct-app-key", AppSecret: "secret-ph"},
				},
				version:     "2.0",
				partnerRepo: func(c *gomock.Controller) repo_interface.PartnerRepository { return nil },
				logger:      utils.NewLogger("tiktok-auth-handler"),
			},
			args: args{
				c: func() *gin.Context {
					formVals := url.Values{}
					formVals.Add("app_key", "correct-app-key")
					formVals.Add("version", "2.0")
					formVals.Add("timestamp", fmt.Sprintf("%d", time.Now().Unix()))
					formVals.Add("param_json", `{"name":"alo"}`)
					formVals.Add("sign", "a-bad-sign")

					ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
					ctx.Request, _ = http.NewRequest("POST", "/label", strings.NewReader(formVals.Encode()))
					ctx.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")

					return ctx
				}(),
			},
			wantErr: true,
		},
		{
			name: "couldn't get partner information",
			fields: fields{
				credentials: []*tiktokCredential{
					{AppKey: "right-app-key", AppSecret: "secret-sg"},
					{AppKey: "correct-app-key", AppSecret: "secret-ph"},
				},
				version: "2.0",
				partnerRepo: func(c *gomock.Controller) repo_interface.PartnerRepository {
					r := mocks2.NewMockPartnerRepository(c)
					r.EXPECT().
						GetOne(gomock.Any(), models.PartnerWhere.ID.EQ(ttDefaultPartnerId)).
						Return(nil, sql.ErrNoRows)
					return r
				},
				cacheClient: errCache,
				logger:      utils.NewLogger("tiktok-auth-handler"),
			},
			args: args{
				c: func() *gin.Context {
					req := &order.TiktokRequest{
						AppKey:    "correct-app-key",
						Version:   "2.0",
						ParamJSON: `{"name":"alo"}`,
						Timestamp: fmt.Sprintf("%d", time.Now().Unix()),
					}

					formVals := url.Values{}
					formVals.Add("app_key", req.AppKey)
					formVals.Add("version", req.Version)
					formVals.Add("timestamp", req.Timestamp)
					formVals.Add("param_json", req.ParamJSON)
					formVals.Add("sign", req.CreateSign("secret-ph"))

					ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
					ctx.Request, _ = http.NewRequest("POST", "/label", strings.NewReader(formVals.Encode()))
					ctx.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")

					return ctx
				}(),
			},
			wantErr: true,
		},
		{
			name: "cache hit",
			fields: fields{
				credentials: []*tiktokCredential{
					{AppKey: "right-app-key", AppSecret: "secret-sg"},
					{AppKey: "correct-app-key", AppSecret: "secret-ph"},
				},
				version: "2.0",
				partnerRepo: func(c *gomock.Controller) repo_interface.PartnerRepository {
					return mocks2.NewMockPartnerRepository(c)
				},
				cacheClient: func(c *gomock.Controller) cache.Handler {
					m := mocks1.NewMockHandler(c)
					m.EXPECT().GetObj(gomock.Any(), tiktokCacheKey, gomock.Any()).
						Return(nil).SetArg(2,
						CacheWrapper{
							Partner: &models.Partner{ID: 1000},
						},
					)

					return m
				},
				logger: utils.NewLogger("tiktok-auth-handler"),
			},
			args: args{
				c: func() *gin.Context {
					req := &order.TiktokRequest{
						AppKey:    "correct-app-key",
						Version:   "2.0",
						ParamJSON: `{"name":"alo"}`,
						Timestamp: fmt.Sprintf("%d", time.Now().Unix()),
					}

					formVals := url.Values{}
					formVals.Add("app_key", req.AppKey)
					formVals.Add("version", req.Version)
					formVals.Add("timestamp", req.Timestamp)
					formVals.Add("param_json", req.ParamJSON)
					formVals.Add("sign", req.CreateSign("secret-ph"))

					ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
					ctx.Request, _ = http.NewRequest("POST", "/label", strings.NewReader(formVals.Encode()))
					ctx.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")

					return ctx
				}(),
			},
			want:    &models.Partner{ID: 1000},
			wantErr: false,
		},
		{
			name: "get partner information successfully but partner does not enable OC method yet",
			fields: fields{
				credentials: []*tiktokCredential{
					{AppKey: "right-app-key", AppSecret: "secret-sg"},
					{AppKey: "correct-app-key", AppSecret: "secret-ph"},
				},
				version: "2.0",
				partnerRepo: func(c *gomock.Controller) repo_interface.PartnerRepository {
					r := mocks2.NewMockPartnerRepository(c)
					r.EXPECT().
						GetOne(gomock.Any(), models.PartnerWhere.ID.EQ(ttDefaultPartnerId)).
						Return(&models.Partner{ID: 1}, nil)
					return r
				},
				cacheClient: errCache,
				logger:      utils.NewLogger("tiktok-auth-handler"),
			},
			args: args{
				c: func() *gin.Context {
					req := &order.TiktokRequest{
						AppKey:    "correct-app-key",
						Version:   "2.0",
						ParamJSON: `{"name":"alo"}`,
						Timestamp: fmt.Sprintf("%d", time.Now().Unix()),
					}

					formVals := url.Values{}
					formVals.Add("app_key", req.AppKey)
					formVals.Add("version", req.Version)
					formVals.Add("timestamp", req.Timestamp)
					formVals.Add("param_json", req.ParamJSON)
					formVals.Add("sign", req.CreateSign("secret-ph"))

					ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
					ctx.Request, _ = http.NewRequest("POST", "/label", strings.NewReader(formVals.Encode()))
					ctx.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")

					return ctx
				}(),
			},
			wantErr: true,
		},
		{
			name: "get partner information successfully and the partner has enabled OC method",
			fields: fields{
				credentials: []*tiktokCredential{
					{AppKey: "right-app-key", AppSecret: "secret-sg"},
					{AppKey: "correct-app-key", AppSecret: "secret-ph"},
				},
				version: "2.0",
				partnerRepo: func(c *gomock.Controller) repo_interface.PartnerRepository {
					r := mocks2.NewMockPartnerRepository(c)
					r.EXPECT().
						GetOne(gomock.Any(), models.PartnerWhere.ID.EQ(ttDefaultPartnerId)).
						Return(&models.Partner{ID: 1, OrderCreationMethod: null.Uint8From(1)}, nil)
					return r
				},
				cacheClient: func(c *gomock.Controller) cache.Handler {
					m := mocks1.NewMockHandler(c)
					m.EXPECT().GetObj(gomock.Any(), tiktokCacheKey, gomock.Any()).Return(errors.New("failed"))
					m.EXPECT().SetObj(gomock.Any(), tiktokCacheKey, gomock.Any(), gomock.Any()).
						Return(nil).AnyTimes()

					return m
				},
				logger: utils.NewLogger("tiktok-auth-handler"),
			},
			args: args{
				c: func() *gin.Context {
					req := &order.TiktokRequest{
						AppKey:    "correct-app-key",
						Version:   "2.0",
						ParamJSON: `{"name":"alo"}`,
						Timestamp: fmt.Sprintf("%d", time.Now().Unix()),
					}

					formVals := url.Values{}
					formVals.Add("app_key", req.AppKey)
					formVals.Add("version", req.Version)
					formVals.Add("timestamp", req.Timestamp)
					formVals.Add("param_json", req.ParamJSON)
					formVals.Add("sign", req.CreateSign("secret-ph"))

					ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
					ctx.Request, _ = http.NewRequest("POST", "/label", strings.NewReader(formVals.Encode()))
					ctx.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")

					return ctx
				}(),
			},
			want: &models.Partner{ID: 1, OrderCreationMethod: null.Uint8From(1)},
		},
	}

	ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
	ctx.Request, _ = http.NewRequest("POST", "/label", strings.NewReader(`{"tracking_id":"123"}`))

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			var cacheMock cache.Handler
			if tt.fields.cacheClient != nil {
				cacheMock = tt.fields.cacheClient(ctrl)
			}

			tr := &tiktokAuth{
				credentials: tt.fields.credentials,
				version:     tt.fields.version,
				mmccVersion: tt.fields.mmccVersion,
				authType:    tt.fields.authType,
				partnerRepo: tt.fields.partnerRepo(ctrl),
				cacheClient: cacheMock,
			}
			got, err := tr.Authenticate(tt.args.c)
			if (err != nil) != tt.wantErr {
				t.Errorf("tiktokAuth.Authenticate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("tiktokAuth.Authenticate() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewEPIAuth(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name string
		want *epiAuth
	}{
		{
			name: "able to get epi auth handler",
			want: &epiAuth{
				partnerRepo: repositories.NewPartnerRepository(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, NewEPIAuth(), "NewEPIAuth()")
		})
	}
}

func Test_epiAuth_Authenticate(t1 *testing.T) {
	type fields struct {
		partnerRepo repo_interface.PartnerRepository
	}
	type args struct {
		c *gin.Context
	}

	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()

	ctrl := gomock.NewController(t1)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *models.Partner
		wantErr bool
	}{
		{
			name:   "partner id is required",
			fields: fields{},
			args: args{
				c: func() *gin.Context {
					ctx, _ := gin.CreateTestContext(w)
					ctx.Request, _ = http.NewRequest("POST", "/v1/epi/orders", strings.NewReader(`{}`))
					return ctx
				}(),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failed to get partner due to err occurred",
			fields: fields{
				partnerRepo: func() repo_interface.PartnerRepository {
					r := mocks2.NewMockPartnerRepository(ctrl)
					r.EXPECT().GetOne(gomock.Any(), gomock.Any()).Return(nil, sql.ErrConnDone)
					return r
				}(),
			},
			args: args{
				c: func() *gin.Context {
					ctx, _ := gin.CreateTestContext(w)
					ctx.Request, _ = http.NewRequest("POST", "/v1/epi/orders", strings.NewReader(`{"partner_id": 1}`))
					return ctx
				}(),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "partner not found",
			fields: fields{
				partnerRepo: func() repo_interface.PartnerRepository {
					r := mocks2.NewMockPartnerRepository(ctrl)
					r.EXPECT().GetOne(gomock.Any(), gomock.Any()).Return(nil, sql.ErrNoRows)
					return r
				}(),
			},
			args: args{
				c: func() *gin.Context {
					ctx, _ := gin.CreateTestContext(w)
					ctx.Request, _ = http.NewRequest("POST", "/v1/epi/orders", strings.NewReader(`{"partner_id": 1}`))
					return ctx
				}(),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "partner found",
			fields: fields{
				partnerRepo: func() repo_interface.PartnerRepository {
					r := mocks2.NewMockPartnerRepository(ctrl)
					r.EXPECT().GetOne(gomock.Any(), gomock.Any()).Return(&models.Partner{ID: 1}, nil)
					return r
				}(),
			},
			args: args{
				c: func() *gin.Context {
					ctx, _ := gin.CreateTestContext(w)
					ctx.Request, _ = http.NewRequest("POST", "/v1/epi/orders", strings.NewReader(`{"partner_id": 1}`))
					return ctx
				}(),
			},
			want:    &models.Partner{ID: 1},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &epiAuth{
				partnerRepo: tt.fields.partnerRepo,
			}
			got, err := t.Authenticate(tt.args.c)
			if (err != nil) != tt.wantErr {
				t1.Errorf("Authenticate(): wantErr = %v but actual %v", tt.wantErr, err)
			}

			assert.Equalf(t1, tt.want, got, "Authenticate(%v)", tt.args.c)
		})
	}
}

func TestCalculateLazadaSign(t *testing.T) {
	t.Parallel()
	actual := calculateLazadaSign("hello1234", "key123")
	expected := "ufYU7rvXhHY3IDyZgyt6SA=="
	assert.Equal(t, expected, actual)
}

func TestLazadaCCAuthHandler(t *testing.T) {
	// Save original factory functions to restore after test
	origNewCacheHandler := newCacheHandler
	origNewPartnerRepository := newPartnerRepository
	defer func() {
		newCacheHandler = origNewCacheHandler
		newPartnerRepository = origNewPartnerRepository
	}()

	// Save original environment values
	originalAppKey := envs.Instance.LazadaCC.AppKey
	originalAppSecret := envs.Instance.LazadaCC.AppSecret
	originalPartnerID := envs.Instance.LazadaCC.PartnerId

	// Set test values
	envs.Instance.LazadaCC.AppKey = "test-app-key"
	envs.Instance.LazadaCC.AppSecret = "test-app-secret"
	envs.Instance.LazadaCC.PartnerId = 90299

	// Restore after test
	defer func() {
		envs.Instance.LazadaCC.AppKey = originalAppKey
		envs.Instance.LazadaCC.AppSecret = originalAppSecret
		envs.Instance.LazadaCC.PartnerId = originalPartnerID
	}()

	type mockSetup struct {
		cacheClient func(*gomock.Controller) cache.Handler
		partnerRepo func(*gomock.Controller) repo_interface.PartnerRepository
	}

	tests := []struct {
		name           string
		setup          mockSetup
		requestBuilder func() *http.Request
		wantStatus     int
		wantPartner    bool
	}{
		{
			name: "valid request with correct signature",
			setup: mockSetup{
				cacheClient: func(ctrl *gomock.Controller) cache.Handler {
					m := mocks1.NewMockHandler(ctrl)
					m.EXPECT().
						GetObj(gomock.Any(), fmt.Sprintf(cache.ShipperPartnerCacheKeyTmpl, LazadaCC), gomock.Any()).
						DoAndReturn(func(ctx context.Context, key string, value interface{}) error {
							wrapper := value.(*CacheWrapper)
							*wrapper = CacheWrapper{
								Partner: &models.Partner{ID: 90299, OrderCreationMethod: null.Uint8From(1)},
							}
							return nil
						})
					return m
				},
				partnerRepo: func(ctrl *gomock.Controller) repo_interface.PartnerRepository {
					return mocks2.NewMockPartnerRepository(ctrl)
				},
			},
			requestBuilder: func() *http.Request {
				// Create test request
				payload := []byte(`{"order_code":"123456"}`)
				req, _ := http.NewRequest("POST", "/lazada/cc/orders", bytes.NewBuffer(payload))

				// Set headers
				req.Header.Set("Content-Type", "application/json")

				// Generate authorization header
				// For testing, we'll create a simplified but valid signature
				// This matches the signature verification in the handler
				date := time.Now().UTC().Format(time.RFC3339)
				nonce := "test-nonce"

				// Create canonical request
				canonicalRequest := fmt.Sprintf("POST\n/global/lazada/cc/orders\n\ncontent-type:application/json\nx-acs-date:%s\nx-acs-nonce:%s\n\ncontent-type;x-acs-date;x-acs-nonce\n%x",
					date,
					nonce,
					sha256.Sum256(payload))

				// Create string to sign
				hashedCanonicalRequest := fmt.Sprintf("%x", sha256.Sum256([]byte(canonicalRequest)))
				stringToSign := fmt.Sprintf("ACS3-HMAC-SHA256\n%s", hashedCanonicalRequest)

				// Calculate signature
				h := hmac.New(sha256.New, []byte("test-app-secret"))
				h.Write([]byte(stringToSign))
				signature := hex.EncodeToString(h.Sum(nil))

				// Create authorization header
				authHeader := fmt.Sprintf("ACS3-HMAC-SHA256 Credential=test-app-key,SignedHeaders=content-type;x-acs-date;x-acs-nonce,Signature=%s", signature)

				req.Header.Set("Authorization", authHeader)
				req.Header.Set("x-acs-date", date)
				req.Header.Set("x-acs-nonce", nonce)

				return req
			},
			wantStatus:  http.StatusOK,
			wantPartner: true,
		},
		{
			name: "invalid signature",
			setup: mockSetup{
				cacheClient: func(ctrl *gomock.Controller) cache.Handler {
					return mocks1.NewMockHandler(ctrl)
				},
				partnerRepo: func(ctrl *gomock.Controller) repo_interface.PartnerRepository {
					return mocks2.NewMockPartnerRepository(ctrl)
				},
			},
			requestBuilder: func() *http.Request {
				payload := []byte(`{"order_code":"123456"}`)
				req, _ := http.NewRequest("POST", "/lazada/cc/orders", bytes.NewBuffer(payload))

				// Set invalid authorization header
				req.Header.Set("Authorization", "invalid-auth-header")
				req.Header.Set("Content-Type", "application/json")

				return req
			},
			wantStatus:  http.StatusUnauthorized,
			wantPartner: false,
		},
		{
			name: "partner not found in cache, db error",
			setup: mockSetup{
				cacheClient: func(ctrl *gomock.Controller) cache.Handler {
					m := mocks1.NewMockHandler(ctrl)
					m.EXPECT().
						GetObj(gomock.Any(), fmt.Sprintf(cache.ShipperPartnerCacheKeyTmpl, LazadaCC), gomock.Any()).
						Return(errors.New("cache miss"))
					return m
				},
				partnerRepo: func(ctrl *gomock.Controller) repo_interface.PartnerRepository {
					m := mocks2.NewMockPartnerRepository(ctrl)
					m.EXPECT().
						GetOne(gomock.Any(), models.PartnerWhere.ID.EQ(envs.Instance.LazadaCC.PartnerId)).
						Return(nil, errors.New("db error"))
					return m
				},
			},
			requestBuilder: func() *http.Request {
				// Create test request with valid signature
				payload := []byte(`{"order_code":"123456"}`)
				req, _ := http.NewRequest("POST", "/lazada/cc/orders", bytes.NewBuffer(payload))

				// Set headers
				req.Header.Set("Content-Type", "application/json")

				// Generate authorization header
				date := time.Now().UTC().Format(time.RFC3339)
				nonce := "test-nonce"

				// Create canonical request
				canonicalRequest := fmt.Sprintf("POST\n/global/lazada/cc/orders\n\ncontent-type:application/json\nx-acs-date:%s\nx-acs-nonce:%s\n\ncontent-type;x-acs-date;x-acs-nonce\n%x",
					date,
					nonce,
					sha256.Sum256(payload))

				// Create string to sign
				hashedCanonicalRequest := fmt.Sprintf("%x", sha256.Sum256([]byte(canonicalRequest)))
				stringToSign := fmt.Sprintf("ACS3-HMAC-SHA256\n%s", hashedCanonicalRequest)

				// Calculate signature
				h := hmac.New(sha256.New, []byte("test-app-secret"))
				h.Write([]byte(stringToSign))
				signature := hex.EncodeToString(h.Sum(nil))

				// Create authorization header
				authHeader := fmt.Sprintf("ACS3-HMAC-SHA256 Credential=test-app-key,SignedHeaders=content-type;x-acs-date;x-acs-nonce,Signature=%s", signature)

				req.Header.Set("Authorization", authHeader)
				req.Header.Set("x-acs-date", date)
				req.Header.Set("x-acs-nonce", nonce)

				return req
			},
			wantStatus:  http.StatusInternalServerError,
			wantPartner: false,
		},
		{
			name: "partner found but order creation not enabled",
			setup: mockSetup{
				cacheClient: func(ctrl *gomock.Controller) cache.Handler {
					m := mocks1.NewMockHandler(ctrl)
					m.EXPECT().
						GetObj(gomock.Any(), fmt.Sprintf(cache.ShipperPartnerCacheKeyTmpl, LazadaCC), gomock.Any()).
						Return(errors.New("cache miss"))
					return m
				},
				partnerRepo: func(ctrl *gomock.Controller) repo_interface.PartnerRepository {
					m := mocks2.NewMockPartnerRepository(ctrl)
					m.EXPECT().
						GetOne(gomock.Any(), models.PartnerWhere.ID.EQ(envs.Instance.LazadaCC.PartnerId)).
						Return(&models.Partner{ID: 90299}, nil) // No OrderCreationMethod set
					return m
				},
			},
			requestBuilder: func() *http.Request {
				// Create test request with valid signature
				payload := []byte(`{"order_code":"123456"}`)
				req, _ := http.NewRequest("POST", "/lazada/cc/orders", bytes.NewBuffer(payload))

				// Set headers
				req.Header.Set("Content-Type", "application/json")

				// Generate authorization header
				date := time.Now().UTC().Format(time.RFC3339)
				nonce := "test-nonce"

				// Create canonical request
				canonicalRequest := fmt.Sprintf("POST\n/global/lazada/cc/orders\n\ncontent-type:application/json\nx-acs-date:%s\nx-acs-nonce:%s\n\ncontent-type;x-acs-date;x-acs-nonce\n%x",
					date,
					nonce,
					sha256.Sum256(payload))

				// Create string to sign
				hashedCanonicalRequest := fmt.Sprintf("%x", sha256.Sum256([]byte(canonicalRequest)))
				stringToSign := fmt.Sprintf("ACS3-HMAC-SHA256\n%s", hashedCanonicalRequest)

				// Calculate signature
				h := hmac.New(sha256.New, []byte("test-app-secret"))
				h.Write([]byte(stringToSign))
				signature := hex.EncodeToString(h.Sum(nil))

				// Create authorization header
				authHeader := fmt.Sprintf("ACS3-HMAC-SHA256 Credential=test-app-key,SignedHeaders=content-type;x-acs-date;x-acs-nonce,Signature=%s", signature)

				req.Header.Set("Authorization", authHeader)
				req.Header.Set("x-acs-date", date)
				req.Header.Set("x-acs-nonce", nonce)

				return req
			},
			wantStatus:  http.StatusInternalServerError,
			wantPartner: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Setup router
			gin.SetMode(gin.TestMode)
			router := gin.New()

			// Override factory functions with mocks
			newCacheHandler = func() cache.Handler {
				return tt.setup.cacheClient(ctrl)
			}
			newPartnerRepository = func() repo_interface.PartnerRepository {
				return tt.setup.partnerRepo(ctrl)
			}

			// Setup handler with mocks
			handler := LazadaCCAuthHandler()

			// Setup test endpoint
			router.POST("/lazada/cc/orders", handler, func(c *gin.Context) {
				// Check if partner was set in context
				partner, exists := c.Get(middlewareCfg.PartnerKey)
				if tt.wantPartner != exists {
					t.Errorf("Partner in context: got %v, want %v", exists, tt.wantPartner)
				}
				if exists && partner == nil {
					t.Error("Partner is nil but exists in context")
				}
				c.Status(http.StatusOK)
			})

			// Create test recorder and request
			w := httptest.NewRecorder()
			req := tt.requestBuilder()

			// Perform request
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, tt.wantStatus, w.Code)
		})
	}
}

func TestLazadaMMAuthHandler(t *testing.T) {
	// Save original factory functions to restore after test
	origNewCacheHandler := newCacheHandler
	origNewPartnerRepository := newPartnerRepository
	defer func() {
		newCacheHandler = origNewCacheHandler
		newPartnerRepository = origNewPartnerRepository
	}()

	// Save original environment values
	originalAppKey := envs.Instance.LazadaCC.AppKey
	originalAppSecret := envs.Instance.LazadaCC.AppSecret
	originalPartnerID := envs.Instance.LazadaMMAIDC.PartnerId

	// Set test values
	envs.Instance.LazadaCC.AppKey = "test-app-key"
	envs.Instance.LazadaCC.AppSecret = "test-app-secret"
	envs.Instance.LazadaMMAIDC.PartnerId = 90300

	// Restore after test
	defer func() {
		envs.Instance.LazadaCC.AppKey = originalAppKey
		envs.Instance.LazadaCC.AppSecret = originalAppSecret
		envs.Instance.LazadaMMAIDC.PartnerId = originalPartnerID
	}()

	type mockSetup struct {
		cacheClient func(*gomock.Controller) cache.Handler
		partnerRepo func(*gomock.Controller) repo_interface.PartnerRepository
	}

	tests := []struct {
		name           string
		setup          mockSetup
		requestBuilder func() *http.Request
		wantStatus     int
		wantPartner    bool
	}{
		{
			name: "valid request with correct signature",
			setup: mockSetup{
				cacheClient: func(ctrl *gomock.Controller) cache.Handler {
					m := mocks1.NewMockHandler(ctrl)
					m.EXPECT().
						GetObj(gomock.Any(), fmt.Sprintf(cache.ShipperPartnerCacheKeyTmpl, LazadaMMAIDC), gomock.Any()).
						DoAndReturn(func(ctx context.Context, key string, value interface{}) error {
							wrapper := value.(*CacheWrapper)
							*wrapper = CacheWrapper{
								Partner: &models.Partner{ID: 90300, OrderCreationMethod: null.Uint8From(1)},
							}
							return nil
						})
					return m
				},
				partnerRepo: func(ctrl *gomock.Controller) repo_interface.PartnerRepository {
					return mocks2.NewMockPartnerRepository(ctrl)
				},
			},
			requestBuilder: func() *http.Request {
				// Create test request
				payload := []byte(`{"order_code":"123456"}`)
				req, _ := http.NewRequest("POST", "/lazada/mm/orders", bytes.NewBuffer(payload))

				// Set headers
				req.Header.Set("Content-Type", "application/json")

				// Generate authorization header
				date := time.Now().UTC().Format(time.RFC3339)
				nonce := "test-nonce"

				// Create canonical request
				canonicalRequest := fmt.Sprintf("POST\n/global/lazada/mm/orders\n\ncontent-type:application/json\nx-acs-date:%s\nx-acs-nonce:%s\n\ncontent-type;x-acs-date;x-acs-nonce\n%x",
					date,
					nonce,
					sha256.Sum256(payload))

				// Create string to sign
				hashedCanonicalRequest := fmt.Sprintf("%x", sha256.Sum256([]byte(canonicalRequest)))
				stringToSign := fmt.Sprintf("ACS3-HMAC-SHA256\n%s", hashedCanonicalRequest)

				// Calculate signature
				h := hmac.New(sha256.New, []byte("test-app-secret"))
				h.Write([]byte(stringToSign))
				signature := hex.EncodeToString(h.Sum(nil))

				// Create authorization header
				authHeader := fmt.Sprintf("ACS3-HMAC-SHA256 Credential=test-app-key,SignedHeaders=content-type;x-acs-date;x-acs-nonce,Signature=%s", signature)

				req.Header.Set("Authorization", authHeader)
				req.Header.Set("x-acs-date", date)
				req.Header.Set("x-acs-nonce", nonce)

				return req
			},
			wantStatus:  http.StatusOK,
			wantPartner: true,
		},
		{
			name: "invalid signature",
			setup: mockSetup{
				cacheClient: func(ctrl *gomock.Controller) cache.Handler {
					return mocks1.NewMockHandler(ctrl)
				},
				partnerRepo: func(ctrl *gomock.Controller) repo_interface.PartnerRepository {
					return mocks2.NewMockPartnerRepository(ctrl)
				},
			},
			requestBuilder: func() *http.Request {
				payload := []byte(`{"order_code":"123456"}`)
				req, _ := http.NewRequest("POST", "/lazada/mm/orders", bytes.NewBuffer(payload))

				// Set invalid authorization header
				req.Header.Set("Authorization", "invalid-auth-header")
				req.Header.Set("Content-Type", "application/json")

				return req
			},
			wantStatus:  http.StatusUnauthorized,
			wantPartner: false,
		},
		{
			name: "partner not found in cache, db error",
			setup: mockSetup{
				cacheClient: func(ctrl *gomock.Controller) cache.Handler {
					m := mocks1.NewMockHandler(ctrl)
					m.EXPECT().
						GetObj(gomock.Any(), fmt.Sprintf(cache.ShipperPartnerCacheKeyTmpl, LazadaMMAIDC), gomock.Any()).
						Return(errors.New("cache miss"))
					return m
				},
				partnerRepo: func(ctrl *gomock.Controller) repo_interface.PartnerRepository {
					m := mocks2.NewMockPartnerRepository(ctrl)
					m.EXPECT().
						GetOne(gomock.Any(), models.PartnerWhere.ID.EQ(envs.Instance.LazadaMMAIDC.PartnerId)).
						Return(nil, errors.New("db error"))
					return m
				},
			},
			requestBuilder: func() *http.Request {
				// Create test request with valid signature
				payload := []byte(`{"order_code":"123456"}`)
				req, _ := http.NewRequest("POST", "/lazada/mm/orders", bytes.NewBuffer(payload))

				// Set headers
				req.Header.Set("Content-Type", "application/json")

				// Generate authorization header
				date := time.Now().UTC().Format(time.RFC3339)
				nonce := "test-nonce"

				// Create canonical request
				canonicalRequest := fmt.Sprintf("POST\n/global/lazada/mm/orders\n\ncontent-type:application/json\nx-acs-date:%s\nx-acs-nonce:%s\n\ncontent-type;x-acs-date;x-acs-nonce\n%x",
					date,
					nonce,
					sha256.Sum256(payload))

				// Create string to sign
				hashedCanonicalRequest := fmt.Sprintf("%x", sha256.Sum256([]byte(canonicalRequest)))
				stringToSign := fmt.Sprintf("ACS3-HMAC-SHA256\n%s", hashedCanonicalRequest)

				// Calculate signature
				h := hmac.New(sha256.New, []byte("test-app-secret"))
				h.Write([]byte(stringToSign))
				signature := hex.EncodeToString(h.Sum(nil))

				// Create authorization header
				authHeader := fmt.Sprintf("ACS3-HMAC-SHA256 Credential=test-app-key,SignedHeaders=content-type;x-acs-date;x-acs-nonce,Signature=%s", signature)

				req.Header.Set("Authorization", authHeader)
				req.Header.Set("x-acs-date", date)
				req.Header.Set("x-acs-nonce", nonce)

				return req
			},
			wantStatus:  http.StatusInternalServerError,
			wantPartner: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Replace factory functions with mocks
			newCacheHandler = func() cache.Handler {
				return tt.setup.cacheClient(ctrl)
			}
			newPartnerRepository = func() repo_interface.PartnerRepository {
				return tt.setup.partnerRepo(ctrl)
			}

			// Setup router
			router := gin.New()
			router.Use(gin.Recovery())

			// Setup handler with mocks
			handler := LazadaMMAuthHandler()

			// Setup test endpoint
			router.POST("/lazada/mm/orders", handler, func(c *gin.Context) {
				// Check if partner was set in context
				partner, exists := c.Get(middlewareCfg.PartnerKey)
				if tt.wantPartner != exists {
					t.Errorf("Partner in context: got %v, want %v", exists, tt.wantPartner)
				}
				if exists && partner == nil {
					t.Error("Partner is nil but exists in context")
				}
				c.Status(http.StatusOK)
			})

			// Create test recorder and request
			w := httptest.NewRecorder()
			req := tt.requestBuilder()

			// Perform request
			router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, tt.wantStatus, w.Code)
		})
	}
}

func Test_shopeeAuthHandler(t *testing.T) {
	t.Parallel()
	envs.Instance.Shopee.AppKey = "njv_shopee_account"
	envs.Instance.Shopee.AppSecret = "njv_shopee_secret"
	envs.Instance.NvBypassAuthValidation = false
	tests := map[string]struct {
		JWT             func() string
		ExpectedRetCode order.ShopeeRetCode
	}{
		"valid JWT": {
			JWT: func() string {
				token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
					"data":      "data",
					"timestamp": time.Now().Unix(),
				})
				token.Header["account"] = envs.Instance.Shopee.AppKey
				signedToken, _ := token.SignedString([]byte(envs.Instance.Shopee.AppSecret))
				return signedToken
			},
			ExpectedRetCode: order.ShopeeRetCodeSuccess,
		},
		"Invalid JWT": {
			JWT: func() string {
				return "abc"
			},
			ExpectedRetCode: order.ShopeeRetCodeJWTInvalid,
		},
	}

	for name, tc := range tests {
		t.Run(name, func(t *testing.T) {
			gin.SetMode(gin.TestMode)
			router := gin.New()

			testCtrl := gomock.NewController(t)
			defer testCtrl.Finish()
			mockCacheClient := mocks1.NewMockHandler(testCtrl)
			mockCacheClient.EXPECT().GetObj(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(nil).SetArg(2,
				CacheWrapper{
					Partner: &models.Partner{ID: 1000},
				},
			).AnyTimes()
			mockPartnerRepo := mocks2.NewMockPartnerRepository(testCtrl)

			reqBody := &order.ShopeeRequest{Jwt: tc.JWT()}
			w := httptest.NewRecorder()
			reqBodyBytes, _ := json.Marshal(reqBody)
			req, _ := http.NewRequest("POST", "/shopee/auth", bytes.NewBuffer(reqBodyBytes))

			router.POST("/shopee/auth", shopeeAuthHandler(mockCacheClient, mockPartnerRepo), func(context *gin.Context) {
				context.JSON(http.StatusOK, order.NewShopeeResponse(order.ShopeeRetCodeSuccess, "success"))
			})

			router.ServeHTTP(w, req)

			respBody := w.Body
			shopeeResp := &order.ShopeeResponse{}
			_ = json.Unmarshal(respBody.Bytes(), shopeeResp)

			assert.Equal(t, tc.ExpectedRetCode, shopeeResp.RetCode)
		})
	}
}
