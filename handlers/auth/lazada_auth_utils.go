package auth

import (
	"bytes"
	"crypto"
	"crypto/hmac"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/hex"
	"encoding/pem"
	"errors"
	"io"
	"net/http"
	"net/textproto"
	"net/url"
	"sort"
	"strconv"
	"strings"

	"github.com/tjfoc/gmsm/sm3"
	"github.com/volatiletech/null/v8"

	"git.ninjavan.co/3pl/utils"
)

const (
	PEM_BEGIN = "-----BEGIN RSA PRIVATE KEY-----\n"
	PEM_END   = "\n-----END RSA PRIVATE KEY-----"
)

type LazadaRequest struct {
	Protocol *string
	Port     *int
	Method   *string
	Pathname *string
	Domain   *string
	Headers  map[string]*string
	Query    map[string]*string
	Body     io.Reader
}

type Sorter struct {
	Keys []string
	Vals []string
}

func newSorter(m map[string]string) *Sorter {
	hs := &Sorter{
		Keys: make([]string, 0, len(m)),
		Vals: make([]string, 0, len(m)),
	}

	for k, v := range m {
		hs.Keys = append(hs.Keys, k)
		hs.Vals = append(hs.Vals, v)
	}
	return hs
}

// Sort is an additional function for function SignHeader.
func (hs *Sorter) Sort() {
	sort.Sort(hs)
}

// Len is an additional function for function SignHeader.
func (hs *Sorter) Len() int {
	return len(hs.Vals)
}

// Less is an additional function for function SignHeader.
func (hs *Sorter) Less(i, j int) bool {
	return bytes.Compare([]byte(hs.Keys[i]), []byte(hs.Keys[j])) < 0
}

// Swap is an additional function for function SignHeader.
func (hs *Sorter) Swap(i, j int) {
	hs.Vals[i], hs.Vals[j] = hs.Vals[j], hs.Vals[i]
	hs.Keys[i], hs.Keys[j] = hs.Keys[j], hs.Keys[i]
}

func Hash(raw []byte, signatureAlgorithm *string) (_result []byte) {
	signType := null.StringFromPtr(signatureAlgorithm).String
	if signType == "ACS3-HMAC-SHA256" || signType == "ACS3-RSA-SHA256" {
		h := sha256.New()
		h.Write(raw)
		return h.Sum(nil)
	} else if signType == "ACS3-HMAC-SM3" {
		h := sm3.New()
		h.Write(raw)
		return h.Sum(nil)
	}
	return nil
}

func getAuthorization(request *LazadaRequest, signatureAlgorithm *string, payload *string, accessKey *string, accessKeySecret *string) (_result *string) {
	canonicalURI := null.StringFromPtr(request.Pathname).String
	if canonicalURI == "" {
		canonicalURI = "/"
	}

	canonicalURI = strings.Replace(canonicalURI, "+", "%20", -1)
	canonicalURI = strings.Replace(canonicalURI, "*", "%2A", -1)
	canonicalURI = strings.Replace(canonicalURI, "%7E", "~", -1)

	method := null.StringFromPtr(request.Method).String
	canonicalQueryString := getCanonicalQueryString(request.Query)
	canonicalheaders, signedHeaders := getCanonicalHeaders(request.Headers)

	canonicalRequest := method + "\n" + canonicalURI + "\n" + canonicalQueryString + "\n" + canonicalheaders + "\n" +
		strings.Join(signedHeaders, ";") + "\n" + null.StringFromPtr(payload).String
	signType := null.StringFromPtr(signatureAlgorithm).String
	StringToSign := signType + "\n" + hex.EncodeToString(Hash([]byte(canonicalRequest), signatureAlgorithm))
	signature := hex.EncodeToString(SignatureMethod(null.StringFromPtr(accessKeySecret).String, StringToSign, signType))
	auth := signType + " Credential=" + null.StringFromPtr(accessKey).String + ",SignedHeaders=" +
		strings.Join(signedHeaders, ";") + ",Signature=" + signature
	return null.StringFrom(auth).Ptr()
}

func SignatureMethod(secret, source, signatureAlgorithm string) []byte {
	if signatureAlgorithm == "ACS3-HMAC-SHA256" {
		h := hmac.New(sha256.New, []byte(secret))
		h.Write([]byte(source))
		return h.Sum(nil)
	} else if signatureAlgorithm == "ACS3-HMAC-SM3" {
		h := hmac.New(sm3.New, []byte(secret))
		h.Write([]byte(source))
		return h.Sum(nil)
	} else if signatureAlgorithm == "ACS3-RSA-SHA256" {
		return rsaSign(source, secret)
	}
	return nil
}

func rsaSign(content, secret string) []byte {
	h := crypto.SHA256.New()
	h.Write([]byte(content))
	hashed := h.Sum(nil)
	priv, err := parsePrivateKey(secret)
	if err != nil {
		return nil
	}
	sign, err := rsa.SignPKCS1v15(rand.Reader, priv, crypto.SHA256, hashed)
	if err != nil {
		return nil
	}
	return sign
}

func parsePrivateKey(privateKey string) (*rsa.PrivateKey, error) {
	privateKey = formatPrivateKey(privateKey)
	block, _ := pem.Decode([]byte(privateKey))
	if block == nil {
		return nil, errors.New("PrivateKey is invalid")
	}
	priKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	switch priKey.(type) {
	case *rsa.PrivateKey:
		return priKey.(*rsa.PrivateKey), nil
	default:
		return nil, nil
	}
}

func formatPrivateKey(privateKey string) string {
	if !strings.HasPrefix(privateKey, PEM_BEGIN) {
		privateKey = PEM_BEGIN + privateKey
	}

	if !strings.HasSuffix(privateKey, PEM_END) {
		privateKey += PEM_END
	}
	return privateKey
}

func getCanonicalHeaders(headers map[string]*string) (string, []string) {
	tmp := make(map[string]string)
	tmpHeader := http.Header{}
	for k, v := range headers {
		if strings.HasPrefix(strings.ToLower(k), "x-acs-") || strings.ToLower(k) == "host" ||
			strings.ToLower(k) == "content-type" {
			tmp[strings.ToLower(k)] = strings.TrimSpace(null.StringFromPtr(v).String)
			tmpHeader.Add(strings.ToLower(k), strings.TrimSpace(null.StringFromPtr(v).String))
		}
	}
	hs := newSorter(tmp)

	// Sort the temp by the ascending order
	hs.Sort()
	canonicalheaders := ""
	for _, key := range hs.Keys {
		vals := tmpHeader[textproto.CanonicalMIMEHeaderKey(key)]
		sort.Strings(vals)
		canonicalheaders += key + ":" + strings.Join(vals, ",") + "\n"
	}

	return canonicalheaders, hs.Keys
}

func getCanonicalQueryString(query map[string]*string) string {
	canonicalQueryString := ""
	if utils.IsNil(query) {
		return canonicalQueryString
	}
	tmp := make(map[string]string)
	for k, v := range query {
		tmp[k] = null.StringFromPtr(v).String
	}

	hs := newSorter(tmp)

	// Sort the temp by the ascending order
	hs.Sort()
	for i := range hs.Keys {
		if hs.Vals[i] != "" {
			canonicalQueryString += "&" + hs.Keys[i] + "=" + url.QueryEscape(hs.Vals[i])
		} else {
			canonicalQueryString += "&" + hs.Keys[i] + "="
		}
	}
	canonicalQueryString = strings.Replace(canonicalQueryString, "+", "%20", -1)
	canonicalQueryString = strings.Replace(canonicalQueryString, "*", "%2A", -1)
	canonicalQueryString = strings.Replace(canonicalQueryString, "%7E", "~", -1)

	if canonicalQueryString != "" {
		canonicalQueryString = strings.TrimLeft(canonicalQueryString, "&")
	}
	return canonicalQueryString
}

func getLazadaRequest(httpReq *http.Request) *LazadaRequest {
	lazadaReq := &LazadaRequest{}

	// Convert Method
	lazadaReq.Method = null.StringFrom(httpReq.Method).Ptr()

	// Lazada client will recog path will "/global/v1....." after "api.ninjavan.co"
	// But 4pl just receive path with "/v1/..."
	lazadaReq.Pathname = null.StringFrom("/global" + httpReq.URL.Path).Ptr()

	// Convert Domain
	lazadaReq.Domain = null.StringFrom(httpReq.URL.Host).Ptr()

	// Convert Protocol
	protocol := "http"
	if httpReq.URL.Scheme != "" {
		protocol = httpReq.URL.Scheme
	}
	lazadaReq.Protocol = null.StringFrom(protocol).Ptr()

	// Convert Port
	port := 80
	if httpReq.URL.Port() != "" {
		port, _ = strconv.Atoi(httpReq.URL.Port())
	} else if protocol == "https" {
		port = 443
	}
	lazadaReq.Port = null.IntFrom(port).Ptr()

	// Convert Headers
	lazadaReq.Headers = make(map[string]*string)
	for key, values := range httpReq.Header {
		if len(values) > 0 {
			lazadaReq.Headers[key] = null.StringFrom(values[0]).Ptr()
		}
	}

	// Convert Query Parameters
	lazadaReq.Query = make(map[string]*string)
	for key, values := range httpReq.URL.Query() {
		if len(values) > 0 {
			lazadaReq.Query[key] = null.StringFrom(values[0]).Ptr()
		}
	}

	// Convert Body
	lazadaReq.Body = httpReq.Body

	return lazadaReq
}
