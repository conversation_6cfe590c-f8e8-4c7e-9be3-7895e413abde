package shipment

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"google.golang.org/api/option"

	assetApi "bitbucket.ninjavan.co/apiclients/assets-proxy-api-client---go/assets"

	parcelConst "git.ninjavan.co/3pl/configs/parcel"
	serviceConst "git.ninjavan.co/3pl/configs/services"
	shipmentConst "git.ninjavan.co/3pl/configs/shipment"
	"git.ninjavan.co/3pl/envs"
	"git.ninjavan.co/3pl/errors/fplerror"
	update_interface "git.ninjavan.co/3pl/handlers/shipment/shipment_interface"
	shipmentRequest "git.ninjavan.co/3pl/httpmodels/shipment"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/services"
	"git.ninjavan.co/3pl/services/auth"
	"git.ninjavan.co/3pl/services/gcs"
	"git.ninjavan.co/3pl/services/storage"
	"git.ninjavan.co/3pl/utils"
	"git.ninjavan.co/3pl/utils/loggerutils"
)

type (
	documentCreatorHandler struct {
		processor update_interface.DocumentCreateProcessor
	}

	documentCreateProcessor struct {
		cloudStorage         storage.CloudStorage
		shipmentRepo         repo_interface.ShipmentRepository
		shipmentDocumentRepo repo_interface.ShipmentDocumentsRepository
		documentRepo         repo_interface.DocumentsRepository
		transactionRepo      repo_interface.TransactionRepoInterface
		shipmentParcelRepo   repo_interface.ShipmentParcelRepository
		shipmentLogRepo      repo_interface.ShipmentLogRepository
		generateURLAPI       assetApi.Requester
	}
)

func NewDocumentCreateHandler() update_interface.DocumentCreateHandler {
	return &documentCreatorHandler{
		processor: NewDocumentCreateProcessor(),
	}
}

func (s *documentCreatorHandler) Create(ctx context.Context, req shipmentRequest.DocumentCreateRequest, userInfo auth.UserInfo) error {
	var (
		shipment         *models.Shipment
		document         *models.Document
		shipmentDocument *models.ShipmentDocument
		url, fileType    *string
		err              error
	)

	if err = s.validate(req); err != nil {
		return err
	}

	if shipment, err = s.processor.GetShipment(ctx, req); err != nil {
		return err
	}

	oldShipment := *shipment

	if req.Payload.Name != nil && req.Payload.URI != nil {
		fileURI := *req.Payload.URI
		fileName := *req.Payload.Name

		bucket := envs.Instance.NvGcsInternalBucket
		filePath := strings.ReplaceAll(fileURI, fmt.Sprintf("gs://%s/", bucket), "")

		if url, err = s.processor.CreateAssetProxy(ctx, bucket, filePath); err != nil {
			return err
		}

		if fileType, err = s.processor.GetFileType(ctx, filePath); err != nil {
			return err
		}

		expiredAt := time.Now().Add(time.Duration(envs.Instance.MawbDocumentAssetExpiresInHour) * time.Hour)

		document = &models.Document{
			Type:      parcelConst.MAWB,
			FileName:  fileName,
			FileType:  *fileType,
			FileURI:   fileURI,
			ExpiredAt: expiredAt,
		}

		shipmentDocument = &models.ShipmentDocument{
			ShipmentID:     shipment.ID,
			AssertProxyURL: *url,
			ExpiredAt:      expiredAt,
		}
	}

	if err = s.processTransportService(ctx, req, shipment); err != nil {
		return err
	}

	if err = s.processor.Update(ctx, document, shipmentDocument, shipment, oldShipment, userInfo); err != nil {
		return err
	}

	return nil
}

func (s *documentCreatorHandler) validate(req shipmentRequest.DocumentCreateRequest) error {
	if req.Payload.Name == nil && req.Payload.URI == nil && req.Payload.TransportService == nil {
		return fplerror.ErrBadRequest.NewWithoutStack("Missing fields")
	}

	if (req.Payload.Name == nil && req.Payload.URI != nil) ||
		(req.Payload.Name != nil && req.Payload.URI == nil) {
		return fplerror.ErrBadRequest.NewWithoutStack("File Name And File URI must be required")
	}

	return nil
}

func (s *documentCreatorHandler) processTransportService(ctx context.Context, req shipmentRequest.DocumentCreateRequest, shipment *models.Shipment) error {
	if req.Payload.TransportService == nil {
		return nil
	}

	var (
		validate bool
		err      error
	)

	if validate, err = s.processor.ValidateTransportService(ctx, shipment); err != nil {
		return err
	}

	if !validate {
		return fplerror.ErrBadRequest.NewWithoutStack("Shipment must contain TT MMCC air freight MY goods to choose service")
	}

	metadata := &repositories.Metadata{}
	_ = json.Unmarshal([]byte(shipment.Metadata), metadata)
	metadata.TransportService = null.Uint8FromPtr(req.Payload.TransportService)
	metadataStr, _ := json.Marshal(metadata)
	shipment.Metadata = string(metadataStr)

	return nil
}

const documentCreateModuleName = "shipment-document-create-processor"

func NewDocumentCreateProcessor() update_interface.DocumentCreateProcessor {
	return &documentCreateProcessor{
		cloudStorage: gcs.NewGCSService(
			envs.Instance.NvGcsInternalBucket, envs.Instance.NvGcsBucketDir, envs.Instance.ShipmentDocumentSignedTtlInMinutes,
			option.WithCredentialsFile(envs.Instance.GoogleApplicationCredentials),
		),
		shipmentRepo:         repositories.NewShipmentRepository(&repositories.MetadataRepo{}),
		shipmentDocumentRepo: repositories.NewShipmentDocumentsRepository(),
		shipmentParcelRepo:   repositories.NewShipmentParcelRepository(),
		documentRepo:         repositories.NewDocuments(),
		transactionRepo:      repositories.NewTransactionRepo(),
		shipmentLogRepo:      repositories.NewShipmentLogRepository(),
		generateURLAPI:       assetApi.NewClient(services.GetAuthClient()),
	}
}

func (p *documentCreateProcessor) GetShipment(ctx context.Context, req shipmentRequest.DocumentCreateRequest) (*models.Shipment, error) {
	var (
		shipment         *models.Shipment
		shipmentDocument *models.ShipmentDocument
		err              error
	)

	shipmentID := req.Uri.ShipmentID

	if shipment, err = p.shipmentRepo.FindOneWithCtx(ctx, shipmentID); err != nil {
		if err != sql.ErrNoRows {
			loggerutils.Ctx(ctx, documentCreateModuleName).Err(err).Uint("shipment_id", shipmentID).Msg("failed-to-get-shipment")
		}
		return nil, fplerror.ErrEntityNotFound.NewWithoutStack("shipment.id.not_found", shipmentID)
	}

	if req.Payload.Name == nil && req.Payload.URI == nil {
		return shipment, nil
	}

	if shipmentDocument, err = p.shipmentDocumentRepo.GetByShipmentID(ctx, shipmentID); err != nil && err != sql.ErrNoRows {
		loggerutils.Ctx(ctx, documentCreateModuleName).Err(err).Uint("shipment_id", shipmentID).Msg("failed-to-get-shipment-document")
		return nil, fplerror.ErrInternal.WithMsgInternalDB()
	}
	if shipmentDocument != nil {
		return nil, fplerror.ErrBadRequest.NewWithoutStack("Shipment %d already has document before", shipmentID)
	}

	return shipment, nil
}

func (p *documentCreateProcessor) CreateAssetProxy(ctx context.Context, bucket string, filePath string) (*string, error) {
	var (
		assetResponse assetApi.Response
		err           error
	)

	if assetResponse, err = p.generateURLAPI.CreateAsset(assetApi.CreateAssetReq{
		Bucket:    bucket,
		FilePath:  filePath,
		ExpiresIn: int64(envs.Instance.MawbDocumentAssetExpiresInHour),
	}); err != nil {
		loggerutils.Ctx(ctx, documentCreateModuleName).Err(err).Msg("fail-to-create-asset-proxy")
		return nil, fplerror.ErrBadRequest.NewWithoutStack("Can not get assert proxy url")
	}

	if assetResponse.Error != nil {
		loggerutils.Ctx(ctx, documentCreateModuleName).Err(err).Msg("get-create-asset-proxy-error")
		return nil, fplerror.ErrBadRequest.NewWithoutStack("Can not get assert proxy url")
	}

	return null.StringFrom(assetResponse.Data.ResourceURL).Ptr(), nil
}

func (p *documentCreateProcessor) Update(ctx context.Context, document *models.Document, shipmentDocument *models.ShipmentDocument, shipment *models.Shipment, oldShipment models.Shipment, userInfo auth.UserInfo) error {
	txn, err := p.transactionRepo.BeginTransaction(ctx)
	if err != nil {
		loggerutils.Ctx(ctx, documentCreateModuleName).Err(err).Msg("can-not-begin-transaction")
		return fplerror.ErrInternal.WithMsgInternalDB()
	}

	defer func() {
		p.transactionRepo.ProcessTransaction(txn, err)
	}()

	if document != nil && shipmentDocument != nil {
		if err = p.documentRepo.CreateWithTxn(ctx, document, txn); err != nil {
			loggerutils.Ctx(ctx, documentCreateModuleName).Error().Uint("shipment_id", shipmentDocument.ShipmentID).Err(err).Msg("failed-to-create-document")
			return fplerror.ErrInternal.WithMsgInternalDB()
		}

		shipmentDocument.DocumentID = document.ID

		if err = p.shipmentDocumentRepo.CreateWithTxn(ctx, shipmentDocument, txn); err != nil {
			loggerutils.Ctx(ctx, documentCreateModuleName).Error().Uint("shipment_id", shipmentDocument.ShipmentID).Err(err).Msg("failed-to-create-shipment-document")
			return fplerror.ErrInternal.WithMsgInternalDB()
		}
	}

	if err = p.UpdateShipment(ctx, txn, shipment, oldShipment, shipmentDocument, userInfo); err != nil {
		return fplerror.ErrInternal.WithMsgInternalDB()
	}

	return nil
}

func (p *documentCreateProcessor) GetFileType(ctx context.Context, fileURI string) (*string, error) {
	fileObject, err := p.cloudStorage.GetObject(ctx, fileURI)
	if err != nil {
		loggerutils.Ctx(ctx, documentCreateModuleName).Err(err).Str("file_uri", fileURI).Msg("can-not-get-file-object")
		return nil, fplerror.ErrBadRequest.NewWithoutStack("Can not get file")
	}

	return null.StringFrom(fileObject.ContentType()).Ptr(), nil
}

func (p *documentCreateProcessor) ValidateTransportService(ctx context.Context, shipment *models.Shipment) (bool, error) {
	if shipment.DestinationCountry != utils.CountryCodeMY || shipment.Type != null.Uint8From(uint8(shipmentConst.TypeAir)) {
		return false, nil
	}

	var (
		tiktokParcelsCount uint
		err                error
	)

	if tiktokParcelsCount, err = p.shipmentParcelRepo.GetNoOfItemsByPartnerIDAndServiceType(
		ctx,
		shipment.ID,
		envs.Instance.Tiktok.PartnerId,
		uint8(serviceConst.TypeMMCCB2C),
	); err != nil {
		loggerutils.Ctx(ctx, documentCreateModuleName).Error().Uint("shipment_id", shipment.ID).Err(err).Msg("failed-to-count-tiktok-parcels")
		return false, fplerror.ErrInternal.WithMsgInternalDB()
	}

	if tiktokParcelsCount == 0 {
		return false, nil
	}

	return true, nil
}

func (p *documentCreateProcessor) UpdateShipment(ctx context.Context, txn boil.ContextTransactor, shipment *models.Shipment, oldShipment models.Shipment, shipmentDocument *models.ShipmentDocument, userInfo auth.UserInfo) error {
	if shipment == nil {
		return nil
	}

	var (
		shipmentLog  *models.ShipmentLogTab
		shipmentLogs models.ShipmentLogTabSlice
		err          error
	)

	if err = p.shipmentRepo.Update(ctx, shipment, txn); err != nil {
		loggerutils.Ctx(ctx, documentCreateModuleName).Error().Uint("shipment_id", shipment.ID).Err(err).Msg("failed-to-update-shipment")
		return err
	}

	if shipmentLog = buildShipmentLogForUpdateTransportService(oldShipment, shipment, userInfo.Email); shipmentLog != nil {
		shipmentLogs = append(shipmentLogs, shipmentLog)
	}

	if shipmentLog = buildShipmentLogForMAWBDocument(shipmentDocument, uint8(shipmentConst.MAWBDocumentLogActionAdd), userInfo.Email); shipmentLog != nil {
		shipmentLogs = append(shipmentLogs, shipmentLog)
	}

	if err = p.shipmentLogRepo.BulkCreate(ctx, shipmentLogs, txn); err != nil {
		loggerutils.Ctx(ctx, updatorModuleName).Error().Uint("shipment_id", shipment.ID).Err(err).Msg("fail-to-save-shipment-log")
		return err
	}

	return nil
}

func buildShipmentLogForUpdateTransportService(oldShipment models.Shipment, newShipment *models.Shipment, createdBy string) *models.ShipmentLogTab {
	originFields := make(map[string]interface{})
	updatedFields := make(map[string]interface{})

	// Compare metadata changes
	var oldMeta, newMeta repositories.Metadata
	_ = json.Unmarshal([]byte(oldShipment.Metadata), &oldMeta)
	_ = json.Unmarshal([]byte(newShipment.Metadata), &newMeta)

	metadataAllowFields := []string{"TransportService"}

	if originMetaFields, updatedMetaFields := utils.CompareStructs(oldMeta, newMeta, metadataAllowFields, ""); len(updatedMetaFields) > 0 {
		for k, v := range originMetaFields {
			originFields[fmt.Sprintf("metadata.%s", k)] = v
		}
		for k, v := range updatedMetaFields {
			updatedFields[fmt.Sprintf("metadata.%s", k)] = v
		}
	}

	if len(updatedFields) == 0 {
		return nil
	}

	originState, _ := json.Marshal(originFields)
	updateState, _ := json.Marshal(updatedFields)

	return &models.ShipmentLogTab{
		ShipmentID:  uint64(newShipment.ID),
		LogType:     uint8(shipmentConst.UpdateShipment),
		OriginState: null.StringFrom(string(originState)),
		UpdateState: null.StringFrom(string(updateState)),
		CreatedBy:   null.StringFrom(createdBy),
	}
}

func buildShipmentLogForMAWBDocument(shipmentDocument *models.ShipmentDocument, action uint8, createdBy string) *models.ShipmentLogTab {
	if shipmentDocument == nil {
		return nil
	}

	var originStateJson, updateStateJson []byte

	shipmentLog := &models.ShipmentLogTab{
		ShipmentID: uint64(shipmentDocument.ShipmentID),
		LogType:    uint8(shipmentConst.MAWBDocument),
		CreatedBy:  null.StringFrom(createdBy),
	}

	if action == uint8(shipmentConst.MAWBDocumentLogActionAdd) {
		updateState := map[string]interface{}{
			"file_url": shipmentDocument.AssertProxyURL,
			"action":   action,
		}
		updateStateJson, _ = json.Marshal(updateState)

		shipmentLog.UpdateState = null.StringFrom(string(updateStateJson))
	} else {
		originState := map[string]interface{}{
			"file_url": shipmentDocument.AssertProxyURL,
			"action":   action,
		}
		originStateJson, _ = json.Marshal(originState)

		shipmentLog.OriginState = null.StringFrom(string(originStateJson))
	}

	return shipmentLog
}
