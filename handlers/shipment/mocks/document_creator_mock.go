// Code generated by MockGen. DO NOT EDIT.
// Source: ./handlers/shipment/shipment_interface/document_creator.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	shipment "git.ninjavan.co/3pl/httpmodels/shipment"
	models "git.ninjavan.co/3pl/models"
	auth "git.ninjavan.co/3pl/services/auth"
	gomock "github.com/golang/mock/gomock"
)

// MockDocumentCreateHandler is a mock of DocumentCreateHandler interface.
type MockDocumentCreateHandler struct {
	ctrl     *gomock.Controller
	recorder *MockDocumentCreateHandlerMockRecorder
}

// MockDocumentCreateHandlerMockRecorder is the mock recorder for MockDocumentCreateHandler.
type MockDocumentCreateHandlerMockRecorder struct {
	mock *MockDocumentCreateHandler
}

// NewMockDocumentCreateHandler creates a new mock instance.
func NewMockDocumentCreateHandler(ctrl *gomock.Controller) *MockDocumentCreateHandler {
	mock := &MockDocumentCreateHandler{ctrl: ctrl}
	mock.recorder = &MockDocumentCreateHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDocumentCreateHandler) EXPECT() *MockDocumentCreateHandlerMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockDocumentCreateHandler) Create(ctx context.Context, req shipment.DocumentCreateRequest, userInfo auth.UserInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, req, userInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockDocumentCreateHandlerMockRecorder) Create(ctx, req, userInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockDocumentCreateHandler)(nil).Create), ctx, req, userInfo)
}

// MockDocumentCreateProcessor is a mock of DocumentCreateProcessor interface.
type MockDocumentCreateProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockDocumentCreateProcessorMockRecorder
}

// MockDocumentCreateProcessorMockRecorder is the mock recorder for MockDocumentCreateProcessor.
type MockDocumentCreateProcessorMockRecorder struct {
	mock *MockDocumentCreateProcessor
}

// NewMockDocumentCreateProcessor creates a new mock instance.
func NewMockDocumentCreateProcessor(ctrl *gomock.Controller) *MockDocumentCreateProcessor {
	mock := &MockDocumentCreateProcessor{ctrl: ctrl}
	mock.recorder = &MockDocumentCreateProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDocumentCreateProcessor) EXPECT() *MockDocumentCreateProcessorMockRecorder {
	return m.recorder
}

// CreateAssetProxy mocks base method.
func (m *MockDocumentCreateProcessor) CreateAssetProxy(ctx context.Context, bucket, filepath string) (*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAssetProxy", ctx, bucket, filepath)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAssetProxy indicates an expected call of CreateAssetProxy.
func (mr *MockDocumentCreateProcessorMockRecorder) CreateAssetProxy(ctx, bucket, filepath interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAssetProxy", reflect.TypeOf((*MockDocumentCreateProcessor)(nil).CreateAssetProxy), ctx, bucket, filepath)
}

// GetFileType mocks base method.
func (m *MockDocumentCreateProcessor) GetFileType(ctx context.Context, fileURI string) (*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFileType", ctx, fileURI)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFileType indicates an expected call of GetFileType.
func (mr *MockDocumentCreateProcessorMockRecorder) GetFileType(ctx, fileURI interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFileType", reflect.TypeOf((*MockDocumentCreateProcessor)(nil).GetFileType), ctx, fileURI)
}

// GetShipment mocks base method.
func (m *MockDocumentCreateProcessor) GetShipment(ctx context.Context, req shipment.DocumentCreateRequest) (*models.Shipment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetShipment", ctx, req)
	ret0, _ := ret[0].(*models.Shipment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetShipment indicates an expected call of GetShipment.
func (mr *MockDocumentCreateProcessorMockRecorder) GetShipment(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetShipment", reflect.TypeOf((*MockDocumentCreateProcessor)(nil).GetShipment), ctx, req)
}

// Update mocks base method.
func (m *MockDocumentCreateProcessor) Update(ctx context.Context, document *models.Document, shipmentDocument *models.ShipmentDocument, shipment *models.Shipment, oldShipment models.Shipment, userInfo auth.UserInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, document, shipmentDocument, shipment, oldShipment, userInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockDocumentCreateProcessorMockRecorder) Update(ctx, document, shipmentDocument, shipment, oldShipment, userInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockDocumentCreateProcessor)(nil).Update), ctx, document, shipmentDocument, shipment, oldShipment, userInfo)
}

// ValidateTransportService mocks base method.
func (m *MockDocumentCreateProcessor) ValidateTransportService(ctx context.Context, shipment *models.Shipment) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateTransportService", ctx, shipment)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateTransportService indicates an expected call of ValidateTransportService.
func (mr *MockDocumentCreateProcessorMockRecorder) ValidateTransportService(ctx, shipment interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateTransportService", reflect.TypeOf((*MockDocumentCreateProcessor)(nil).ValidateTransportService), ctx, shipment)
}
