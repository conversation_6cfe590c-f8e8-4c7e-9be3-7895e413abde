// Code generated by MockGen. DO NOT EDIT.
// Source: ./handlers/shipment/shipment_interface/document_deleter.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	auth "git.ninjavan.co/3pl/services/auth"
	gomock "github.com/golang/mock/gomock"
)

// MockDocumentDeleteHandler is a mock of DocumentDeleteHandler interface.
type MockDocumentDeleteHandler struct {
	ctrl     *gomock.Controller
	recorder *MockDocumentDeleteHandlerMockRecorder
}

// MockDocumentDeleteHandlerMockRecorder is the mock recorder for MockDocumentDeleteHandler.
type MockDocumentDeleteHandlerMockRecorder struct {
	mock *MockDocumentDeleteHandler
}

// NewMockDocumentDeleteHandler creates a new mock instance.
func NewMockDocumentDeleteHandler(ctrl *gomock.Controller) *MockDocumentDeleteHandler {
	mock := &MockDocumentDeleteHandler{ctrl: ctrl}
	mock.recorder = &MockDocumentDeleteHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDocumentDeleteHandler) EXPECT() *MockDocumentDeleteHandlerMockRecorder {
	return m.recorder
}

// Delete mocks base method.
func (m *MockDocumentDeleteHandler) Delete(ctx context.Context, shipmentID uint, userInfo auth.UserInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, shipmentID, userInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockDocumentDeleteHandlerMockRecorder) Delete(ctx, shipmentID, userInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockDocumentDeleteHandler)(nil).Delete), ctx, shipmentID, userInfo)
}
