package shipment

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"

	shipmentConst "git.ninjavan.co/3pl/configs/shipment"
	"git.ninjavan.co/3pl/errors/fplerror"
	"git.ninjavan.co/3pl/handlers/async_request"
	"git.ninjavan.co/3pl/handlers/shipment/wrapper"
	shipmentRequest "git.ninjavan.co/3pl/httpmodels/shipment"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/services/auth"
	"git.ninjavan.co/3pl/utils"
	"git.ninjavan.co/3pl/utils/loggerutils"
)

type (
	updater struct {
		shipmentRepo          repo_interface.ShipmentRepository
		shipmentLogRepo       repo_interface.ShipmentLogRepository
		transportRepo         repo_interface.TransportRepository
		transportEventRepo    repo_interface.TransportEventRepository
		shipmentTransportRepo repo_interface.ShipmentTransportRepository
		transactionRepo       repo_interface.TransactionRepoInterface
		validator             *validator
		asyncRequestCreator   async_request.AsyncRequestCreator2
	}
)

const updatorModuleName = "shipment-updator"

func NewUpdater() *updater {
	return &updater{
		shipmentRepo:          repositories.NewShipmentRepository(&repositories.UpdateRepo{}),
		shipmentLogRepo:       repositories.NewShipmentLogRepository(),
		transportRepo:         repositories.NewTransportRepository(),
		transportEventRepo:    repositories.NewTransportEventRepository(),
		shipmentTransportRepo: repositories.NewShipmentTransportRepository(),
		transactionRepo:       repositories.NewTransactionRepo(),
		validator:             newValidator(),
		asyncRequestCreator:   async_request.NewOnUpdatedShipmentCreator(),
	}
}

func (u *updater) Update(ctx context.Context, id uint, request shipmentRequest.UpdateRequest, userInfo auth.UserInfo) error {
	shipment, err := u.shipmentRepo.FindOneWithCtx(ctx, id)
	if err != nil {
		return fplerror.ErrEntityNotFound.NewWithoutStack("common.resource_not_found", "Shipment")
	}

	oldShipment := *shipment

	currentStatus := shipment.Status
	currentRef := shipment.ReferenceID.String
	currentEvtId := shipment.CurrentEventID.Uint

	shipment = u.transformUpdateRequestToModel(ctx, shipment, request)

	if isTryingToConfirmShipment(request.Status) {
		if ok, err := u.validator.isAbleToConfirmShipment(ctx, shipment); !ok {
			return fplerror.ErrBadRequest.NewWithoutStack(errors.Wrap(err, "Couldn't closing shipment").Error())
		}
	}

	if ok, err := hasValidOptionalFields(ctx, u.validator, shipment); !ok {
		return err
	}

	transportsReq, err := validateTransports(request, shipment)
	if err != nil {
		return err
	}

	if shipment.OriginPort == shipment.DestinationPort {
		return fplerror.ErrBadRequest.NewWithoutStack("shipment.origin_port_same_dest")
	}

	if shipment.OriginCountry == shipment.DestinationCountry {
		return fplerror.ErrBadRequest.NewWithoutStack("shipment.origin_country_same_dest")
	}

	if err = u.updateData(ctx, transportsReq, oldShipment, shipment, userInfo); err != nil {
		return err
	}

	var isBagSummaryComputation bool
	if request.Status != nil && *request.Status != currentStatus {
		isBagSummaryComputation = true
	}

	// TODO: Depend on shipment creation message gets publish in real-time, the shipment update should be follow in the same way
	// to avoid shipment update message being published before shipment creation message
	_ = u.asyncRequestCreator.Create(ctx, &wrapper.ShipmentWrapper{
		Shipment:                     shipment,
		IsBagSummaryComputation:      isBagSummaryComputation,
		PreviousStatus:               currentStatus,
		PreviousRefID:                currentRef,
		PreviousEventID:              currentEvtId,
		ShouldSentMMCCUpdateShipment: true,
	})

	return nil
}

func isTryingToConfirmShipment(status *uint16) bool {
	input := null.Uint16FromPtr(status)
	return input.Valid && input.Uint16 == uint16(shipmentConst.StatusConfirmed)
}

func (u *updater) transformUpdateRequestToModel(ctx context.Context, shipment *models.Shipment, req shipmentRequest.UpdateRequest) *models.Shipment {
	// The below fields always be updated
	shipment.VendorID = null.UintFromPtr(req.VendorID)
	shipment.ConsigneeID = null.UintFromPtr(req.ConsigneeID)
	shipment.Type = null.Uint8FromPtr(req.Type)
	shipment.ReferenceID = null.StringFromPtr(req.ReferenceID)
	shipment.ShipperID = null.UintFromPtr(req.ShipperID)

	// The below fields will be updated when not empty
	if req.OriginPort != "" {
		shipment.OriginPort = req.OriginPort
	}

	if req.DestinationPort != "" {
		shipment.DestinationPort = req.DestinationPort
	}

	if req.OriginCountry != "" {
		shipment.OriginCountry = req.OriginCountry
	}

	if req.DestinationCountry != "" {
		shipment.DestinationCountry = req.DestinationCountry
	}

	// The below fields will be updated when valid
	if req.ETA.Valid {
		shipment.Eta = req.ETA
	}

	if req.ETD.Valid {
		shipment.Etd = req.ETD
	}

	if req.ClosedAt.Valid {
		shipment.ClosedAt = req.ClosedAt
	}

	if input := null.Uint16FromPtr(req.Status); input.Valid {
		shipment.Status = input.Uint16
	}

	populateShipmentMetadata(ctx, shipment, req)

	if req.ReferenceID != nil {
		shipment.ReferenceID = null.StringFrom(strings.TrimSpace(*req.ReferenceID))
	}

	if req.ActualVendorInboundDate.Valid {
		shipment.ActualVendorInboundDate = req.ActualVendorInboundDate
	}

	if input := null.UintFromPtr(req.NoOfParcelsExpected); input.Valid {
		shipment.NoOfParcelsExpected = input
	}

	if input := null.UintFromPtr(req.NoOfParcelsReceived); input.Valid {
		shipment.NoOfParcelsReceived = input
	}

	if input := null.StringFromPtr(req.GoodsCategory); input.Valid {
		shipment.GoodsCategory = input
	}

	if input := null.Float64FromPtr(req.GrossWeight); input.Valid {
		shipment.GrossWeight = input
	}

	if input := null.Float64FromPtr(req.ChargeableWeight); input.Valid {
		shipment.ChargeableWeight = input
	}

	if input := null.Float64FromPtr(req.WHGrossWeight); input.Valid {
		shipment.WHGrossWeight = input
	}

	if input := null.Float64FromPtr(req.WHChargeableWeight); input.Valid {
		shipment.WHChargeableWeight = input
	}

	if input := null.BoolFromPtr(req.ReadyToAssign); input.Valid {
		shipment.ReadyToAssign = input.Bool
	}

	return shipment
}

func populateShipmentMetadata(ctx context.Context, shipment *models.Shipment, req shipmentRequest.UpdateRequest) {
	var etdTz, etaTz *string
	if input := null.StringFromPtr(req.Metadata); input.Valid {
		loggerutils.Ctx(ctx, updatorModuleName).Info().Str("metadata", input.String).Msg("updating-shipment-with-meta")

		shipmentMeta := repositories.Metadata{}
		err := json.Unmarshal([]byte(shipment.Metadata), &shipmentMeta)

		// Override current shipment metadata with payload
		shipment.Metadata = input.String
		if err == nil {
			etdTz, etaTz = shipmentMeta.EtdTz, shipmentMeta.EtaTz
			repositories.UpdateMetadataInline(shipmentMeta.TiktokPreAlertReturned, shipment)
		}
	}

	if req.EtdTz != nil {
		etdTz = req.EtdTz
	}

	if req.EtaTz != nil {
		etaTz = req.EtaTz
	}

	repositories.PopulateEtdEtaTimezonesToShipmentMetadata(shipment, etdTz, etaTz)
}

func (u *updater) updateData(ctx context.Context, transportsReq []*shipmentRequest.TransportItem, oldShipment models.Shipment, shipment *models.Shipment, userInfo auth.UserInfo) error {
	var (
		transports models.TransportSlice
		err        error
	)

	if transports, err = u.transportRepo.ListByShipmentID(ctx, shipment.ID, nil); err != nil {
		loggerutils.Ctx(ctx, updatorModuleName).Err(err).Msg("fail-to-list-transport-by-shipment-id")
		return fplerror.ErrInternal.WithMsgInternalDB()
	}

	txn, err := u.transactionRepo.BeginTransaction(ctx)
	if err != nil {
		return err
	}

	defer func() {
		u.transactionRepo.ProcessTransaction(txn, err)
	}()

	if len(transportsReq) > 0 {
		if err = u.deleteAllTransportIfChangeUpliftStatus(ctx, txn, transportsReq, transports); err != nil {
			return err
		}

		if err = u.upsertTransport(ctx, txn, transportsReq, shipment); err != nil {
			return err
		}
	} else {
		repopulateTransportToShipment(shipment, len(transports))
	}

	if err = u.updateShipment(ctx, txn, oldShipment, shipment, transportsReq, transports, userInfo); err != nil {
		return err
	}

	return nil
}

func (u *updater) deleteAllTransportIfChangeUpliftStatus(ctx context.Context, txn boil.ContextTransactor, transportsReq []*shipmentRequest.TransportItem, transports models.TransportSlice) error {
	if !isChangeUpliftStatus(transportsReq, transports) {
		return nil
	}

	var err error

	firstTransport := transports[0]

	transportIDsToDelete := lo.FilterMap(transports, func(transport *models.Transport, index int) (uint, bool) {
		if transport.ID != firstTransport.ID {
			return transport.ID, true
		}

		return 0, false
	})

	if err = u.shipmentTransportRepo.DeleteByTransportIDs(ctx, transportIDsToDelete, txn); err != nil {
		loggerutils.Ctx(ctx, updatorModuleName).Err(err).Msg("fail-to-delete-shipment-transports-by-transport-ids")
		return fplerror.ErrInternal.WithMsgInternalDB()
	}

	if err = u.transportEventRepo.DeleteByTransportIDs(ctx, transportIDsToDelete, txn); err != nil {
		loggerutils.Ctx(ctx, updatorModuleName).Err(err).Msg("fail-to-delete-transport-event-by-transport-id")
		return fplerror.ErrInternal.WithMsgInternalDB()
	}

	if err = u.transportRepo.DeleteByIDs(ctx, transportIDsToDelete, txn); err != nil {
		loggerutils.Ctx(ctx, updatorModuleName).Err(err).Msg("fail-to-delete-transport-by-ids")
		return fplerror.ErrInternal.WithMsgInternalDB()
	}

	return nil
}

func (u *updater) upsertTransport(ctx context.Context, txn boil.ContextTransactor, transportsReq []*shipmentRequest.TransportItem, shipment *models.Shipment) error {
	if len(transportsReq) == 0 {
		return nil
	}

	var (
		noOfBagsUpliftedTotal *uint
		err                   error
	)

	newTransports := make(models.TransportSlice, 0)
	updateTransports := make(models.TransportSlice, 0)

	for _, transportReq := range transportsReq {
		newTransports, updateTransports, noOfBagsUpliftedTotal = populateUpsertTransportData(transportReq, newTransports, updateTransports, noOfBagsUpliftedTotal)
	}

	for _, updateTransport := range updateTransports {
		if err = u.transportRepo.UpdateTxn(ctx, updateTransport, txn); err != nil {
			loggerutils.Ctx(ctx, updatorModuleName).Err(err).Msg("fail-to-update-transport")
			return fplerror.ErrInternal.WithMsgInternalDB()
		}
	}

	for _, newTransport := range newTransports {
		if err = u.transportRepo.CreateWithTxn(ctx, newTransport, txn); err != nil {
			loggerutils.Ctx(ctx, updatorModuleName).Err(err).Msg("fail-to-create-transport")
			return fplerror.ErrInternal.WithMsgInternalDB()
		}

		shipmentTransport := &models.ShipmentTransport{
			ShipmentID:  shipment.ID,
			TransportID: newTransport.ID,
		}
		if err = u.shipmentTransportRepo.CreateWithTxn(ctx, shipmentTransport, txn); err != nil {
			loggerutils.Ctx(ctx, updatorModuleName).Err(err).Msg("fail-to-create-shipment-transport")
			return fplerror.ErrInternal.WithMsgInternalDB()
		}
	}

	populateTransportToShipment(transportsReq[0].UpliftStatus, noOfBagsUpliftedTotal, shipment)

	return nil
}

func populateUpsertTransportData(transportReq *shipmentRequest.TransportItem, newTransports models.TransportSlice, updateTransports models.TransportSlice, noOfBagsUpliftedTotal *uint) (models.TransportSlice, models.TransportSlice, *uint) {
	transport := &models.Transport{
		UpliftStatus:           null.Uint8FromPtr(transportReq.UpliftStatus),
		TransportIndex:         transportReq.TransportIndex,
		NoOfBagsUplifted:       null.UintFromPtr(transportReq.NoOfBagsUplifted),
		EstGrossWeightUplifted: null.Float64FromPtr(transportReq.EstGrossWeightUplifted),
		TransportNo:            null.StringFromPtr(transportReq.TransportNo),
		EtdTZ:                  null.StringFromPtr(transportReq.EtdTz),
		EtaTZ:                  null.StringFromPtr(transportReq.EtaTz),
	}

	if transportReq.ETD.Valid {
		transport.Etd = null.TimeFrom(transportReq.ETD.Time.UTC())
	}
	if transportReq.ETA.Valid {
		transport.Eta = null.TimeFrom(transportReq.ETA.Time.UTC())
	}

	if transportReq.ID != nil {
		transport.ID = *transportReq.ID
		updateTransports = append(updateTransports, transport)
	} else {
		newTransports = append(newTransports, transport)
	}

	noOfBagsUpliftedTotal = calculateNoOfBagsUpliftedTotal(transport.NoOfBagsUplifted, noOfBagsUpliftedTotal)

	return newTransports, updateTransports, noOfBagsUpliftedTotal
}

func (u *updater) updateShipment(ctx context.Context, txn boil.ContextTransactor, oldShipment models.Shipment, shipment *models.Shipment, transportsReq []*shipmentRequest.TransportItem, transports models.TransportSlice, userInfo auth.UserInfo) error {
	var (
		shipmentLog  *models.ShipmentLogTab
		shipmentLogs models.ShipmentLogTabSlice
		err          error
	)

	err = u.shipmentRepo.Update(ctx, shipment, txn)
	if utils.DBError(err).IsDuplicatedEntry() {
		return shipmentDuplicatedError
	}

	if err != nil {
		loggerutils.Ctx(ctx, updatorModuleName).Err(err).Msg("fail-to-save-shipment")
		return fplerror.ErrInternal.NewWithoutStack("common.request.internal.db", err)
	}

	if shipmentLog = buildShipmentLogForUpdateShipment(oldShipment, shipment, userInfo.Email); shipmentLog != nil {
		shipmentLogs = append(shipmentLogs, shipmentLog)
	}

	if shipmentLog = buildShipmentLogForChangeUpliftStatus(shipment, transportsReq, transports, userInfo.Email); shipmentLog != nil {
		shipmentLogs = append(shipmentLogs, shipmentLog)
	}

	if err = u.shipmentLogRepo.BulkCreate(ctx, shipmentLogs, txn); err != nil {
		loggerutils.Ctx(ctx, updatorModuleName).Err(err).Msg("fail-to-save-shipment-log")
		return fplerror.ErrInternal.WithMsgInternalDB()
	}

	return nil
}

func buildShipmentLogForUpdateShipment(oldShipment models.Shipment, newShipment *models.Shipment, createdBy string) *models.ShipmentLogTab {
	// Combine field comparisons
	originFields, updatedFields := utils.CompareStructs(oldShipment, *newShipment, []string{
		models.ShipmentColumns.OriginPort,
		models.ShipmentColumns.DestinationPort,
		models.ShipmentColumns.Type,
		models.ShipmentColumns.ReferenceID,
		models.ShipmentColumns.GrossWeight,
		models.ShipmentColumns.ChargeableWeight,
		models.ShipmentColumns.WHGrossWeight,
		models.ShipmentColumns.WHChargeableWeight,
		models.ShipmentColumns.Etd,
	}, "boil")

	// Compare metadata changes
	var oldMeta, newMeta repositories.Metadata
	_ = json.Unmarshal([]byte(oldShipment.Metadata), &oldMeta)
	_ = json.Unmarshal([]byte(newShipment.Metadata), &newMeta)

	metadataAllowFields := []string{"VesselNo"}

	if originMetaFields, updatedMetaFields := utils.CompareStructs(oldMeta, newMeta, metadataAllowFields, ""); len(updatedMetaFields) > 0 {
		for k, v := range originMetaFields {
			originFields[fmt.Sprintf("metadata.%s", k)] = v
		}
		for k, v := range updatedMetaFields {
			updatedFields[fmt.Sprintf("metadata.%s", k)] = v
		}
	}

	if len(updatedFields) == 0 {
		return nil
	}

	originState, _ := json.Marshal(originFields)
	updateState, _ := json.Marshal(updatedFields)

	return &models.ShipmentLogTab{
		ShipmentID:  uint64(newShipment.ID),
		LogType:     uint8(shipmentConst.UpdateShipment),
		OriginState: null.StringFrom(string(originState)),
		UpdateState: null.StringFrom(string(updateState)),
		CreatedBy:   null.StringFrom(createdBy),
	}
}

func buildShipmentLogForChangeUpliftStatus(shipment *models.Shipment, transportsReq []*shipmentRequest.TransportItem, transports models.TransportSlice, createdBy string) *models.ShipmentLogTab {
	if !isChangeUpliftStatus(transportsReq, transports) {
		return nil
	}

	originFields := map[string]interface{}{
		"uplift_status": transports[0].UpliftStatus.Uint8,
	}

	updatedFields := map[string]interface{}{
		"uplift_status": transportsReq[0].UpliftStatus,
		"transports":    transportsReq,
	}

	originState, _ := json.Marshal(originFields)
	updateState, _ := json.Marshal(updatedFields)

	return &models.ShipmentLogTab{
		ShipmentID:  uint64(shipment.ID),
		LogType:     uint8(shipmentConst.ChangeUpliftStatus),
		OriginState: null.StringFrom(string(originState)),
		UpdateState: null.StringFrom(string(updateState)),
		CreatedBy:   null.StringFrom(createdBy),
	}
}

func populateTransportToShipment(upliftStatus *uint8, noOfBagsUpliftedTotal *uint, shipment *models.Shipment) {
	if shipment.NoOfBags == 0 || upliftStatus == nil || shipment.Status == uint16(shipmentConst.StatusDraft) {
		shipment.OverallUpliftStatus = null.Uint8{}
		return
	}

	metadata := &repositories.Metadata{}
	_ = json.Unmarshal([]byte(shipment.Metadata), metadata)

	saveTransportToShipment(noOfBagsUpliftedTotal, shipment, metadata)
}

func repopulateTransportToShipment(shipment *models.Shipment, transportNumber int) {
	if shipment.NoOfBags == 0 || transportNumber == 0 || shipment.Status == uint16(shipmentConst.StatusDraft) {
		shipment.OverallUpliftStatus = null.Uint8{}
		return
	}

	metadata := &repositories.Metadata{}
	_ = json.Unmarshal([]byte(shipment.Metadata), metadata)

	saveTransportToShipment(metadata.NoOfBagsUpliftedTotal, shipment, metadata)
}

func saveTransportToShipment(noOfBagsUpliftedTotal *uint, shipment *models.Shipment, metadata *repositories.Metadata) {
	if noOfBagsUpliftedTotal != nil && *noOfBagsUpliftedTotal > shipment.NoOfBags {
		return
	}

	var overallUpliftStatus uint8

	switch {
	case noOfBagsUpliftedTotal == nil:
		overallUpliftStatus = uint8(shipmentConst.OverallUpliftStatusFullUplift)
	case *noOfBagsUpliftedTotal == 0:
		overallUpliftStatus = uint8(shipmentConst.OverallUpliftStatusFullOffload)
	case *noOfBagsUpliftedTotal == shipment.NoOfBags:
		overallUpliftStatus = uint8(shipmentConst.OverallUpliftStatusFullUplift)
	default:
		overallUpliftStatus = uint8(shipmentConst.OverallUpliftStatusPartialUplift)
	}

	shipment.OverallUpliftStatus = null.Uint8From(overallUpliftStatus)

	metadata.NoOfBagsUpliftedTotal = noOfBagsUpliftedTotal
	metadataStr, _ := json.Marshal(metadata)
	shipment.Metadata = string(metadataStr)
}

func calculateNoOfBagsUpliftedTotal(noOfBagsUplifted null.Uint, noOfBagsUpliftedTotal *uint) *uint {
	if !noOfBagsUplifted.Valid {
		return noOfBagsUpliftedTotal
	}

	if noOfBagsUpliftedTotal == nil {
		return noOfBagsUplifted.Ptr()
	}

	return null.UintFrom(noOfBagsUplifted.Uint + *noOfBagsUpliftedTotal).Ptr()
}

func isChangeUpliftStatus(transportsReq []*shipmentRequest.TransportItem, transports models.TransportSlice) bool {
	if len(transports) == 0 || len(transportsReq) == 0 {
		return false
	}

	firstTransport := transports[0]

	if firstTransport.UpliftStatus.Valid && transportsReq[0].UpliftStatus != nil && firstTransport.UpliftStatus.Uint8 != *transportsReq[0].UpliftStatus {
		return true
	}

	return false
}
