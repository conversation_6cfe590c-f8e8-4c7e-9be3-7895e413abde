package update_interface

import (
	"context"

	"git.ninjavan.co/3pl/httpmodels/shipment"
	shipmentRequest "git.ninjavan.co/3pl/httpmodels/shipment"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/services/auth"
)

type DocumentCreateHandler interface {
	Create(ctx context.Context, req shipment.DocumentCreateRequest, userInfo auth.UserInfo) error
}

type DocumentCreateProcessor interface {
	GetShipment(ctx context.Context, req shipmentRequest.DocumentCreateRequest) (*models.Shipment, error)
	CreateAssetProxy(ctx context.Context, bucket string, filepath string) (*string, error)
	Update(ctx context.Context, document *models.Document, shipmentDocument *models.ShipmentDocument, shipment *models.Shipment, oldShipment models.Shipment, userInfo auth.UserInfo) error
	GetFileType(ctx context.Context, fileURI string) (*string, error)
	ValidateTransportService(ctx context.Context, shipment *models.Shipment) (bool, error)
}
