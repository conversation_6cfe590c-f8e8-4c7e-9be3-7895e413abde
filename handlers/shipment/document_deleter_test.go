package shipment

import (
	"context"
	"database/sql"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/volatiletech/null/v8"

	shipmentConst "git.ninjavan.co/3pl/configs/shipment"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	mocks1 "git.ninjavan.co/3pl/repositories/mocks"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/services/auth"
)

func TestNewDocumentDeleteHandler(t *testing.T) {
	t.Parallel()
	assert.Equal(t, &documentDeleteHandler{
		shipmentRepo:         repositories.NewShipmentRepository(nil),
		shipmentDocumentRepo: repositories.NewShipmentDocumentsRepository(),
		documentRepo:         repositories.NewDocuments(),
		shipmentLogRepo:      repositories.NewShipmentLogRepository(),
		transactionRepo:      repositories.NewTransactionRepo(),
	}, NewDocumentDeleteHandler())
}

func Test_documentDeleteHandler_Delete(t *testing.T) {
	t.Parallel()
	type fields struct {
		shipmentRepo         repo_interface.ShipmentRepository
		shipmentDocumentRepo repo_interface.ShipmentDocumentsRepository
		documentRepo         repo_interface.DocumentsRepository
		transactionRepo      repo_interface.TransactionRepoInterface
		shipmentLogRepo      repo_interface.ShipmentLogRepository
	}
	type args struct {
		shipmentID uint
		userInfo   auth.UserInfo
	}

	ctrl := gomock.NewController(t)

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "fail due to get shipment error",
			fields: fields{
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks1.NewMockShipmentRepository(ctrl)
					r.EXPECT().FindOneWithCtx(gomock.Any(), gomock.Any()).Return(nil, errors.New("get error"))

					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "fail due to get shipment not found",
			fields: fields{
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks1.NewMockShipmentRepository(ctrl)
					r.EXPECT().FindOneWithCtx(gomock.Any(), gomock.Any()).Return(nil, sql.ErrNoRows)

					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "fail due to get shipment document error",
			fields: fields{
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks1.NewMockShipmentRepository(ctrl)
					r.EXPECT().FindOneWithCtx(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)

					return r
				}(),
				shipmentDocumentRepo: func() repo_interface.ShipmentDocumentsRepository {
					r := mocks1.NewMockShipmentDocumentsRepository(ctrl)
					r.EXPECT().GetByShipmentID(gomock.Any(), gomock.Any()).Return(nil, errors.New("get error"))

					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "fail due to get document error",
			fields: fields{
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks1.NewMockShipmentRepository(ctrl)
					r.EXPECT().FindOneWithCtx(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)

					return r
				}(),
				shipmentDocumentRepo: func() repo_interface.ShipmentDocumentsRepository {
					r := mocks1.NewMockShipmentDocumentsRepository(ctrl)
					r.EXPECT().GetByShipmentID(gomock.Any(), gomock.Any()).Return(&models.ShipmentDocument{}, nil)

					return r
				}(),
				documentRepo: func() repo_interface.DocumentsRepository {
					r := mocks1.NewMockDocumentsRepository(ctrl)
					r.EXPECT().GetByID(gomock.Any(), gomock.Any()).Return(nil, errors.New("get error"))

					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "can't init a transaction",
			fields: fields{
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks1.NewMockShipmentRepository(ctrl)
					r.EXPECT().FindOneWithCtx(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)

					return r
				}(),
				shipmentDocumentRepo: func() repo_interface.ShipmentDocumentsRepository {
					r := mocks1.NewMockShipmentDocumentsRepository(ctrl)
					r.EXPECT().GetByShipmentID(gomock.Any(), gomock.Any()).Return(&models.ShipmentDocument{
						AssertProxyURL: "url",
					}, nil)

					return r
				}(),
				documentRepo: func() repo_interface.DocumentsRepository {
					r := mocks1.NewMockDocumentsRepository(ctrl)
					r.EXPECT().GetByID(gomock.Any(), gomock.Any()).Return(&models.Document{
						FileName: "mawb.pdf",
					}, nil)

					return r
				}(),
				transactionRepo: func() repo_interface.TransactionRepoInterface {
					r := mocks1.NewMockTransactionRepoInterface(ctrl)
					r.EXPECT().BeginTransaction(context.TODO()).Return(nil, sql.ErrConnDone)
					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "failed to delete shipment document due to error occurred",
			fields: fields{
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks1.NewMockShipmentRepository(ctrl)
					r.EXPECT().FindOneWithCtx(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)

					return r
				}(),
				shipmentDocumentRepo: func() repo_interface.ShipmentDocumentsRepository {
					r := mocks1.NewMockShipmentDocumentsRepository(ctrl)
					r.EXPECT().GetByShipmentID(gomock.Any(), gomock.Any()).Return(&models.ShipmentDocument{
						AssertProxyURL: "url",
					}, nil)
					r.EXPECT().DeleteWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("delete error"))

					return r
				}(),
				documentRepo: func() repo_interface.DocumentsRepository {
					r := mocks1.NewMockDocumentsRepository(ctrl)
					r.EXPECT().GetByID(gomock.Any(), gomock.Any()).Return(&models.Document{
						FileName: "mawb.pdf",
					}, nil)

					return r
				}(),
				transactionRepo: func() repo_interface.TransactionRepoInterface {
					r := mocks1.NewMockTransactionRepoInterface(ctrl)
					r.EXPECT().BeginTransaction(context.TODO()).Return(mocks1.NewMockContextTransactor(ctrl), nil)
					r.EXPECT().ProcessTransaction(gomock.Any(), errors.New("delete error"))

					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "failed to create shipment log due to error occurred",
			fields: fields{
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks1.NewMockShipmentRepository(ctrl)
					r.EXPECT().FindOneWithCtx(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)

					return r
				}(),
				shipmentDocumentRepo: func() repo_interface.ShipmentDocumentsRepository {
					r := mocks1.NewMockShipmentDocumentsRepository(ctrl)
					r.EXPECT().GetByShipmentID(gomock.Any(), gomock.Any()).Return(&models.ShipmentDocument{
						AssertProxyURL: "url",
					}, nil)
					r.EXPECT().DeleteWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return r
				}(),
				shipmentLogRepo: func() repo_interface.ShipmentLogRepository {
					r := mocks1.NewMockShipmentLogRepository(ctrl)
					r.EXPECT().CreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("create shipment log error"))

					return r
				}(),
				documentRepo: func() repo_interface.DocumentsRepository {
					r := mocks1.NewMockDocumentsRepository(ctrl)
					r.EXPECT().GetByID(gomock.Any(), gomock.Any()).Return(&models.Document{
						FileName: "mawb.pdf",
					}, nil)

					return r
				}(),
				transactionRepo: func() repo_interface.TransactionRepoInterface {
					r := mocks1.NewMockTransactionRepoInterface(ctrl)
					r.EXPECT().BeginTransaction(context.TODO()).Return(mocks1.NewMockContextTransactor(ctrl), nil)
					r.EXPECT().ProcessTransaction(gomock.Any(), errors.New("create shipment log error"))

					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "failed to delete document due to error occurred",
			fields: fields{
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks1.NewMockShipmentRepository(ctrl)
					r.EXPECT().FindOneWithCtx(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)

					return r
				}(),
				shipmentDocumentRepo: func() repo_interface.ShipmentDocumentsRepository {
					r := mocks1.NewMockShipmentDocumentsRepository(ctrl)
					r.EXPECT().GetByShipmentID(gomock.Any(), gomock.Any()).Return(&models.ShipmentDocument{
						AssertProxyURL: "url",
					}, nil)
					r.EXPECT().DeleteWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return r
				}(),
				shipmentLogRepo: func() repo_interface.ShipmentLogRepository {
					r := mocks1.NewMockShipmentLogRepository(ctrl)
					r.EXPECT().CreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return r
				}(),
				documentRepo: func() repo_interface.DocumentsRepository {
					r := mocks1.NewMockDocumentsRepository(ctrl)
					r.EXPECT().GetByID(gomock.Any(), gomock.Any()).Return(&models.Document{
						FileName: "mawb.pdf",
					}, nil)
					r.EXPECT().DeleteWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("delete error"))

					return r
				}(),
				transactionRepo: func() repo_interface.TransactionRepoInterface {
					r := mocks1.NewMockTransactionRepoInterface(ctrl)
					r.EXPECT().BeginTransaction(context.TODO()).Return(mocks1.NewMockContextTransactor(ctrl), nil)
					r.EXPECT().ProcessTransaction(gomock.Any(), errors.New("delete error"))

					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "delete mawb document successfully",
			fields: fields{
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks1.NewMockShipmentRepository(ctrl)
					r.EXPECT().FindOneWithCtx(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)

					return r
				}(),
				shipmentDocumentRepo: func() repo_interface.ShipmentDocumentsRepository {
					r := mocks1.NewMockShipmentDocumentsRepository(ctrl)
					r.EXPECT().GetByShipmentID(gomock.Any(), gomock.Any()).Return(&models.ShipmentDocument{
						ShipmentID:     1,
						AssertProxyURL: "url",
					}, nil)
					r.EXPECT().DeleteWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return r
				}(),
				shipmentLogRepo: func() repo_interface.ShipmentLogRepository {
					r := mocks1.NewMockShipmentLogRepository(ctrl)
					r.EXPECT().CreateWithTxn(gomock.Any(), &models.ShipmentLogTab{
						ShipmentID:  1,
						LogType:     uint8(shipmentConst.MAWBDocument),
						CreatedBy:   null.StringFrom("<EMAIL>"),
						OriginState: null.StringFrom(`{"action":2,"file_url":"url"}`),
					}, gomock.Any()).Return(nil)

					return r
				}(),
				documentRepo: func() repo_interface.DocumentsRepository {
					r := mocks1.NewMockDocumentsRepository(ctrl)
					r.EXPECT().GetByID(gomock.Any(), gomock.Any()).Return(&models.Document{
						FileName: "mawb.pdf",
					}, nil)
					r.EXPECT().DeleteWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return r
				}(),
				transactionRepo: func() repo_interface.TransactionRepoInterface {
					r := mocks1.NewMockTransactionRepoInterface(ctrl)
					r.EXPECT().BeginTransaction(context.TODO()).Return(mocks1.NewMockContextTransactor(ctrl), nil)
					r.EXPECT().ProcessTransaction(gomock.Any(), nil)

					return r
				}(),
			},
			args: args{
				shipmentID: 1,
				userInfo:   auth.UserInfo{Email: "<EMAIL>"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &documentDeleteHandler{
				shipmentRepo:         tt.fields.shipmentRepo,
				shipmentDocumentRepo: tt.fields.shipmentDocumentRepo,
				documentRepo:         tt.fields.documentRepo,
				shipmentLogRepo:      tt.fields.shipmentLogRepo,
				transactionRepo:      tt.fields.transactionRepo,
			}

			err := s.Delete(context.TODO(), tt.args.shipmentID, tt.args.userInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("documentDeleteHandler.Delete() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
