package shipment

import (
	"context"
	"database/sql"

	shipmentConst "git.ninjavan.co/3pl/configs/shipment"
	"git.ninjavan.co/3pl/errors/fplerror"
	update_interface "git.ninjavan.co/3pl/handlers/shipment/shipment_interface"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/services/auth"
	"git.ninjavan.co/3pl/utils/loggerutils"
)

type (
	documentDeleteHandler struct {
		shipmentRepo         repo_interface.ShipmentRepository
		shipmentDocumentRepo repo_interface.ShipmentDocumentsRepository
		documentRepo         repo_interface.DocumentsRepository
		shipmentLogRepo      repo_interface.ShipmentLogRepository
		transactionRepo      repo_interface.TransactionRepoInterface
	}
)

const documentDeleteModuleName = "shipment-document-delete-handler"

func NewDocumentDeleteHandler() update_interface.DocumentDeleteHandler {
	return &documentDeleteHandler{
		shipmentRepo:         repositories.NewShipmentRepository(nil),
		shipmentDocumentRepo: repositories.NewShipmentDocumentsRepository(),
		documentRepo:         repositories.NewDocuments(),
		shipmentLogRepo:      repositories.NewShipmentLogRepository(),
		transactionRepo:      repositories.NewTransactionRepo(),
	}
}

func (s *documentDeleteHandler) Delete(ctx context.Context, shipmentID uint, userInfo auth.UserInfo) error {
	var (
		shipmentDocument *models.ShipmentDocument
		document         *models.Document
		err              error
	)

	if _, err = s.shipmentRepo.FindOneWithCtx(ctx, shipmentID); err != nil {
		if err != sql.ErrNoRows {
			loggerutils.Ctx(ctx, documentDeleteModuleName).Err(err).Uint("shipment_id", shipmentID).Msg("failed-to-get-shipment")
			return fplerror.ErrInternal.WithMsgInternalDB()
		}

		return fplerror.ErrEntityNotFound.NewWithoutStack("shipment.id.not_found", shipmentID)
	}

	if shipmentDocument, err = s.shipmentDocumentRepo.GetByShipmentID(ctx, shipmentID); err != nil && err != sql.ErrNoRows {
		loggerutils.Ctx(ctx, documentDeleteModuleName).Err(err).Uint("shipment_id", shipmentID).Msg("failed-to-get-shipment-document")
		return fplerror.ErrInternal.WithMsgInternalDB()
	}

	if shipmentDocument != nil {
		if document, err = s.documentRepo.GetByID(ctx, shipmentDocument.DocumentID); err != nil && err != sql.ErrNoRows {
			loggerutils.Ctx(ctx, documentDeleteModuleName).Err(err).Uint("shipment_document_id", shipmentDocument.DocumentID).Msg("failed-to-get-document")
			return fplerror.ErrInternal.WithMsgInternalDB()
		}
	}

	if err = s.process(ctx, shipmentDocument, document, userInfo); err != nil {
		return err
	}

	return nil
}

func (s *documentDeleteHandler) process(ctx context.Context, shipmentDocument *models.ShipmentDocument, document *models.Document, userInfo auth.UserInfo) error {
	txn, err := s.transactionRepo.BeginTransaction(ctx)
	if err != nil {
		loggerutils.Ctx(ctx, documentDeleteModuleName).Err(err).Msg("can-not-begin-transaction")
		return fplerror.ErrInternal.WithMsgInternalDB()
	}

	defer func() {
		s.transactionRepo.ProcessTransaction(txn, err)
	}()

	if shipmentDocument != nil {
		if err = s.shipmentDocumentRepo.DeleteWithTxn(ctx, shipmentDocument, txn); err != nil {
			loggerutils.Ctx(ctx, documentDeleteModuleName).Error().Uint("shipment_id", shipmentDocument.ShipmentID).Err(err).Msg("failed-to-delete-shipment-document")
			return fplerror.ErrInternal.WithMsgInternalDB()
		}

		if shipmentLog := buildShipmentLogForMAWBDocument(shipmentDocument, uint8(shipmentConst.MAWBDocumentLogActionDelete), userInfo.Email); shipmentLog != nil {
			if err = s.shipmentLogRepo.CreateWithTxn(ctx, shipmentLog, txn); err != nil {
				loggerutils.Ctx(ctx, documentDeleteModuleName).Error().Uint("shipment_id", shipmentDocument.ShipmentID).Err(err).Msg("failed-to-create-shipment-log")
				return fplerror.ErrInternal.WithMsgInternalDB()
			}
		}
	}

	if document != nil {
		if err = s.documentRepo.DeleteWithTxn(ctx, document, txn); err != nil {
			loggerutils.Ctx(ctx, documentDeleteModuleName).Error().Uint("document_id", document.ID).Err(err).Msg("failed-to-delete-document")
			return fplerror.ErrInternal.WithMsgInternalDB()
		}
	}

	return nil
}
