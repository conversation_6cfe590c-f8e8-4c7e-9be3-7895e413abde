package shipment

import (
	"context"
	"database/sql"
	"errors"
	"reflect"
	"testing"

	gcs "cloud.google.com/go/storage"
	"github.com/golang/mock/gomock"
	"github.com/volatiletech/null/v8"

	assetApi "bitbucket.ninjavan.co/apiclients/assets-proxy-api-client---go/assets"
	assetApiMocks "bitbucket.ninjavan.co/apiclients/assets-proxy-api-client---go/assets/mocks"

	shipmentConst "git.ninjavan.co/3pl/configs/shipment"
	mocks1 "git.ninjavan.co/3pl/handlers/shipment/mocks"
	update_interface "git.ninjavan.co/3pl/handlers/shipment/shipment_interface"
	shipmentRequest "git.ninjavan.co/3pl/httpmodels/shipment"
	"git.ninjavan.co/3pl/models"
	mocks2 "git.ninjavan.co/3pl/repositories/mocks"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/services/auth"
	"git.ninjavan.co/3pl/services/storage"
	mocks3 "git.ninjavan.co/3pl/services/storage/mocks"
)

func TestNewDocumentCreateHandler(t *testing.T) {
	t.Parallel()
	NewDocumentCreateHandler()
}

func Test_documentCreatorHandler_Create(t *testing.T) {
	t.Parallel()
	type fields struct {
		processor update_interface.DocumentCreateProcessor
	}
	type args struct {
		req      shipmentRequest.DocumentCreateRequest
		userInfo auth.UserInfo
	}

	ctrl := gomock.NewController(t)

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "fail due to invalid file",
			args: args{
				req: shipmentRequest.DocumentCreateRequest{
					Payload: shipmentRequest.DocumentCreatePayload{
						Name: null.StringFrom("name").Ptr(),
					},
				},
			},
			wantErr: true,
		},
		{
			name: "fail due to get shipment error",
			fields: fields{
				processor: func() update_interface.DocumentCreateProcessor {
					r := mocks1.NewMockDocumentCreateProcessor(ctrl)
					r.EXPECT().GetShipment(gomock.Any(), gomock.Any()).Return(nil, errors.New("get error"))

					return r
				}(),
			},
			args: args{
				req: shipmentRequest.DocumentCreateRequest{
					Payload: shipmentRequest.DocumentCreatePayload{
						Name: null.StringFrom("file").Ptr(),
						URI:  null.StringFrom("url").Ptr(),
					},
				},
			},
			wantErr: true,
		},
		{
			name: "fail due to generate assert proxy url error",
			fields: fields{
				processor: func() update_interface.DocumentCreateProcessor {
					r := mocks1.NewMockDocumentCreateProcessor(ctrl)
					r.EXPECT().GetShipment(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)
					r.EXPECT().CreateAssetProxy(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("get error"))

					return r
				}(),
			},
			args: args{
				req: shipmentRequest.DocumentCreateRequest{
					Payload: shipmentRequest.DocumentCreatePayload{
						Name: null.StringFrom("file").Ptr(),
						URI:  null.StringFrom("url").Ptr(),
					},
				},
			},
			wantErr: true,
		},
		{
			name: "fail due to get file type error",
			fields: fields{
				processor: func() update_interface.DocumentCreateProcessor {
					r := mocks1.NewMockDocumentCreateProcessor(ctrl)
					r.EXPECT().GetShipment(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)
					r.EXPECT().CreateAssetProxy(gomock.Any(), gomock.Any(), gomock.Any()).Return(null.StringFrom("url").Ptr(), nil)
					r.EXPECT().GetFileType(gomock.Any(), gomock.Any()).Return(nil, errors.New("get error"))

					return r
				}(),
			},
			args: args{
				req: shipmentRequest.DocumentCreateRequest{
					Payload: shipmentRequest.DocumentCreatePayload{
						Name: null.StringFrom("file").Ptr(),
						URI:  null.StringFrom("url").Ptr(),
					},
				},
			},
			wantErr: true,
		},
		{
			name: "fail due to update error",
			fields: fields{

				processor: func() update_interface.DocumentCreateProcessor {
					r := mocks1.NewMockDocumentCreateProcessor(ctrl)
					r.EXPECT().GetShipment(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)
					r.EXPECT().CreateAssetProxy(gomock.Any(), gomock.Any(), gomock.Any()).Return(null.StringFrom("url").Ptr(), nil)
					r.EXPECT().GetFileType(gomock.Any(), gomock.Any()).Return(null.StringFrom("application/pdf").Ptr(), nil)
					r.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("update error"))

					return r
				}(),
			},
			args: args{
				req: shipmentRequest.DocumentCreateRequest{
					Payload: shipmentRequest.DocumentCreatePayload{
						Name: null.StringFrom("file").Ptr(),
						URI:  null.StringFrom("url").Ptr(),
					},
				},
			},
			wantErr: true,
		},
		{
			name: "fail due to validate for tiktok service error",
			fields: fields{
				processor: func() update_interface.DocumentCreateProcessor {
					r := mocks1.NewMockDocumentCreateProcessor(ctrl)
					r.EXPECT().GetShipment(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)
					r.EXPECT().CreateAssetProxy(gomock.Any(), gomock.Any(), gomock.Any()).Return(null.StringFrom("url").Ptr(), nil)
					r.EXPECT().GetFileType(gomock.Any(), gomock.Any()).Return(null.StringFrom("application/pdf").Ptr(), nil)
					r.EXPECT().ValidateTransportService(gomock.Any(), gomock.Any()).Return(false, errors.New("validate error"))

					return r
				}(),
			},
			args: args{
				req: shipmentRequest.DocumentCreateRequest{
					Payload: shipmentRequest.DocumentCreatePayload{
						Name:             null.StringFrom("file").Ptr(),
						URI:              null.StringFrom("url").Ptr(),
						TransportService: null.Uint8From(1).Ptr(),
					},
				},
			},
			wantErr: true,
		},
		{
			name: "fail due to invalid for tiktok service",
			fields: fields{
				processor: func() update_interface.DocumentCreateProcessor {
					r := mocks1.NewMockDocumentCreateProcessor(ctrl)
					r.EXPECT().GetShipment(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)
					r.EXPECT().CreateAssetProxy(gomock.Any(), gomock.Any(), gomock.Any()).Return(null.StringFrom("url").Ptr(), nil)
					r.EXPECT().GetFileType(gomock.Any(), gomock.Any()).Return(null.StringFrom("application/pdf").Ptr(), nil)
					r.EXPECT().ValidateTransportService(gomock.Any(), gomock.Any()).Return(false, nil)

					return r
				}(),
			},
			args: args{
				req: shipmentRequest.DocumentCreateRequest{
					Payload: shipmentRequest.DocumentCreatePayload{
						Name:             null.StringFrom("file").Ptr(),
						URI:              null.StringFrom("url").Ptr(),
						TransportService: null.Uint8From(1).Ptr(),
					},
				},
			},
			wantErr: true,
		},
		{
			name: "create shipment document successfully",
			fields: fields{
				processor: func() update_interface.DocumentCreateProcessor {
					r := mocks1.NewMockDocumentCreateProcessor(ctrl)
					r.EXPECT().GetShipment(gomock.Any(), gomock.Any()).Return(&models.Shipment{
						Metadata: `{"vessel_no":null,"xb_ops_shipment_created_from":null,"xb_ops_second_shipment_id":null,"transport_service":1}`,
					}, nil)
					r.EXPECT().CreateAssetProxy(gomock.Any(), gomock.Any(), gomock.Any()).Return(null.StringFrom("url").Ptr(), nil)
					r.EXPECT().GetFileType(gomock.Any(), gomock.Any()).Return(null.StringFrom("application/pdf").Ptr(), nil)
					r.EXPECT().ValidateTransportService(gomock.Any(), gomock.Any()).Return(true, nil)
					r.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(),
						&models.Shipment{
							Metadata: `{"vessel_no":null,"xb_ops_shipment_created_from":null,"xb_ops_second_shipment_id":null,"transport_service":2}`,
						}, models.Shipment{
							Metadata: `{"vessel_no":null,"xb_ops_shipment_created_from":null,"xb_ops_second_shipment_id":null,"transport_service":1}`,
						}, auth.UserInfo{
							Email: "<EMAIL>",
						}).Return(nil)

					return r
				}(),
			},
			args: args{
				req: shipmentRequest.DocumentCreateRequest{
					Payload: shipmentRequest.DocumentCreatePayload{
						Name:             null.StringFrom("file").Ptr(),
						URI:              null.StringFrom("url").Ptr(),
						TransportService: null.Uint8From(2).Ptr(),
					},
				},
				userInfo: auth.UserInfo{
					Email: "<EMAIL>",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &documentCreatorHandler{
				processor: tt.fields.processor,
			}

			err := s.Create(context.TODO(), tt.args.req, tt.args.userInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("documentCreatorHandler.Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func Test_documentCreateProcessor_GetShipment(t *testing.T) {
	t.Parallel()
	type fields struct {
		shipmentRepo         repo_interface.ShipmentRepository
		shipmentDocumentRepo repo_interface.ShipmentDocumentsRepository
	}
	type args struct {
		req shipmentRequest.DocumentCreateRequest
	}

	ctrl := gomock.NewController(t)

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		want    *models.Shipment
	}{
		{
			name: "failed to get shipment due to error occurred",
			fields: fields{
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks2.NewMockShipmentRepository(ctrl)
					r.EXPECT().FindOneWithCtx(gomock.Any(), gomock.Any()).Return(nil, errors.New("get error"))

					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "failed to get shipment document due to error occurred",
			fields: fields{
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks2.NewMockShipmentRepository(ctrl)
					r.EXPECT().FindOneWithCtx(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)

					return r
				}(),
				shipmentDocumentRepo: func() repo_interface.ShipmentDocumentsRepository {
					r := mocks2.NewMockShipmentDocumentsRepository(ctrl)
					r.EXPECT().GetByShipmentID(gomock.Any(), gomock.Any()).Return(nil, errors.New("get error"))

					return r
				}(),
			},
			args: args{
				req: shipmentRequest.DocumentCreateRequest{
					Payload: shipmentRequest.DocumentCreatePayload{
						Name: null.StringFrom("file").Ptr(),
						URI:  null.StringFrom("url").Ptr(),
					},
				},
			},
			wantErr: true,
		},
		{
			name: "failed due to shipment document existed",
			fields: fields{
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks2.NewMockShipmentRepository(ctrl)
					r.EXPECT().FindOneWithCtx(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)

					return r
				}(),
				shipmentDocumentRepo: func() repo_interface.ShipmentDocumentsRepository {
					r := mocks2.NewMockShipmentDocumentsRepository(ctrl)
					r.EXPECT().GetByShipmentID(gomock.Any(), gomock.Any()).Return(&models.ShipmentDocument{}, nil)

					return r
				}(),
			},
			args: args{
				req: shipmentRequest.DocumentCreateRequest{
					Payload: shipmentRequest.DocumentCreatePayload{
						Name: null.StringFrom("file").Ptr(),
						URI:  null.StringFrom("url").Ptr(),
					},
				},
			},
			wantErr: true,
		},
		{
			name: "validate successfully",
			fields: fields{
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks2.NewMockShipmentRepository(ctrl)
					r.EXPECT().FindOneWithCtx(gomock.Any(), gomock.Any()).Return(&models.Shipment{}, nil)

					return r
				}(),
				shipmentDocumentRepo: func() repo_interface.ShipmentDocumentsRepository {
					r := mocks2.NewMockShipmentDocumentsRepository(ctrl)
					r.EXPECT().GetByShipmentID(gomock.Any(), gomock.Any()).Return(nil, nil)

					return r
				}(),
			},
			args: args{
				req: shipmentRequest.DocumentCreateRequest{
					Payload: shipmentRequest.DocumentCreatePayload{
						Name: null.StringFrom("file").Ptr(),
						URI:  null.StringFrom("url").Ptr(),
					},
				},
			},
			want: &models.Shipment{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &documentCreateProcessor{
				shipmentRepo:         tt.fields.shipmentRepo,
				shipmentDocumentRepo: tt.fields.shipmentDocumentRepo,
			}

			got, err := s.GetShipment(context.TODO(), tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("documentCreateProcessor.GetShipment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("documentCreateProcessor.GetShipment() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_documentCreateProcessor_CreateAssetProxy(t *testing.T) {
	t.Parallel()
	type fields struct {
		generateURLAPI assetApi.Requester
	}
	type args struct {
		bucket   string
		filePath string
	}

	ctrl := gomock.NewController(t)

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *string
		wantErr bool
	}{
		{
			name: "fail due to create asset error",
			fields: fields{
				generateURLAPI: func() assetApi.Requester {
					r := assetApiMocks.NewMockRequester(ctrl)
					r.EXPECT().CreateAsset(gomock.Any()).Return(assetApi.Response{}, errors.New("get error"))

					return r
				}(),
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &documentCreateProcessor{
				generateURLAPI: tt.fields.generateURLAPI,
			}

			got, err := s.CreateAssetProxy(context.Background(), tt.args.bucket, tt.args.filePath)
			if (err != nil) != tt.wantErr {
				t.Errorf("documentCreateProcessor.CreateAssetProxy() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("documentCreateProcessor.CreateAssetProxy() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_documentCreateProcessor_Update(t *testing.T) {
	t.Parallel()
	type fields struct {
		transactionRepo      repo_interface.TransactionRepoInterface
		shipmentDocumentRepo repo_interface.ShipmentDocumentsRepository
		documentRepo         repo_interface.DocumentsRepository
		shipmentRepo         repo_interface.ShipmentRepository
		shipmentLogRepo      repo_interface.ShipmentLogRepository
	}
	type args struct {
		document         *models.Document
		shipmentDocument *models.ShipmentDocument
		shipment         *models.Shipment
		oldShipment      models.Shipment
		userInfo         auth.UserInfo
	}

	ctrl := gomock.NewController(t)

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "can't init a transaction",
			fields: fields{
				transactionRepo: func() repo_interface.TransactionRepoInterface {
					r := mocks2.NewMockTransactionRepoInterface(ctrl)
					r.EXPECT().BeginTransaction(context.TODO()).Return(nil, sql.ErrConnDone)
					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "failed to create document due to error occurred",
			fields: fields{
				transactionRepo: func() repo_interface.TransactionRepoInterface {
					r := mocks2.NewMockTransactionRepoInterface(ctrl)
					r.EXPECT().BeginTransaction(context.TODO()).Return(mocks2.NewMockContextTransactor(ctrl), nil)
					r.EXPECT().ProcessTransaction(gomock.Any(), errors.New("create error"))

					return r
				}(),
				documentRepo: func() repo_interface.DocumentsRepository {
					r := mocks2.NewMockDocumentsRepository(ctrl)
					r.EXPECT().CreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("create error"))

					return r
				}(),
			},
			args: args{
				document:         &models.Document{},
				shipmentDocument: &models.ShipmentDocument{},
			},
			wantErr: true,
		},
		{
			name: "failed to create shipment document due to error occurred",
			fields: fields{
				transactionRepo: func() repo_interface.TransactionRepoInterface {
					r := mocks2.NewMockTransactionRepoInterface(ctrl)
					r.EXPECT().BeginTransaction(context.TODO()).Return(mocks2.NewMockContextTransactor(ctrl), nil)
					r.EXPECT().ProcessTransaction(gomock.Any(), errors.New("create error"))

					return r
				}(),
				documentRepo: func() repo_interface.DocumentsRepository {
					r := mocks2.NewMockDocumentsRepository(ctrl)
					r.EXPECT().CreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return r
				}(),
				shipmentDocumentRepo: func() repo_interface.ShipmentDocumentsRepository {
					r := mocks2.NewMockShipmentDocumentsRepository(ctrl)
					r.EXPECT().CreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("create error"))

					return r
				}(),
			},
			args: args{
				document:         &models.Document{},
				shipmentDocument: &models.ShipmentDocument{},
			},
			wantErr: true,
		},
		{
			name: "failed to update shipment due to error occurred",
			fields: fields{
				transactionRepo: func() repo_interface.TransactionRepoInterface {
					r := mocks2.NewMockTransactionRepoInterface(ctrl)
					r.EXPECT().BeginTransaction(context.TODO()).Return(mocks2.NewMockContextTransactor(ctrl), nil)
					r.EXPECT().ProcessTransaction(gomock.Any(), errors.New("update error"))

					return r
				}(),
				documentRepo: func() repo_interface.DocumentsRepository {
					r := mocks2.NewMockDocumentsRepository(ctrl)
					r.EXPECT().CreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return r
				}(),
				shipmentDocumentRepo: func() repo_interface.ShipmentDocumentsRepository {
					r := mocks2.NewMockShipmentDocumentsRepository(ctrl)
					r.EXPECT().CreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return r
				}(),
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks2.NewMockShipmentRepository(ctrl)
					r.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("update error"))

					return r
				}(),
			},
			args: args{
				document:         &models.Document{},
				shipmentDocument: &models.ShipmentDocument{},
				shipment:         &models.Shipment{},
			},
			wantErr: true,
		},
		{
			name: "failed to create shipment log due to error occurred",
			fields: fields{
				transactionRepo: func() repo_interface.TransactionRepoInterface {
					r := mocks2.NewMockTransactionRepoInterface(ctrl)
					r.EXPECT().BeginTransaction(context.TODO()).Return(mocks2.NewMockContextTransactor(ctrl), nil)
					r.EXPECT().ProcessTransaction(gomock.Any(), errors.New("create error"))

					return r
				}(),
				documentRepo: func() repo_interface.DocumentsRepository {
					r := mocks2.NewMockDocumentsRepository(ctrl)
					r.EXPECT().CreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return r
				}(),
				shipmentDocumentRepo: func() repo_interface.ShipmentDocumentsRepository {
					r := mocks2.NewMockShipmentDocumentsRepository(ctrl)
					r.EXPECT().CreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return r
				}(),
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks2.NewMockShipmentRepository(ctrl)
					r.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return r
				}(),
				shipmentLogRepo: func() repo_interface.ShipmentLogRepository {
					r := mocks2.NewMockShipmentLogRepository(ctrl)
					r.EXPECT().BulkCreate(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("create error"))

					return r
				}(),
			},
			args: args{
				document:         &models.Document{},
				shipmentDocument: &models.ShipmentDocument{},
				shipment: &models.Shipment{
					ID:       1,
					Metadata: `{"transport_service":2}`,
				},
				oldShipment: models.Shipment{
					ID:       1,
					Metadata: `{"transport_service":1}`,
				},
			},
			wantErr: true,
		},
		{
			name: "update successfully",
			fields: fields{
				transactionRepo: func() repo_interface.TransactionRepoInterface {
					r := mocks2.NewMockTransactionRepoInterface(ctrl)
					r.EXPECT().BeginTransaction(context.TODO()).Return(mocks2.NewMockContextTransactor(ctrl), nil)
					r.EXPECT().ProcessTransaction(gomock.Any(), nil)

					return r
				}(),
				documentRepo: func() repo_interface.DocumentsRepository {
					r := mocks2.NewMockDocumentsRepository(ctrl)
					r.EXPECT().CreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return r
				}(),
				shipmentDocumentRepo: func() repo_interface.ShipmentDocumentsRepository {
					r := mocks2.NewMockShipmentDocumentsRepository(ctrl)
					r.EXPECT().CreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return r
				}(),
				shipmentRepo: func() repo_interface.ShipmentRepository {
					r := mocks2.NewMockShipmentRepository(ctrl)
					r.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return r
				}(),
				shipmentLogRepo: func() repo_interface.ShipmentLogRepository {
					r := mocks2.NewMockShipmentLogRepository(ctrl)
					r.EXPECT().BulkCreate(gomock.Any(),
						models.ShipmentLogTabSlice{
							&models.ShipmentLogTab{
								ShipmentID:  1,
								LogType:     uint8(shipmentConst.UpdateShipment),
								OriginState: null.StringFrom(`{"metadata.TransportService":1}`),
								UpdateState: null.StringFrom(`{"metadata.TransportService":2}`),
								CreatedBy:   null.StringFrom("<EMAIL>"),
							},
							&models.ShipmentLogTab{
								ShipmentID:  1,
								LogType:     uint8(shipmentConst.MAWBDocument),
								UpdateState: null.StringFrom(`{"action":1,"file_url":"url"}`),
								CreatedBy:   null.StringFrom("<EMAIL>"),
							},
						}, gomock.Any()).Return(nil)

					return r
				}(),
			},
			args: args{
				document: &models.Document{},
				shipmentDocument: &models.ShipmentDocument{
					ShipmentID:     1,
					AssertProxyURL: "url",
				},
				shipment: &models.Shipment{
					ID:       1,
					Metadata: `{"transport_service":2}`,
				},
				oldShipment: models.Shipment{
					ID:       1,
					Metadata: `{"transport_service":1}`,
				},
				userInfo: auth.UserInfo{
					Email: "<EMAIL>",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &documentCreateProcessor{
				transactionRepo:      tt.fields.transactionRepo,
				documentRepo:         tt.fields.documentRepo,
				shipmentDocumentRepo: tt.fields.shipmentDocumentRepo,
				shipmentRepo:         tt.fields.shipmentRepo,
				shipmentLogRepo:      tt.fields.shipmentLogRepo,
			}

			err := s.Update(context.TODO(), tt.args.document, tt.args.shipmentDocument, tt.args.shipment, tt.args.oldShipment, tt.args.userInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("documentCreateProcessor.Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func Test_documentCreateProcessor_GetFileType(t *testing.T) {
	t.Parallel()
	type fields struct {
		cloudStorage storage.CloudStorage
	}
	type args struct {
		fileURI string
	}

	ctrl := gomock.NewController(t)

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "get file type error",
			fields: fields{
				cloudStorage: func() storage.CloudStorage {
					r := mocks3.NewMockCloudStorage(ctrl)
					r.EXPECT().GetObject(gomock.Any(), gomock.Any()).Return(nil, errors.New("get error"))
					return r
				}(),
			},
			wantErr: true,
		},
		{
			name: "get file type successfully",
			fields: fields{
				cloudStorage: func() storage.CloudStorage {
					r := mocks3.NewMockCloudStorage(ctrl)
					r.EXPECT().GetObject(gomock.Any(), gomock.Any()).Return(&gcs.Reader{}, nil)
					return r
				}(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &documentCreateProcessor{
				cloudStorage: tt.fields.cloudStorage,
			}

			_, err := s.GetFileType(context.Background(), tt.args.fileURI)
			if (err != nil) != tt.wantErr {
				t.Errorf("documentCreateProcessor.GetFileType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func Test_documentCreateProcessor_ValidateTransportService(t *testing.T) {
	t.Parallel()
	type fields struct {
		shipmentParcelRepo repo_interface.ShipmentParcelRepository
	}
	type args struct {
		shipment *models.Shipment
	}

	ctrl := gomock.NewController(t)

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		want    bool
	}{
		{
			name: "skip",
			args: args{
				shipment: &models.Shipment{
					DestinationCountry: "SG",
				},
			},
		},
		{
			name: "failed to count tiktok parcel due to error",
			fields: fields{
				shipmentParcelRepo: func() repo_interface.ShipmentParcelRepository {
					r := mocks2.NewMockShipmentParcelRepository(ctrl)
					r.EXPECT().GetNoOfItemsByPartnerIDAndServiceType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint(0), errors.New("get error"))

					return r
				}(),
			},
			args: args{
				shipment: &models.Shipment{
					DestinationCountry: "MY",
					Type:               null.Uint8From(uint8(shipmentConst.TypeAir)),
				},
			},
			wantErr: true,
		},
		{
			name: "count tiktok parcel = 0",
			fields: fields{
				shipmentParcelRepo: func() repo_interface.ShipmentParcelRepository {
					r := mocks2.NewMockShipmentParcelRepository(ctrl)
					r.EXPECT().GetNoOfItemsByPartnerIDAndServiceType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint(0), nil)

					return r
				}(),
			},
			args: args{
				shipment: &models.Shipment{
					DestinationCountry: "MY",
					Type:               null.Uint8From(uint8(shipmentConst.TypeAir)),
				},
			},
		},
		{
			name: "validate transport service",
			fields: fields{
				shipmentParcelRepo: func() repo_interface.ShipmentParcelRepository {
					r := mocks2.NewMockShipmentParcelRepository(ctrl)
					r.EXPECT().GetNoOfItemsByPartnerIDAndServiceType(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(uint(1), nil)

					return r
				}(),
			},
			args: args{
				shipment: &models.Shipment{
					DestinationCountry: "MY",
					Type:               null.Uint8From(uint8(shipmentConst.TypeAir)),
				},
			},
			want: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &documentCreateProcessor{
				shipmentParcelRepo: tt.fields.shipmentParcelRepo,
			}

			got, err := s.ValidateTransportService(context.TODO(), tt.args.shipment)
			if (err != nil) != tt.wantErr {
				t.Errorf("documentCreateProcessor.ValidateTransportService() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("documentCreateProcessor.GetShipment() got = %v, want %v", got, tt.want)
			}
		})
	}
}
