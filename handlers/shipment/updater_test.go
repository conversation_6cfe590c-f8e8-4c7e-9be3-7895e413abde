package shipment

import (
	"context"
	"database/sql"
	"reflect"
	"testing"
	"time"

	"github.com/VividCortex/mysqlerr"
	"github.com/go-sql-driver/mysql"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	shipmentConst "git.ninjavan.co/3pl/configs/shipment"
	"git.ninjavan.co/3pl/configs/transport"
	"git.ninjavan.co/3pl/errors/fplerror"
	"git.ninjavan.co/3pl/handlers/async_request"
	mocks1 "git.ninjavan.co/3pl/handlers/async_request/mocks"
	shipmentRequest "git.ninjavan.co/3pl/httpmodels/shipment"
	"git.ninjavan.co/3pl/models"
	mocks2 "git.ninjavan.co/3pl/repositories/mocks"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/services/auth"
)

func TestNewUpdater(t *testing.T) {
	t.Parallel()
	NewUpdater()
}

func Test_updater_Update(t *testing.T) {
	t.Parallel()
	type fields struct {
		shipmentRepo          func(ctrl *gomock.Controller) repo_interface.ShipmentRepository
		shipmentLogRepo       func(ctrl *gomock.Controller) repo_interface.ShipmentLogRepository
		transportRepo         func(ctrl *gomock.Controller) repo_interface.TransportRepository
		transportEventRepo    func(ctrl *gomock.Controller) repo_interface.TransportEventRepository
		shipmentTransportRepo func(ctrl *gomock.Controller) repo_interface.ShipmentTransportRepository
		transactionRepo       func(ctrl *gomock.Controller) repo_interface.TransactionRepoInterface
		validator             func(ctrl *gomock.Controller) *validator
		asyncRequestCreator   func(ctrl *gomock.Controller) async_request.AsyncRequestCreator2
	}
	type args struct {
		id      uint
		request shipmentRequest.UpdateRequest
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			"cannot found shipment",
			fields{
				shipmentRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentRepository {
					var shipment *models.Shipment
					shipmentRepo := mocks2.NewMockShipmentRepository(ctrl)
					shipmentRepo.EXPECT().FindOneWithCtx(gomock.Any(), uint(123)).Return(shipment, sql.ErrNoRows)

					return shipmentRepo
				},
				shipmentLogRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentLogRepository {
					shipmentLogRepo := mocks2.NewMockShipmentLogRepository(ctrl)
					return shipmentLogRepo
				},
				transportRepo: func(ctrl *gomock.Controller) repo_interface.TransportRepository {
					transportRepo := mocks2.NewMockTransportRepository(ctrl)

					return transportRepo
				},
				transportEventRepo: func(ctrl *gomock.Controller) repo_interface.TransportEventRepository {
					transportEventRepo := mocks2.NewMockTransportEventRepository(ctrl)

					return transportEventRepo
				},
				shipmentTransportRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentTransportRepository {
					shipmentTransportRepo := mocks2.NewMockShipmentTransportRepository(ctrl)

					return shipmentTransportRepo
				},
				transactionRepo: func(ctrl *gomock.Controller) repo_interface.TransactionRepoInterface {
					transactionRepo := mocks2.NewMockTransactionRepoInterface(ctrl)
					return transactionRepo
				},
				validator:           func(ctrl *gomock.Controller) *validator { return nil },
				asyncRequestCreator: func(ctrl *gomock.Controller) async_request.AsyncRequestCreator2 { return nil },
			},
			args{
				123,
				shipmentRequest.UpdateRequest{},
			},
			true,
		},
		{
			"cannot close shipment",
			fields{
				shipmentRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentRepository {
					shipmentRepo := mocks2.NewMockShipmentRepository(ctrl)
					shipmentRepo.EXPECT().FindOneWithCtx(gomock.Any(), uint(123)).Return(&models.Shipment{
						ID:                 123,
						OriginPort:         "VNN",
						OriginCountry:      "VN",
						DestinationPort:    "SGG",
						DestinationCountry: "SG",
						Status:             shipmentConst.StatusDraft.Uint16(),
					}, nil)

					return shipmentRepo
				},
				shipmentLogRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentLogRepository {
					shipmentLogRepo := mocks2.NewMockShipmentLogRepository(ctrl)
					return shipmentLogRepo
				},
				transportRepo: func(ctrl *gomock.Controller) repo_interface.TransportRepository {
					transportRepo := mocks2.NewMockTransportRepository(ctrl)

					return transportRepo
				},
				transportEventRepo: func(ctrl *gomock.Controller) repo_interface.TransportEventRepository {
					transportEventRepo := mocks2.NewMockTransportEventRepository(ctrl)

					return transportEventRepo
				},
				shipmentTransportRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentTransportRepository {
					shipmentTransportRepo := mocks2.NewMockShipmentTransportRepository(ctrl)

					return shipmentTransportRepo
				},
				transactionRepo: func(ctrl *gomock.Controller) repo_interface.TransactionRepoInterface {
					transactionRepo := mocks2.NewMockTransactionRepoInterface(ctrl)
					return transactionRepo
				},
				validator:           func(ctrl *gomock.Controller) *validator { return nil },
				asyncRequestCreator: func(ctrl *gomock.Controller) async_request.AsyncRequestCreator2 { return nil },
			},
			args{
				123,
				shipmentRequest.UpdateRequest{
					OriginPort: "SGN",
					Status:     null.Uint16From(shipmentConst.StatusConfirmed.Uint16()).Ptr(),
				},
			},
			true,
		},
		{
			"cannot pass optional fields validation",
			fields{
				shipmentRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentRepository {
					shipmentRepo := mocks2.NewMockShipmentRepository(ctrl)
					shipmentRepo.EXPECT().FindOneWithCtx(gomock.Any(), uint(123)).Return(&models.Shipment{
						ID:                 123,
						OriginPort:         "VNN",
						OriginCountry:      "VN",
						DestinationPort:    "SGG",
						DestinationCountry: "SG",
						Status:             shipmentConst.StatusDraft.Uint16(),
					}, nil)

					return shipmentRepo
				},
				shipmentLogRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentLogRepository {
					shipmentLogRepo := mocks2.NewMockShipmentLogRepository(ctrl)
					return shipmentLogRepo
				},
				transportRepo: func(ctrl *gomock.Controller) repo_interface.TransportRepository {
					transportRepo := mocks2.NewMockTransportRepository(ctrl)

					return transportRepo
				},
				transportEventRepo: func(ctrl *gomock.Controller) repo_interface.TransportEventRepository {
					transportEventRepo := mocks2.NewMockTransportEventRepository(ctrl)

					return transportEventRepo
				},
				shipmentTransportRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentTransportRepository {
					shipmentTransportRepo := mocks2.NewMockShipmentTransportRepository(ctrl)

					return shipmentTransportRepo
				},
				transactionRepo: func(ctrl *gomock.Controller) repo_interface.TransactionRepoInterface {
					transactionRepo := mocks2.NewMockTransactionRepoInterface(ctrl)
					return transactionRepo
				},
				validator: func(ctrl *gomock.Controller) *validator {
					return &validator{
						vendorRepo: func() repo_interface.VendorRepositoryInterface {
							var vendor *models.Vendor
							vendorRepo := mocks2.NewMockVendorRepositoryInterface(ctrl)
							vendorRepo.EXPECT().GetOne(gomock.Any(), []qm.QueryMod{models.VendorWhere.ID.EQ(12)}).
								Return(vendor, sql.ErrNoRows)

							return vendorRepo
						}(),
					}
				},
				asyncRequestCreator: func(ctrl *gomock.Controller) async_request.AsyncRequestCreator2 { return nil },
			},
			args{
				123,
				shipmentRequest.UpdateRequest{
					VendorID: null.UintFrom(12).Ptr(),
				},
			},
			true,
		},
		{
			"cannot update port",
			fields{
				shipmentRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentRepository {
					shipmentRepo := mocks2.NewMockShipmentRepository(ctrl)
					shipmentRepo.EXPECT().FindOneWithCtx(gomock.Any(), uint(123)).Return(&models.Shipment{
						ID:                 123,
						OriginPort:         "VNN",
						OriginCountry:      "VN",
						DestinationPort:    "SGG",
						DestinationCountry: "SG",
						Status:             shipmentConst.StatusDraft.Uint16(),
					}, nil)

					return shipmentRepo
				},
				shipmentLogRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentLogRepository {
					shipmentLogRepo := mocks2.NewMockShipmentLogRepository(ctrl)
					return shipmentLogRepo
				},
				transportRepo: func(ctrl *gomock.Controller) repo_interface.TransportRepository {
					transportRepo := mocks2.NewMockTransportRepository(ctrl)

					return transportRepo
				},
				transportEventRepo: func(ctrl *gomock.Controller) repo_interface.TransportEventRepository {
					transportEventRepo := mocks2.NewMockTransportEventRepository(ctrl)

					return transportEventRepo
				},
				shipmentTransportRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentTransportRepository {
					shipmentTransportRepo := mocks2.NewMockShipmentTransportRepository(ctrl)

					return shipmentTransportRepo
				},
				transactionRepo: func(ctrl *gomock.Controller) repo_interface.TransactionRepoInterface {
					transactionRepo := mocks2.NewMockTransactionRepoInterface(ctrl)
					return transactionRepo
				},
				validator:           func(ctrl *gomock.Controller) *validator { return nil },
				asyncRequestCreator: func(ctrl *gomock.Controller) async_request.AsyncRequestCreator2 { return nil },
			},
			args{
				123,
				shipmentRequest.UpdateRequest{
					OriginPort: "SGG",
				},
			},
			true,
		},
		{
			"cannot update country",
			fields{
				shipmentRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentRepository {
					shipmentRepo := mocks2.NewMockShipmentRepository(ctrl)
					shipmentRepo.EXPECT().FindOneWithCtx(gomock.Any(), uint(123)).Return(&models.Shipment{
						ID:                 123,
						OriginPort:         "VNN",
						OriginCountry:      "VN",
						DestinationPort:    "SGG",
						DestinationCountry: "SG",
						Status:             shipmentConst.StatusDraft.Uint16(),
					}, nil)

					return shipmentRepo
				},
				shipmentLogRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentLogRepository {
					shipmentLogRepo := mocks2.NewMockShipmentLogRepository(ctrl)
					return shipmentLogRepo
				},
				transportRepo: func(ctrl *gomock.Controller) repo_interface.TransportRepository {
					transportRepo := mocks2.NewMockTransportRepository(ctrl)

					return transportRepo
				},
				transportEventRepo: func(ctrl *gomock.Controller) repo_interface.TransportEventRepository {
					transportEventRepo := mocks2.NewMockTransportEventRepository(ctrl)

					return transportEventRepo
				},
				shipmentTransportRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentTransportRepository {
					shipmentTransportRepo := mocks2.NewMockShipmentTransportRepository(ctrl)

					return shipmentTransportRepo
				},
				transactionRepo: func(ctrl *gomock.Controller) repo_interface.TransactionRepoInterface {
					transactionRepo := mocks2.NewMockTransactionRepoInterface(ctrl)
					return transactionRepo
				},
				validator:           func(ctrl *gomock.Controller) *validator { return nil },
				asyncRequestCreator: func(ctrl *gomock.Controller) async_request.AsyncRequestCreator2 { return nil },
			},
			args{
				123,
				shipmentRequest.UpdateRequest{
					OriginCountry: "SG",
				},
			},
			true,
		},
		{
			"cannot update shipment due to internal server error",
			fields{
				shipmentRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentRepository {
					shipmentRepo := mocks2.NewMockShipmentRepository(ctrl)
					shipmentRepo.EXPECT().FindOneWithCtx(gomock.Any(), uint(123)).Return(&models.Shipment{
						ID:                 123,
						OriginPort:         "VNN",
						OriginCountry:      "VN",
						DestinationPort:    "SGG",
						DestinationCountry: "SG",
						Status:             shipmentConst.StatusDraft.Uint16(),
					}, nil)

					shipmentRepo.EXPECT().Update(context.TODO(), gomock.Any(), gomock.Any()).Return(sql.ErrConnDone)

					return shipmentRepo
				},
				shipmentLogRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentLogRepository {
					shipmentLogRepo := mocks2.NewMockShipmentLogRepository(ctrl)
					return shipmentLogRepo
				},
				transportRepo: func(ctrl *gomock.Controller) repo_interface.TransportRepository {
					transportRepo := mocks2.NewMockTransportRepository(ctrl)
					transportRepo.EXPECT().ListByShipmentID(gomock.Any(), gomock.Any(), gomock.Any()).Return(models.TransportSlice{{}}, nil)

					return transportRepo
				},
				transportEventRepo: func(ctrl *gomock.Controller) repo_interface.TransportEventRepository {
					transportEventRepo := mocks2.NewMockTransportEventRepository(ctrl)

					return transportEventRepo
				},
				shipmentTransportRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentTransportRepository {
					shipmentTransportRepo := mocks2.NewMockShipmentTransportRepository(ctrl)

					return shipmentTransportRepo
				},
				transactionRepo: func(ctrl *gomock.Controller) repo_interface.TransactionRepoInterface {
					transactionRepo := mocks2.NewMockTransactionRepoInterface(ctrl)
					transactionRepo.EXPECT().BeginTransaction(gomock.Any()).Return(mocks2.NewMockContextTransactor(ctrl), nil)
					transactionRepo.EXPECT().ProcessTransaction(gomock.Any(), fplerror.ErrInternal.NewWithoutStack("common.request.internal.db", sql.ErrConnDone)).Return(nil)

					return transactionRepo
				},
				validator:           func(ctrl *gomock.Controller) *validator { return nil },
				asyncRequestCreator: func(ctrl *gomock.Controller) async_request.AsyncRequestCreator2 { return nil },
			},
			args{
				123,
				shipmentRequest.UpdateRequest{
					OriginPort: "123",
				},
			},
			true,
		},
		{
			"cannot update shipment due to duplicated entry",
			fields{
				shipmentRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentRepository {
					shipmentRepo := mocks2.NewMockShipmentRepository(ctrl)
					shipmentRepo.EXPECT().FindOneWithCtx(gomock.Any(), uint(123)).Return(&models.Shipment{
						ID:                 123,
						OriginPort:         "VNN",
						OriginCountry:      "VN",
						DestinationPort:    "SGG",
						DestinationCountry: "SG",
						Status:             shipmentConst.StatusDraft.Uint16(),
					}, nil)

					shipmentRepo.EXPECT().Update(context.TODO(), gomock.Any(), gomock.Any()).
						Return(&mysql.MySQLError{Number: mysqlerr.ER_DUP_ENTRY})

					return shipmentRepo
				},
				shipmentLogRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentLogRepository {
					shipmentLogRepo := mocks2.NewMockShipmentLogRepository(ctrl)
					return shipmentLogRepo
				},
				transportRepo: func(ctrl *gomock.Controller) repo_interface.TransportRepository {
					transportRepo := mocks2.NewMockTransportRepository(ctrl)
					transportRepo.EXPECT().ListByShipmentID(gomock.Any(), gomock.Any(), gomock.Any()).Return(models.TransportSlice{{}}, nil)

					return transportRepo
				},
				transportEventRepo: func(ctrl *gomock.Controller) repo_interface.TransportEventRepository {
					transportEventRepo := mocks2.NewMockTransportEventRepository(ctrl)

					return transportEventRepo
				},
				shipmentTransportRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentTransportRepository {
					shipmentTransportRepo := mocks2.NewMockShipmentTransportRepository(ctrl)

					return shipmentTransportRepo
				},
				transactionRepo: func(ctrl *gomock.Controller) repo_interface.TransactionRepoInterface {
					transactionRepo := mocks2.NewMockTransactionRepoInterface(ctrl)
					transactionRepo.EXPECT().BeginTransaction(gomock.Any()).Return(mocks2.NewMockContextTransactor(ctrl), nil)
					transactionRepo.EXPECT().ProcessTransaction(gomock.Any(), shipmentDuplicatedError).Return(nil)

					return transactionRepo
				},
				validator:           func(ctrl *gomock.Controller) *validator { return nil },
				asyncRequestCreator: func(ctrl *gomock.Controller) async_request.AsyncRequestCreator2 { return nil },
			},
			args{
				123,
				shipmentRequest.UpdateRequest{
					OriginPort: "123",
				},
			},
			true,
		},
		{
			"updated shipment successfully without changing its status",
			fields{
				shipmentRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentRepository {
					shipmentRepo := mocks2.NewMockShipmentRepository(ctrl)
					shipmentRepo.EXPECT().FindOneWithCtx(gomock.Any(), uint(123)).Return(&models.Shipment{
						ID:                      123,
						OriginPort:              "VNN",
						OriginCountry:           "VN",
						DestinationPort:         "SGG",
						DestinationCountry:      "SG",
						ActualVendorInboundDate: null.TimeFrom(time.Date(2021, 12, 29, 0o0, 0o0, 0o0, 0o00, time.UTC)),
						NoOfParcelsExpected:     null.UintFrom(1),
						NoOfParcelsReceived:     null.UintFrom(1),
						GoodsCategory:           null.StringFrom("ABC"),
						GrossWeight:             null.Float64From(1.99),
						ChargeableWeight:        null.Float64From(2.01),
						Status:                  shipmentConst.StatusDraft.Uint16(),
					}, nil)

					shipmentRepo.EXPECT().Update(context.TODO(), gomock.Any(), gomock.Any()).
						Return(nil)

					return shipmentRepo
				},
				shipmentLogRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentLogRepository {
					shipmentLogRepo := mocks2.NewMockShipmentLogRepository(ctrl)
					shipmentLogRepo.EXPECT().BulkCreate(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return shipmentLogRepo
				},
				transportRepo: func(ctrl *gomock.Controller) repo_interface.TransportRepository {
					transportRepo := mocks2.NewMockTransportRepository(ctrl)
					transportRepo.EXPECT().ListByShipmentID(gomock.Any(), gomock.Any(), gomock.Any()).Return(models.TransportSlice{{}}, nil)

					return transportRepo
				},
				transportEventRepo: func(ctrl *gomock.Controller) repo_interface.TransportEventRepository {
					transportEventRepo := mocks2.NewMockTransportEventRepository(ctrl)

					return transportEventRepo
				},
				shipmentTransportRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentTransportRepository {
					shipmentTransportRepo := mocks2.NewMockShipmentTransportRepository(ctrl)

					return shipmentTransportRepo
				},
				transactionRepo: func(ctrl *gomock.Controller) repo_interface.TransactionRepoInterface {
					transactionRepo := mocks2.NewMockTransactionRepoInterface(ctrl)
					transactionRepo.EXPECT().BeginTransaction(gomock.Any()).Return(mocks2.NewMockContextTransactor(ctrl), nil)
					transactionRepo.EXPECT().ProcessTransaction(gomock.Any(), nil).Return(nil)

					return transactionRepo
				},
				validator: func(ctrl *gomock.Controller) *validator { return nil },
				asyncRequestCreator: func(ctrl *gomock.Controller) async_request.AsyncRequestCreator2 {
					c := mocks1.NewMockAsyncRequestCreator2(ctrl)
					c.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
					return c
				},
			},
			args{
				123,
				shipmentRequest.UpdateRequest{
					OriginPort:              "123",
					NoOfParcelsExpected:     null.UintFrom(1).Ptr(),
					NoOfParcelsReceived:     null.UintFrom(1).Ptr(),
					ActualVendorInboundDate: null.TimeFrom(time.Date(2021, 12, 29, 0o0, 0o0, 0o0, 0o00, time.UTC)),
					GoodsCategory:           null.StringFrom("ABC").Ptr(),
					GrossWeight:             null.Float64From(1.99).Ptr(),
					ChargeableWeight:        null.Float64From(2.01).Ptr(),
				},
			},
			false,
		},
		{
			"updated shipment to confirmed unsuccessfully (got internal status when retrive shipment parcels)",
			fields{
				shipmentRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentRepository {
					shipmentRepo := mocks2.NewMockShipmentRepository(ctrl)
					shipmentRepo.EXPECT().FindOneWithCtx(gomock.Any(), uint(123)).Return(&models.Shipment{
						ID:                 123,
						OriginPort:         "VNN",
						OriginCountry:      "VN",
						DestinationPort:    "SGG",
						DestinationCountry: "SG",
						NoOfBags:           10,
						NoOfParcels:        100,
						Status:             shipmentConst.StatusDraft.Uint16(),
					}, nil)

					return shipmentRepo
				},
				shipmentLogRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentLogRepository {
					shipmentLogRepo := mocks2.NewMockShipmentLogRepository(ctrl)
					return shipmentLogRepo
				},
				transportRepo: func(ctrl *gomock.Controller) repo_interface.TransportRepository {
					transportRepo := mocks2.NewMockTransportRepository(ctrl)
					transportRepo.EXPECT().ListByShipmentID(gomock.Any(), gomock.Any(), gomock.Any()).Return(models.TransportSlice{{}}, nil)

					return transportRepo
				},
				transportEventRepo: func(ctrl *gomock.Controller) repo_interface.TransportEventRepository {
					transportEventRepo := mocks2.NewMockTransportEventRepository(ctrl)

					return transportEventRepo
				},
				shipmentTransportRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentTransportRepository {
					shipmentTransportRepo := mocks2.NewMockShipmentTransportRepository(ctrl)

					return shipmentTransportRepo
				},
				transactionRepo: func(ctrl *gomock.Controller) repo_interface.TransactionRepoInterface {
					transactionRepo := mocks2.NewMockTransactionRepoInterface(ctrl)
					return transactionRepo
				},
				validator: func(ctrl *gomock.Controller) *validator {
					return &validator{
						vendorRepo: func() repo_interface.VendorRepositoryInterface {
							vendorRepo := mocks2.NewMockVendorRepositoryInterface(ctrl)
							vendorRepo.EXPECT().GetOne(gomock.Any(), []qm.QueryMod{models.VendorWhere.ID.EQ(123)}).
								Return(&models.Vendor{ID: 123}, nil).MaxTimes(1)
							vendorRepo.EXPECT().GetOne(gomock.Any(), []qm.QueryMod{models.VendorWhere.ID.EQ(124)}).
								Return(&models.Vendor{ID: 124}, nil).MaxTimes(1)

							return vendorRepo
						}(),
						shipmentParcelRepo: func() repo_interface.ShipmentParcelRepository {
							shipmentParcelRepo := mocks2.NewMockShipmentParcelRepository(ctrl)
							shipmentParcelRepo.EXPECT().ExistsUnresolvedParcelWithShipmentID(uint(123)).Return(false, sql.ErrNoRows)

							return shipmentParcelRepo
						}(),
					}
				},
				asyncRequestCreator: func(ctrl *gomock.Controller) async_request.AsyncRequestCreator2 { return nil },
			},
			args{
				123,
				shipmentRequest.UpdateRequest{
					Status:      null.Uint16From(shipmentConst.StatusConfirmed.Uint16()).Ptr(),
					ETD:         null.TimeFrom(time.Date(2020, 10, 12, 23, 59, 59, 0, time.Local)),
					ETA:         null.TimeFrom(time.Date(2020, 10, 13, 23, 59, 59, 0, time.Local)),
					ReferenceID: null.StringFrom("ABC").Ptr(),
					VendorID:    null.UintFrom(123).Ptr(),
					ConsigneeID: null.UintFrom(124).Ptr(),
					ShipperID:   null.UintFrom(100).Ptr(),
					Type:        null.Uint8From(1).Ptr(),
				},
			},
			true,
		},
		{
			"updated shipment to confirmed unsuccessfully (unresolved parcels)",
			fields{
				shipmentRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentRepository {
					shipmentRepo := mocks2.NewMockShipmentRepository(ctrl)
					shipmentRepo.EXPECT().FindOneWithCtx(gomock.Any(), uint(123)).Return(&models.Shipment{
						ID:                 123,
						OriginPort:         "VNN",
						OriginCountry:      "VN",
						DestinationPort:    "SGG",
						DestinationCountry: "SG",
						NoOfBags:           10,
						NoOfParcels:        100,
						Status:             shipmentConst.StatusDraft.Uint16(),
					}, nil)

					return shipmentRepo
				},
				shipmentLogRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentLogRepository {
					shipmentLogRepo := mocks2.NewMockShipmentLogRepository(ctrl)
					return shipmentLogRepo
				},
				transportRepo: func(ctrl *gomock.Controller) repo_interface.TransportRepository {
					transportRepo := mocks2.NewMockTransportRepository(ctrl)
					transportRepo.EXPECT().ListByShipmentID(gomock.Any(), gomock.Any(), gomock.Any()).Return(models.TransportSlice{{}}, nil)

					return transportRepo
				},
				transportEventRepo: func(ctrl *gomock.Controller) repo_interface.TransportEventRepository {
					transportEventRepo := mocks2.NewMockTransportEventRepository(ctrl)

					return transportEventRepo
				},
				shipmentTransportRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentTransportRepository {
					shipmentTransportRepo := mocks2.NewMockShipmentTransportRepository(ctrl)

					return shipmentTransportRepo
				},
				transactionRepo: func(ctrl *gomock.Controller) repo_interface.TransactionRepoInterface {
					transactionRepo := mocks2.NewMockTransactionRepoInterface(ctrl)
					return transactionRepo
				},
				validator: func(ctrl *gomock.Controller) *validator {
					return &validator{
						vendorRepo: func() repo_interface.VendorRepositoryInterface {
							vendorRepo := mocks2.NewMockVendorRepositoryInterface(ctrl)
							vendorRepo.EXPECT().GetOne(gomock.Any(), []qm.QueryMod{models.VendorWhere.ID.EQ(123)}).
								Return(&models.Vendor{ID: 123}, nil).MaxTimes(1)
							vendorRepo.EXPECT().GetOne(gomock.Any(), []qm.QueryMod{models.VendorWhere.ID.EQ(124)}).
								Return(&models.Vendor{ID: 124}, nil).MaxTimes(1)

							return vendorRepo
						}(),
						shipmentParcelRepo: func() repo_interface.ShipmentParcelRepository {
							shipmentParcelRepo := mocks2.NewMockShipmentParcelRepository(ctrl)
							shipmentParcelRepo.EXPECT().ExistsUnresolvedParcelWithShipmentID(uint(123)).Return(true, nil)

							return shipmentParcelRepo
						}(),
					}
				},
				asyncRequestCreator: func(ctrl *gomock.Controller) async_request.AsyncRequestCreator2 { return nil },
			},
			args{
				123,
				shipmentRequest.UpdateRequest{
					Status:      null.Uint16From(shipmentConst.StatusConfirmed.Uint16()).Ptr(),
					ETD:         null.TimeFrom(time.Date(2020, 10, 12, 23, 59, 59, 0, time.Local)),
					ETA:         null.TimeFrom(time.Date(2020, 10, 13, 23, 59, 59, 0, time.Local)),
					ReferenceID: null.StringFrom("ABC").Ptr(),
					VendorID:    null.UintFrom(123).Ptr(),
					ConsigneeID: null.UintFrom(124).Ptr(),
					ShipperID:   null.UintFrom(100).Ptr(),
					Type:        null.Uint8From(1).Ptr(),
				},
			},
			true,
		},
		{
			"updated shipment status to confirmed successfully",
			fields{
				shipmentRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentRepository {
					shipmentRepo := mocks2.NewMockShipmentRepository(ctrl)
					shipmentRepo.EXPECT().FindOneWithCtx(gomock.Any(), uint(123)).Return(&models.Shipment{
						ID:                 123,
						OriginPort:         "VNN",
						OriginCountry:      "VN",
						DestinationPort:    "SGG",
						DestinationCountry: "SG",
						NoOfBags:           10,
						NoOfParcels:        100,
						Status:             shipmentConst.StatusDraft.Uint16(),
					}, nil)

					shipmentRepo.EXPECT().Update(context.TODO(), gomock.Any(), gomock.Any()).
						Return(nil)

					return shipmentRepo
				},
				shipmentLogRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentLogRepository {
					shipmentLogRepo := mocks2.NewMockShipmentLogRepository(ctrl)
					shipmentLogRepo.EXPECT().BulkCreate(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return shipmentLogRepo
				},
				transportRepo: func(ctrl *gomock.Controller) repo_interface.TransportRepository {
					transportRepo := mocks2.NewMockTransportRepository(ctrl)
					transportRepo.EXPECT().ListByShipmentID(gomock.Any(), gomock.Any(), gomock.Any()).Return(models.TransportSlice{{}}, nil)

					return transportRepo
				},
				transportEventRepo: func(ctrl *gomock.Controller) repo_interface.TransportEventRepository {
					transportEventRepo := mocks2.NewMockTransportEventRepository(ctrl)

					return transportEventRepo
				},
				shipmentTransportRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentTransportRepository {
					shipmentTransportRepo := mocks2.NewMockShipmentTransportRepository(ctrl)

					return shipmentTransportRepo
				},
				transactionRepo: func(ctrl *gomock.Controller) repo_interface.TransactionRepoInterface {
					transactionRepo := mocks2.NewMockTransactionRepoInterface(ctrl)
					transactionRepo.EXPECT().BeginTransaction(gomock.Any()).Return(mocks2.NewMockContextTransactor(ctrl), nil)
					transactionRepo.EXPECT().ProcessTransaction(gomock.Any(), nil).Return(nil)

					return transactionRepo
				},
				validator: func(ctrl *gomock.Controller) *validator {
					return &validator{
						vendorRepo: func() repo_interface.VendorRepositoryInterface {
							vendorRepo := mocks2.NewMockVendorRepositoryInterface(ctrl)
							vendorRepo.EXPECT().GetOne(gomock.Any(), []qm.QueryMod{models.VendorWhere.ID.EQ(123)}).
								Return(&models.Vendor{ID: 123}, nil).MaxTimes(1)
							vendorRepo.EXPECT().GetOne(gomock.Any(), []qm.QueryMod{models.VendorWhere.ID.EQ(124)}).
								Return(&models.Vendor{ID: 124}, nil).MaxTimes(1)

							return vendorRepo
						}(),
						shipperRepo: func() repo_interface.ShipperRepositoryInterface {
							shipperRepo := mocks2.NewMockShipperRepositoryInterface(ctrl)
							shipperRepo.EXPECT().GetOne(gomock.Any(), []qm.QueryMod{models.ShipperWhere.ID.EQ(100)}).
								Return(&models.Shipper{ID: 100}, nil)

							return shipperRepo
						}(),
						shipmentParcelRepo: func() repo_interface.ShipmentParcelRepository {
							shipmentParcelRepo := mocks2.NewMockShipmentParcelRepository(ctrl)
							shipmentParcelRepo.EXPECT().ExistsUnresolvedParcelWithShipmentID(uint(123)).Return(false, nil)

							return shipmentParcelRepo
						}(),
					}
				},
				asyncRequestCreator: func(ctrl *gomock.Controller) async_request.AsyncRequestCreator2 {
					c := mocks1.NewMockAsyncRequestCreator2(ctrl)
					c.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
					return c
				},
			},
			args{
				123,
				shipmentRequest.UpdateRequest{
					Status:             null.Uint16From(shipmentConst.StatusConfirmed.Uint16()).Ptr(),
					ETD:                null.TimeFrom(time.Date(2020, 10, 12, 23, 59, 59, 0, time.Local)),
					ETA:                null.TimeFrom(time.Date(2020, 10, 13, 23, 59, 59, 0, time.Local)),
					OriginPort:         "kal",
					OriginCountry:      "ks",
					DestinationPort:    "kul",
					DestinationCountry: "my",
					ClosedAt:           null.TimeFrom(time.Date(2020, 10, 13, 23, 59, 59, 0, time.Local)),
					ReadyToAssign:      null.BoolFrom(true).Ptr(),
					Metadata:           null.StringFrom(`{"booking_weight": 1, "booking_cbm": 2, "freight_type": 3}`).Ptr(),
					ReferenceID:        null.StringFrom("ABC").Ptr(),
					VendorID:           null.UintFrom(123).Ptr(),
					ConsigneeID:        null.UintFrom(124).Ptr(),
					ShipperID:          null.UintFrom(100).Ptr(),
					Type:               null.Uint8From(1).Ptr(),
				},
			},
			false,
		},
		{
			"updated shipment status to confirmed with empty parcel successfully",
			fields{
				shipmentRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentRepository {
					shipmentRepo := mocks2.NewMockShipmentRepository(ctrl)
					shipmentRepo.EXPECT().FindOneWithCtx(gomock.Any(), uint(123)).Return(&models.Shipment{
						ID:                 123,
						OriginPort:         "VNN",
						OriginCountry:      "VN",
						DestinationPort:    "SGG",
						DestinationCountry: "SG",
						GrossWeight:        null.Float64From(10),
						NoOfBags:           10,
						NoOfParcels:        0,
						Status:             shipmentConst.StatusDraft.Uint16(),
					}, nil)

					shipmentRepo.EXPECT().Update(context.TODO(), gomock.Any(), gomock.Any()).
						Return(nil)

					return shipmentRepo
				},
				shipmentLogRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentLogRepository {
					shipmentLogRepo := mocks2.NewMockShipmentLogRepository(ctrl)
					shipmentLogRepo.EXPECT().BulkCreate(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return shipmentLogRepo
				},
				transportRepo: func(ctrl *gomock.Controller) repo_interface.TransportRepository {
					transportRepo := mocks2.NewMockTransportRepository(ctrl)
					transportRepo.EXPECT().ListByShipmentID(gomock.Any(), gomock.Any(), gomock.Any()).Return(models.TransportSlice{{}}, nil)
					transportRepo.EXPECT().DeleteByIDs(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
					transportRepo.EXPECT().UpdateTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
					transportRepo.EXPECT().CreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return transportRepo
				},
				transportEventRepo: func(ctrl *gomock.Controller) repo_interface.TransportEventRepository {
					transportEventRepo := mocks2.NewMockTransportEventRepository(ctrl)
					transportEventRepo.EXPECT().DeleteByTransportIDs(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return transportEventRepo
				},
				shipmentTransportRepo: func(ctrl *gomock.Controller) repo_interface.ShipmentTransportRepository {
					shipmentTransportRepo := mocks2.NewMockShipmentTransportRepository(ctrl)
					shipmentTransportRepo.EXPECT().DeleteByTransportIDs(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
					shipmentTransportRepo.EXPECT().CreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

					return shipmentTransportRepo
				},
				transactionRepo: func(ctrl *gomock.Controller) repo_interface.TransactionRepoInterface {
					transactionRepo := mocks2.NewMockTransactionRepoInterface(ctrl)
					transactionRepo.EXPECT().BeginTransaction(gomock.Any()).Return(mocks2.NewMockContextTransactor(ctrl), nil)
					transactionRepo.EXPECT().ProcessTransaction(gomock.Any(), nil).Return(nil)

					return transactionRepo
				},
				validator: func(ctrl *gomock.Controller) *validator {
					return &validator{
						vendorRepo: func() repo_interface.VendorRepositoryInterface {
							vendorRepo := mocks2.NewMockVendorRepositoryInterface(ctrl)
							vendorRepo.EXPECT().GetOne(gomock.Any(), []qm.QueryMod{models.VendorWhere.ID.EQ(123)}).
								Return(&models.Vendor{ID: 123}, nil).MaxTimes(1)
							vendorRepo.EXPECT().GetOne(gomock.Any(), []qm.QueryMod{models.VendorWhere.ID.EQ(124)}).
								Return(&models.Vendor{ID: 124}, nil).MaxTimes(1)

							return vendorRepo
						}(),
						shipperRepo: func() repo_interface.ShipperRepositoryInterface {
							shipperRepo := mocks2.NewMockShipperRepositoryInterface(ctrl)
							shipperRepo.EXPECT().GetOne(gomock.Any(), []qm.QueryMod{models.ShipperWhere.ID.EQ(100)}).
								Return(&models.Shipper{ID: 100}, nil)

							return shipperRepo
						}(),
						shipmentParcelRepo: func() repo_interface.ShipmentParcelRepository {
							shipmentParcelRepo := mocks2.NewMockShipmentParcelRepository(ctrl)
							shipmentParcelRepo.EXPECT().ExistsUnresolvedParcelWithShipmentID(uint(123)).Return(false, nil)
							shipmentParcelRepo.EXPECT().GetListWithCtx(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(models.ShipmentParcelSlice{
								{
									RequestedBagTrackingID: null.StringFrom("BAG"),
								},
							}, nil)

							return shipmentParcelRepo
						}(),
						parcelRepo: func() repo_interface.ParcelRepository {
							parcelRepo := mocks2.NewMockParcelRepository(ctrl)
							parcelRepo.EXPECT().GetListWithCtx(
								gomock.Any(),
								gomock.Any(),
								models.ParcelWhere.TrackingID.IN([]string{"BAG"}),
								gomock.Any(),
							).Return(models.ParcelSlice{
								{
									TrackingID: "BAG",
								},
							}, nil)

							return parcelRepo
						}(),
					}
				},
				asyncRequestCreator: func(ctrl *gomock.Controller) async_request.AsyncRequestCreator2 {
					c := mocks1.NewMockAsyncRequestCreator2(ctrl)
					c.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
					return c
				},
			},
			args{
				123,
				shipmentRequest.UpdateRequest{
					Status:             null.Uint16From(shipmentConst.StatusConfirmed.Uint16()).Ptr(),
					ETD:                null.TimeFrom(time.Date(2020, 10, 12, 23, 59, 59, 0, time.Local)),
					ETA:                null.TimeFrom(time.Date(2020, 10, 13, 23, 59, 59, 0, time.Local)),
					OriginPort:         "kal",
					OriginCountry:      "ks",
					DestinationPort:    "kul",
					DestinationCountry: "my",
					ClosedAt:           null.TimeFrom(time.Date(2020, 10, 13, 23, 59, 59, 0, time.Local)),
					ReadyToAssign:      null.BoolFrom(true).Ptr(),
					Metadata:           null.StringFrom(`{"booking_weight": 1, "booking_cbm": 2, "freight_type": 3}`).Ptr(),
					ReferenceID:        null.StringFrom("ABC").Ptr(),
					VendorID:           null.UintFrom(123).Ptr(),
					ConsigneeID:        null.UintFrom(124).Ptr(),
					ShipperID:          null.UintFrom(100).Ptr(),
					Type:               null.Uint8From(1).Ptr(),
					Transports: []*shipmentRequest.TransportItem{
						{
							ID:                     null.UintFrom(1).Ptr(),
							UpliftStatus:           null.Uint8From(2).Ptr(),
							NoOfBagsUplifted:       null.UintFrom(1).Ptr(),
							EstGrossWeightUplifted: null.Float64From(1).Ptr(),
						},
						{
							TransportIndex:         1,
							NoOfBagsUplifted:       null.UintFrom(1).Ptr(),
							EstGrossWeightUplifted: null.Float64From(1).Ptr(),
							ETD:                    null.TimeFrom(time.Now()),
							ETA:                    null.TimeFrom(time.Now()),
						},
					},
				},
			},
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			u := &updater{
				shipmentRepo:          tt.fields.shipmentRepo(ctrl),
				shipmentLogRepo:       tt.fields.shipmentLogRepo(ctrl),
				transportRepo:         tt.fields.transportRepo(ctrl),
				transportEventRepo:    tt.fields.transportEventRepo(ctrl),
				shipmentTransportRepo: tt.fields.shipmentTransportRepo(ctrl),
				transactionRepo:       tt.fields.transactionRepo(ctrl),
				validator:             tt.fields.validator(ctrl),
				asyncRequestCreator:   tt.fields.asyncRequestCreator(ctrl),
			}
			if err := u.Update(context.TODO(), tt.args.id, tt.args.request, auth.UserInfo{}); (err != nil) != tt.wantErr {
				t.Errorf("updater.Update() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_buildShipmentLogForUpdateShipment(t *testing.T) {
	type args struct {
		oldShipment models.Shipment
		newShipment *models.Shipment
		createdBy   string
	}

	tests := []struct {
		name string
		args args
		want *models.ShipmentLogTab
	}{
		{
			name: "only update columns",
			args: args{
				oldShipment: models.Shipment{
					ID:          1,
					OriginPort:  "SIN",
					ConsigneeID: null.UintFrom(1),
					Etd:         null.TimeFrom(time.Date(2023, 1, 1, 2, 34, 14, 0, time.UTC)),
				},
				newShipment: &models.Shipment{
					ID:          1,
					OriginPort:  "KUL",
					ConsigneeID: null.UintFrom(2),
					Etd:         null.TimeFrom(time.Date(2023, 2, 1, 2, 34, 14, 0, time.UTC)),
				},
				createdBy: "<EMAIL>",
			},
			want: &models.ShipmentLogTab{
				ShipmentID:  1,
				LogType:     uint8(shipmentConst.UpdateShipment),
				CreatedBy:   null.StringFrom("<EMAIL>"),
				OriginState: null.StringFrom(`{"etd":"2023-01-01T02:34:14Z","origin_port":"SIN"}`),
				UpdateState: null.StringFrom(`{"etd":"2023-02-01T02:34:14Z","origin_port":"KUL"}`),
			},
		},
		{
			name: "only update metadata",
			args: args{
				oldShipment: models.Shipment{
					ID:       1,
					Metadata: `{"vessel_no": "AB", "notes": "note"}`,
				},
				newShipment: &models.Shipment{
					ID:       1,
					Metadata: `{"vessel_no": "CD", "notes": "notes"}`,
				},
				createdBy: "<EMAIL>",
			},
			want: &models.ShipmentLogTab{
				ShipmentID:  1,
				LogType:     uint8(shipmentConst.UpdateShipment),
				CreatedBy:   null.StringFrom("<EMAIL>"),
				OriginState: null.StringFrom(`{"metadata.VesselNo":"AB"}`),
				UpdateState: null.StringFrom(`{"metadata.VesselNo":"CD"}`),
			},
		},
		{
			name: "both columns and metadata",
			args: args{
				oldShipment: models.Shipment{
					ID:         1,
					OriginPort: "SIN",
					Metadata:   `{"vessel_no": "AB", "notes": "note"}`,
				},
				newShipment: &models.Shipment{
					ID:         1,
					OriginPort: "KUL",
					Metadata:   `{"vessel_no": "CD", "notes": "notes"}`,
				},
				createdBy: "<EMAIL>",
			},
			want: &models.ShipmentLogTab{
				ShipmentID:  1,
				LogType:     uint8(shipmentConst.UpdateShipment),
				CreatedBy:   null.StringFrom("<EMAIL>"),
				OriginState: null.StringFrom(`{"metadata.VesselNo":"AB","origin_port":"SIN"}`),
				UpdateState: null.StringFrom(`{"metadata.VesselNo":"CD","origin_port":"KUL"}`),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			want := buildShipmentLogForUpdateShipment(tt.args.oldShipment, tt.args.newShipment, tt.args.createdBy)

			assert.Equal(t, tt.want, want, "got unexpected result, want %v, got %v",
				tt.want, want)
		})
	}
}

func Test_buildShipmentLogForChangeUpliftStatus(t *testing.T) {
	type args struct {
		shipment      *models.Shipment
		transportsReq []*shipmentRequest.TransportItem
		transports    models.TransportSlice
		createdBy     string
	}

	tests := []struct {
		name string
		args args
		want *models.ShipmentLogTab
	}{
		{
			name: "skip due to not change uplift status",
			args: args{},
		},
		{
			name: "get shipment log successfully",
			args: args{
				shipment: &models.Shipment{
					ID: 1,
				},
				transportsReq: []*shipmentRequest.TransportItem{
					{
						ID:           null.UintFrom(1).Ptr(),
						UpliftStatus: null.Uint8From(uint8(shipmentConst.OverallUpliftStatusPartialUplift)).Ptr(),
					},
				},
				transports: models.TransportSlice{
					{
						ID:           1,
						UpliftStatus: null.Uint8From(uint8(transport.UpliftStatusFullUplift)),
					},
				},
				createdBy: "<EMAIL>",
			},
			want: &models.ShipmentLogTab{
				ShipmentID:  1,
				LogType:     uint8(shipmentConst.ChangeUpliftStatus),
				CreatedBy:   null.StringFrom("<EMAIL>"),
				OriginState: null.StringFrom(`{"uplift_status":1}`),
				UpdateState: null.StringFrom(`{"transports":[{"id":1,"uplift_status":2,"transport_index":0,"etd":null,"eta":null}],"uplift_status":2}`),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			want := buildShipmentLogForChangeUpliftStatus(tt.args.shipment, tt.args.transportsReq, tt.args.transports, tt.args.createdBy)

			assert.Equal(t, tt.want, want, "got unexpected result, want %v, got %v",
				tt.want, want)
		})
	}
}

func Test_populateTransportToShipment(t *testing.T) {
	t.Parallel()

	type args struct {
		upliftStatus          *uint8
		noOfBagsUpliftedTotal *uint
		shipment              *models.Shipment
	}

	type want struct {
		overallUpliftStatus uint8
	}

	tests := []struct {
		name string
		args args
		want want
	}{
		{
			name: "N/Á",
			args: args{
				upliftStatus: null.Uint8From(uint8(shipmentConst.OverallUpliftStatusFullUplift)).Ptr(),
				shipment: &models.Shipment{
					Status: uint16(shipmentConst.StatusDraft),
				},
			},
		},
		{
			name: "full uplift",
			args: args{
				upliftStatus: null.Uint8From(uint8(shipmentConst.OverallUpliftStatusFullUplift)).Ptr(),
				shipment: &models.Shipment{
					NoOfBags: 1,
					Status:   uint16(shipmentConst.StatusConfirmed),
				},
			},
			want: want{
				overallUpliftStatus: uint8(shipmentConst.OverallUpliftStatusFullUplift),
			},
		},
		{
			name: "full offload",
			args: args{
				upliftStatus:          null.Uint8From(uint8(shipmentConst.OverallUpliftStatusFullUplift)).Ptr(),
				noOfBagsUpliftedTotal: null.UintFrom(0).Ptr(),
				shipment: &models.Shipment{
					NoOfBags: 1,
					Status:   uint16(shipmentConst.StatusConfirmed),
				},
			},
			want: want{
				overallUpliftStatus: uint8(shipmentConst.OverallUpliftStatusFullOffload),
			},
		},
		{
			name: "partial uplift",
			args: args{
				upliftStatus:          null.Uint8From(uint8(shipmentConst.OverallUpliftStatusFullUplift)).Ptr(),
				noOfBagsUpliftedTotal: null.UintFrom(1).Ptr(),
				shipment: &models.Shipment{
					NoOfBags: 2,
					Status:   uint16(shipmentConst.StatusConfirmed),
				},
			},
			want: want{
				overallUpliftStatus: uint8(shipmentConst.OverallUpliftStatusPartialUplift),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			populateTransportToShipment(tt.args.upliftStatus, tt.args.noOfBagsUpliftedTotal, tt.args.shipment)

			assert.Equal(t, tt.want.overallUpliftStatus, tt.args.shipment.OverallUpliftStatus.Uint8, "got unexpected result, want %v, got %v",
				tt.want.overallUpliftStatus, tt.args.shipment.OverallUpliftStatus)
		})
	}
}

func Test_repopulateTransportToShipment(t *testing.T) {
	t.Parallel()

	type args struct {
		shipment *models.Shipment
	}

	type want struct {
		overallUpliftStatus uint8
	}

	tests := []struct {
		name string
		args args
		want want
	}{
		{
			name: "N/Á",
			args: args{
				shipment: &models.Shipment{
					Status: uint16(shipmentConst.StatusDraft),
				},
			},
		},
		{
			name: "full uplift",
			args: args{
				shipment: &models.Shipment{
					NoOfBags:            1,
					Status:              uint16(shipmentConst.StatusConfirmed),
					OverallUpliftStatus: null.Uint8From(uint8(shipmentConst.OverallUpliftStatusFullUplift)),
				},
			},
			want: want{
				overallUpliftStatus: uint8(shipmentConst.OverallUpliftStatusFullUplift),
			},
		},
		{
			name: "full uplift 2",
			args: args{
				shipment: &models.Shipment{
					NoOfBags:            1,
					Status:              uint16(shipmentConst.StatusConfirmed),
					OverallUpliftStatus: null.Uint8From(uint8(shipmentConst.OverallUpliftStatusFullUplift)),
					Metadata:            `{"no_of_bags_uplifted_total":1}`,
				},
			},
			want: want{
				overallUpliftStatus: uint8(shipmentConst.OverallUpliftStatusFullUplift),
			},
		},
		{
			name: "full offload",
			args: args{
				shipment: &models.Shipment{
					NoOfBags:            1,
					Status:              uint16(shipmentConst.StatusConfirmed),
					OverallUpliftStatus: null.Uint8From(uint8(shipmentConst.OverallUpliftStatusFullUplift)),
					Metadata:            `{"no_of_bags_uplifted_total":0}`,
				},
			},
			want: want{
				overallUpliftStatus: uint8(shipmentConst.OverallUpliftStatusFullOffload),
			},
		},
		{
			name: "partial uplift",
			args: args{
				shipment: &models.Shipment{
					NoOfBags:            2,
					Status:              uint16(shipmentConst.StatusConfirmed),
					OverallUpliftStatus: null.Uint8From(uint8(shipmentConst.OverallUpliftStatusFullUplift)),
					Metadata:            `{"no_of_bags_uplifted_total":1}`,
				},
			},
			want: want{
				overallUpliftStatus: uint8(shipmentConst.OverallUpliftStatusPartialUplift),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			repopulateTransportToShipment(tt.args.shipment, 1)

			assert.Equal(t, tt.want.overallUpliftStatus, tt.args.shipment.OverallUpliftStatus.Uint8, "got unexpected result, want %v, got %v",
				tt.want.overallUpliftStatus, tt.args.shipment.OverallUpliftStatus)
		})
	}
}

func Test_validateTransports(t *testing.T) {
	t.Parallel()

	type args struct {
		request  shipmentRequest.UpdateRequest
		shipment *models.Shipment
	}

	tests := []struct {
		name    string
		args    args
		want    []*shipmentRequest.TransportItem
		wantErr bool
	}{
		{
			name: "skip due to transports empty",
		},
		{
			name: "skip due to status is uplift",
			args: args{
				request: shipmentRequest.UpdateRequest{
					Transports: []*shipmentRequest.TransportItem{
						{
							UpliftStatus: null.Uint8From(uint8(transport.UpliftStatusFullUplift)).Ptr(),
						},
					},
				},
				shipment: &models.Shipment{
					NoOfBags: 1,
				},
			},
			want: []*shipmentRequest.TransportItem{
				{
					UpliftStatus: null.Uint8From(uint8(transport.UpliftStatusFullUplift)).Ptr(),
				},
			},
		},
		{
			name: "fail due to number of bags greater than total bags",
			args: args{
				request: shipmentRequest.UpdateRequest{
					Transports: []*shipmentRequest.TransportItem{
						{
							UpliftStatus:     null.Uint8From(uint8(transport.UpliftStatusPartialUplift)).Ptr(),
							NoOfBagsUplifted: null.UintFrom(2).Ptr(),
						},
					},
				},
				shipment: &models.Shipment{
					NoOfBags: 1,
				},
			},
			want: []*shipmentRequest.TransportItem{
				{
					UpliftStatus:     null.Uint8From(uint8(transport.UpliftStatusPartialUplift)).Ptr(),
					NoOfBagsUplifted: null.UintFrom(2).Ptr(),
				},
			},
			wantErr: true,
		},
		{
			name: "fail due to number of bags greater than remaining bags",
			args: args{
				request: shipmentRequest.UpdateRequest{
					Transports: []*shipmentRequest.TransportItem{
						{
							UpliftStatus:     null.Uint8From(uint8(transport.UpliftStatusPartialUplift)).Ptr(),
							NoOfBagsUplifted: null.UintFrom(1).Ptr(),
						},
						{
							NoOfBagsUplifted: null.UintFrom(1).Ptr(),
						},
					},
				},
				shipment: &models.Shipment{
					NoOfBags: 1,
				},
			},
			want: []*shipmentRequest.TransportItem{
				{
					UpliftStatus:     null.Uint8From(uint8(transport.UpliftStatusPartialUplift)).Ptr(),
					NoOfBagsUplifted: null.UintFrom(1).Ptr(),
				},
				{
					NoOfBagsUplifted: null.UintFrom(1).Ptr(),
				},
			},
			wantErr: true,
		},
		{
			name: "fail due to est gross weight greater than gross weight",
			args: args{
				request: shipmentRequest.UpdateRequest{
					Transports: []*shipmentRequest.TransportItem{
						{
							UpliftStatus:           null.Uint8From(uint8(transport.UpliftStatusPartialUplift)).Ptr(),
							EstGrossWeightUplifted: null.Float64From(2).Ptr(),
						},
					},
				},
				shipment: &models.Shipment{
					NoOfBags:    1,
					GrossWeight: null.Float64From(1),
				},
			},
			want: []*shipmentRequest.TransportItem{
				{
					UpliftStatus:           null.Uint8From(uint8(transport.UpliftStatusPartialUplift)).Ptr(),
					EstGrossWeightUplifted: null.Float64From(2).Ptr(),
				},
			},
			wantErr: true,
		},
		{
			name: "fail due to est gross weight greater than gross weight",
			args: args{
				request: shipmentRequest.UpdateRequest{
					Transports: []*shipmentRequest.TransportItem{
						{
							UpliftStatus:           null.Uint8From(uint8(transport.UpliftStatusPartialUplift)).Ptr(),
							EstGrossWeightUplifted: null.Float64From(1).Ptr(),
						},
						{
							EstGrossWeightUplifted: null.Float64From(1).Ptr(),
						},
					},
				},
				shipment: &models.Shipment{
					NoOfBags:    1,
					GrossWeight: null.Float64From(1),
				},
			},
			want: []*shipmentRequest.TransportItem{
				{
					UpliftStatus:           null.Uint8From(uint8(transport.UpliftStatusPartialUplift)).Ptr(),
					EstGrossWeightUplifted: null.Float64From(1).Ptr(),
				},
				{
					EstGrossWeightUplifted: null.Float64From(1).Ptr(),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := validateTransports(tt.args.request, tt.args.shipment)

			if (err != nil) != tt.wantErr {
				t.Errorf("validateTransports() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("validateTransports() got = %v, want %v", got, tt.want)
			}
		})
	}
}
