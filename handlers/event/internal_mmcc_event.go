package event

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/protobuf/proto"

	systemid2 "bitbucket.ninjavan.co/cg/base-commons---go/systemid"
	"bitbucket.ninjavan.co/fpl/4pl-protos/go/fpl"
	"bitbucket.ninjavan.co/protos/proto-commons/go/common"

	"git.ninjavan.co/3pl/configs/internal_status"
	parcelCfg "git.ninjavan.co/3pl/configs/parcel"
	shipmentCfg "git.ninjavan.co/3pl/configs/shipment"
	"git.ninjavan.co/3pl/configs/shipment_parcel"
	ce "git.ninjavan.co/3pl/consumers/consumer_error"
	"git.ninjavan.co/3pl/envs"
	"git.ninjavan.co/3pl/errors/fplerror"
	"git.ninjavan.co/3pl/event_publisher"
	"git.ninjavan.co/3pl/event_publisher/event_publisher_interface"
	"git.ninjavan.co/3pl/handlers/event/event_interface"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/services/xb_ops"
	"git.ninjavan.co/3pl/utils"
	"git.ninjavan.co/3pl/utils/loggerutils"
	"git.ninjavan.co/3pl/utils/protobuf"
)

const (
	failedToGetShipmentAndBagErrMsg = "Failed to get shipment and MMCC parcels due to %v"
	failedToBuildShipmentErrMsg     = "Failed to build shipment event due to %v"
)

type (
	InternalMMCCEventHandler struct {
		parcelRepo                  repo_interface.ParcelRepository
		bagRepo                     repo_interface.BagRepository
		parcelItemRepo              repo_interface.ParcelItemRepository
		partnerRepo                 repo_interface.PartnerRepository
		shipmentRepo                repo_interface.ShipmentRepository
		shipmentDocumentsRepository repo_interface.ShipmentDocumentsRepository
		shipperRepo                 repo_interface.ShipperRepositoryInterface
		shipmentParcelRepo          repo_interface.ShipmentParcelRepository
		shipmentEventRepo           repo_interface.ShipmentEventRepository
		xbService                   xb_ops.XbOpsClient
		eventPublisher              event_publisher_interface.EventPublisher
	}
)

func NewInternalMMCCEventHandler() *InternalMMCCEventHandler {
	return &InternalMMCCEventHandler{
		parcelRepo:                  repositories.NewParcelRepository(),
		bagRepo:                     repositories.NewBagRepository(),
		parcelItemRepo:              repositories.NewParcelItemRepository(),
		partnerRepo:                 repositories.NewPartnerRepository(),
		shipmentRepo:                repositories.NewShipmentRepository(&repositories.UpdateRepo{}),
		shipmentDocumentsRepository: repositories.NewShipmentDocumentsRepository(),
		shipperRepo:                 repositories.NewShipperRepository(),
		shipmentParcelRepo:          repositories.NewShipmentParcelRepository(),
		shipmentEventRepo:           repositories.NewShipmentEventRepository(nil),
		xbService:                   xb_ops.NewXbOpsClient(),
		eventPublisher:              event_publisher.NewEventPublisher(),
	}
}

var (
	errInvalidShipmentEvent = errors.New("shipment current event is invalid")
)

func (o *InternalMMCCEventHandler) HandleMMCCEvent(ctx context.Context, msg *fpl.FplMMCCEvent) (err error) {
	switch msg.Type {
	case fpl.FplMMCCEventType_SHIPMENT_EVT_TYPE:
		return o.handleMMCCEventTypeShipment(ctx, msg.ShipmentEvent)
	case fpl.FplMMCCEventType_PARCELS_EVT_TYPE:
		return o.handleMMCCEventTypeParcels(ctx, msg.ParcelEvents)
	case fpl.FplMMCCEventType_SHIPMENT_UPDATED_EVT_TYPE:
		if msg.ShipmentEvent == nil || msg.Old == nil {
			log.Ctx(ctx).Error().Str("payload", msg.String()).Msg("unexpected-updated-event")
			return nil
		}
		return o.handleMMCCEventTypeShipmentUpdated(ctx, msg.ShipmentEvent, msg.Old)
	default:
		return nil
	}
}

func (o *InternalMMCCEventHandler) handleMMCCEventTypeShipment(ctx context.Context, message *fpl.FplUpdateShipmentEvent) error {
	evt := message.ShipmentEvent
	logger := log.Ctx(ctx).With().
		Int32("movement_status_id", evt.MovementStatusId).
		Uint("shipment_id", uint(evt.ShipmentId)).
		Logger()
	logger = loggerutils.WithContextRequestId(ctx, logger)
	logger.Info().Msg("handle-shipment-event")

	shipment, bags, err := o.getShipmentAndBags(ctx, evt.ShipmentId)
	if err != nil {
		return fplerror.ErrInternal.NewWithoutStack(failedToGetShipmentAndBagErrMsg, err.Error())
	}

	if len(bags) == 0 {
		return nil
	}

	shipmentEvts, err := o.buildShipmentEventsDetail(ctx, shipment, bags, evt)
	if err != nil {
		return fplerror.ErrInternal.NewWithoutStack(failedToBuildShipmentErrMsg, err.Error())
	}
	for _, shipmentEvt := range shipmentEvts {
		switch shipmentEvt.Partner.Id {
		case envs.Instance.Tiktok.PartnerId:
			err = o.publishTiktokShipmentEvents(ctx, evt, shipment, bags, shipmentEvt)
		default:
			err = o.publishMsgWithType(ctx, shipmentEvt, fpl.ShipmentEventType_EVT_SHIPMENT_EVENT)
		}
		if err != nil {
			logger.Err(err).Uint("shipper_id", uint(shipmentEvt.ShipperId)).Msg("publish-shipment-event-failed")
			return &ce.RetriableError{RootCause: err}
		}
	}

	return nil
}

func (o *InternalMMCCEventHandler) handleMMCCEventTypeShipmentUpdated(
	ctx context.Context, message *fpl.FplUpdateShipmentEvent, oldShipment *fpl.FplMMCCEvent_OldShipment,
) error {
	evt := message.ShipmentEvent
	logger := log.Ctx(ctx).With().
		Int32("movement_status_id", evt.MovementStatusId).
		Uint("shipment_id", uint(evt.ShipmentId)).
		Logger()
	logger.Info().Str("old", oldShipment.String()).Msg("handle-shipment-updated-event")

	shipment, bags, err := o.getShipmentAndBags(ctx, evt.ShipmentId)
	if err != nil {
		return fplerror.ErrInternal.NewWithoutStack(failedToGetShipmentAndBagErrMsg, err.Error())
	}

	if len(bags) == 0 {
		return nil
	}

	// Init fake HOTL event here to by pass validate getStatusCode in o.buildShipmentEventsDetail(...)
	evt.MovementStatusId = int32(shipmentCfg.HandedOverToLinehaul)
	evt.EventTime = time.Now().Unix()
	if shipment.CurrentEventID.Valid {
		currentEvent, err := o.shipmentEventRepo.GetOne(ctx, models.ShipmentEventWhere.ID.EQ(shipment.CurrentEventID.Uint))
		if err != nil {
			return fplerror.ErrInternal.NewWithoutStack("Failed to get shipment event due to %v", err.Error())
		}
		if currentEvent != nil {
			evt.MovementStatusId = int32(currentEvent.MovementStatusID)
			evt.EventTime = currentEvent.EventTime.Time.Unix()
		}
	}

	// This simply just to group partner to do shipment update event,
	// currently we don't use shipmentEvtsDetail in Publish function for Tiktok.
	// Can replace by a more simple group bag by partner function
	// But we keep use o.buildShipmentEventsDetail it here if we use shipmentEvtsDetail for other partner in the future
	shipmentEvts, err := o.buildShipmentEventsDetail(ctx, shipment, bags, evt)
	if err != nil {
		return fplerror.ErrInternal.NewWithoutStack(failedToBuildShipmentErrMsg, err.Error())
	}

	for _, shipmentEvt := range shipmentEvts {
		if shipmentEvt.Partner.Id == envs.Instance.Tiktok.PartnerId {
			err = o.publishTiktokShipmentUpdatedEvents(ctx, shipment, bags, evt, oldShipment, shipmentEvt)
			if err != nil {
				logger.Err(err).Uint("shipper_id", uint(shipmentEvt.ShipperId)).Msg("publish-tiktok-shipment-update-failed")
				return &ce.RetriableError{RootCause: err}
			}
		}
	}
	return nil
}

func (o *InternalMMCCEventHandler) handleMMCCEventTypeParcels(ctx context.Context, events []*fpl.FplMMCCEvent_ParcelEvent) error {
	parcelIDs := utils.Map(events, func(t *fpl.FplMMCCEvent_ParcelEvent) uint {
		return uint(t.ParcelId)
	})

	parcels, err := o.parcelRepo.GetListWithCtx(
		ctx,
		models.ParcelWhere.ID.IN(parcelIDs),
		models.ParcelWhere.Type.IN([]uint16{parcelCfg.BagB2B, parcelCfg.BagB2CV2, parcelCfg.MMCCParcel}),
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("failed-to-get-parcels-by-ids")
		return &ce.RetriableError{RootCause: err}
	}
	if len(parcels) == 0 {
		log.Ctx(ctx).Info().Msg("no-mmcc-parcels-found")
		return nil
	}
	parcelById := utils.BuildMap(parcels, func(e *models.Parcel) (key uint, value *models.Parcel) {
		return e.ID, e
	})

	shipmentByParcelID, err := o.buildShipmentByParcelIDFromParcels(ctx, parcels)
	if err != nil {
		return &ce.RetriableError{RootCause: err}
	}

	partnerByID, err := buildPartnerByIDFromParcels(ctx, o.partnerRepo, parcels)
	if err != nil {
		return &ce.RetriableError{RootCause: err}
	}

	shipperByID, err := buildShipperByIDFromParcels(ctx, o.shipperRepo, parcels)
	if err != nil {
		return &ce.RetriableError{RootCause: err}
	}

	events = utils.Filter(events, func(event *fpl.FplMMCCEvent_ParcelEvent) bool {
		return parcelById[uint(event.ParcelId)] != nil
	})

	bagEvents := utils.Filter(events, func(event *fpl.FplMMCCEvent_ParcelEvent) bool {
		return parcelCfg.IsMMCCBag[parcelById[uint(event.ParcelId)].Type]
	})

	err = o.sendBagEvents(ctx, bagEvents, parcelById, shipmentByParcelID, partnerByID, shipperByID)
	if err != nil {
		return err
	}

	err = o.sendTiktokAdhocBagEvents(ctx, bagEvents, parcelById, shipmentByParcelID, partnerByID, shipperByID)
	if err != nil {
		return err
	}

	// send mmcc parcel events
	mmccParcelEvents := utils.Filter(events, func(event *fpl.FplMMCCEvent_ParcelEvent) bool {
		return parcelById[uint(event.ParcelId)].Type == parcelCfg.MMCCParcel
	})
	parcelIds := utils.Map(mmccParcelEvents, func(e *fpl.FplMMCCEvent_ParcelEvent) uint64 {
		return e.ParcelId
	})
	parcelItems, err := o.parcelItemRepo.GetListWithCtx(ctx, models.ParcelItemWhere.ParcelID.IN(parcelIds))
	if err != nil {
		return &ce.RetriableError{RootCause: err}
	}
	itemsByParcelId := map[uint][]*models.ParcelItem{}
	for _, parcelItem := range parcelItems {
		itemsByParcelId[uint(parcelItem.ParcelID)] = append(itemsByParcelId[uint(parcelItem.ParcelID)], parcelItem)
	}
	for _, event := range mmccParcelEvents {
		parcel := parcelById[uint(event.ParcelId)]
		partner := partnerByID[parcel.PartnerID.Uint64]
		globalShipperId := int64(shipperByID[parcel.ShipperID.Uint].RemoteID.Uint)
		shipment := shipmentByParcelID[parcel.ID]

		msg := buildMMCCParcelEvent(
			mmccParcelEventToEventModel(event),
			parcel, itemsByParcelId[parcel.ID], partner.ID, partner.Code, shipment, globalShipperId,
		)

		switch partner.ID {
		case envs.Instance.Lazada.PartnerId:
			err = o.updateLazadaParcelEventInline(ctx, msg, parcel, shipment)
			if err != nil {
				return &ce.RetriableError{RootCause: err}
			}
		case envs.Instance.Tiktok.PartnerId:
			if !internal_status.IsEnabledTTMMCCParcelStatus[fpl.Status(event.GetInternalStatusId())] {
				continue
			}
			msg.TrackingId = parcel.RefTrackingID.String
		default:
		}

		err = o.eventPublisher.Publish(ctx, envs.Instance.KafkaTopics.FplMmccParcelEventTopic, msg)
		if err != nil {
			return &ce.RetriableError{RootCause: err}
		}

		switch partner.ID {
		case envs.Instance.Lazada.PartnerId:
			adhocEvt, err := o.buildLazadaAdhocParcelEvents(ctx, msg, shipment.ID)
			if err != nil {
				return &ce.RetriableError{RootCause: err}
			}

			if adhocEvt != nil {
				err = o.eventPublisher.Publish(ctx, envs.Instance.KafkaTopics.FplMmccParcelEventTopic, adhocEvt)
				if err != nil {
					return &ce.RetriableError{RootCause: err}
				}
			}
		default:
		}
	}

	return nil
}

func (o *InternalMMCCEventHandler) getShipmentAndBags(
	ctx context.Context, shipmentId uint64,
) (*models.Shipment, models.ParcelSlice, error) {
	logger := log.Ctx(ctx).With().
		Uint("shipment_id", uint(shipmentId)).
		Logger()

	shipment, err := o.shipmentRepo.FindOneWithCtx(ctx, uint(shipmentId))
	if err != nil {
		logger.Err(err).Msg("failed-to-query-shipment")
		return nil, nil, &ce.RetriableError{RootCause: err}
	}
	shipmentParcels, err := o.shipmentParcelRepo.GetListWithCtx(ctx,
		qm.Select(models.ShipmentParcelColumns.ParcelID),
		qm.Select(models.ShipmentParcelColumns.RequestedBagTrackingID),
		models.ShipmentParcelWhere.ShipmentID.EQ(shipment.ID),
	)
	if err != nil {
		logger.Err(err).Msg("failed-to-query-shipment-parcels")
		return shipment, nil, &ce.RetriableError{RootCause: err}
	}

	var parcels models.ParcelSlice

	requestedBagTIDs := make([]string, 0)
	mBagTIDs := map[string]struct{}{}
	for _, p := range shipmentParcels {
		if _, ok := mBagTIDs[p.RequestedBagTrackingID.String]; p.RequestedBagTrackingID.Valid && !ok {
			requestedBagTIDs = append(requestedBagTIDs, p.RequestedBagTrackingID.String)
			mBagTIDs[p.RequestedBagTrackingID.String] = struct{}{}
		}
	}

	// V2 contains BoxB2CV2 parcels (without parcel entries in shipment_parcels table), BoxB2B parcels *without* a parcel entry in shipment_parcels table
	// BUG - BoxB2B V2 is not considered; currently, we don't have such bags that require shipment events.
	parcels, err = o.parcelRepo.GetListWithCtx(ctx,
		models.ParcelWhere.TrackingID.IN(requestedBagTIDs),
		models.ParcelWhere.Type.IN([]uint16{parcelCfg.BagB2CV2}),
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("failed-to-get-parcels-by-tracking-ids")
		return nil, nil, &ce.RetriableError{RootCause: err}
	}

	return shipment, parcels, nil
}

// return a list of shipment events, one for each shipper
func (o *InternalMMCCEventHandler) buildShipmentEventsDetail(
	ctx context.Context,
	shipment *models.Shipment,
	bags models.ParcelSlice,
	event *fpl.ShipmentEvent,
) ([]*fpl.FPLShipmentEvent, error) {
	logger := log.Ctx(ctx).With().
		Int32("movement_status_id", event.MovementStatusId).
		Uint("shipment_id", uint(event.ShipmentId)).
		Logger()

	statusCode, err := getStatusCode(uint(event.MovementStatusId))
	if err != nil {
		logger.Err(err).Msg(errMsgNoStatus)
		return nil, errors.New("cannot get status code")
	}

	transportType, ok := shipmentCfg.TransportTypeMap[shipmentCfg.Type(shipment.Type.Uint8)]
	if !ok {
		logger.Error().Msg("unmapped-transport-type")
		return nil, errors.New("unmapped transport type")
	}

	shipperById, err := buildShipperByIDFromParcels(ctx, o.shipperRepo, bags)
	if err != nil {
		return nil, err
	}
	partnerById, err := buildPartnerByIDFromParcels(ctx, o.partnerRepo, bags)
	if err != nil {
		return nil, err
	}

	var documents []*fpl.Document
	shipmentDocDetails, err := o.shipmentDocumentsRepository.GetDetailsByShipmentID(ctx, shipment.ID)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		logger.Err(err).Msg("failed-to-get-shipment-doc-details")
		return nil, err
	}
	if shipmentDocDetails != nil {
		documents = []*fpl.Document{
			{
				Url:      shipmentDocDetails.ShipmentDocument.AssertProxyURL,
				FileType: shipmentDocDetails.Document.FileType,
			},
		}
	}

	bagsByShipperId := lo.GroupBy(bags, func(item *models.Parcel) uint {
		return item.ShipperID.Uint
	})
	res := lo.MapToSlice(bagsByShipperId, func(shipperId uint, bags models.ParcelSlice) *fpl.FPLShipmentEvent {
		metadata := &repositories.Metadata{}
		_ = json.Unmarshal([]byte(shipment.Metadata), metadata)

		bagProtos := lo.Map(bags, func(item *models.Parcel, index int) *fpl.Bag {
			return &fpl.Bag{
				BigBagNo:   item.SourceOrderID.String,
				TrackingId: item.TrackingID,
			}
		})

		transportBookingType := fpl.TransportBookingType_TRANSPORT_BOOKING_TYPE_FLEXIBLE
		if metadata.TransportService.Valid {
			transportBookingType = fpl.TransportBookingType(metadata.TransportService.Uint8)
		}

		opLocation := utils.GetOperateLocation(
			shipment,
			shipmentCfg.MovementStatusToInternalStatusMap[uint(event.MovementStatusId)],
		)
		return &fpl.FPLShipmentEvent{
			ShipperId: int64(shipperById[shipperId].RemoteID.Uint),
			Partner: &fpl.Partner{
				Id:   partnerById[bags[0].PartnerID.Uint64].ID,
				Code: partnerById[bags[0].PartnerID.Uint64].Code,
			},
			Event: &fpl.FPLEvent{
				HappenedAt: event.EventTime,
				StatusCode: statusCode,
				LinehaulInformation: &fpl.LinehaulInformation{
					TransportType:        transportType,
					MAWB:                 shipment.ReferenceID.String,
					OriginPort:           shipment.OriginPort,
					OriginCountry:        shipment.OriginCountry,
					DestinationPort:      shipment.DestinationPort,
					DestinationCountry:   shipment.DestinationCountry,
					Etd:                  shipment.Etd.Time.Unix(),
					Eta:                  shipment.Eta.Time.Unix(),
					VesselNo:             metadata.VesselNo.String,
					Bags:                 bagProtos,
					Documents:            documents,
					Weights:              buildShipmentWeights(bags, shipment),
					TransportBookingType: transportBookingType,
					LoadingType:          fpl.LoadingType_LOADING_TYPE_BUP,
					DeliveryOrNot:        fpl.TiktokDeliveryOrNot_TIKTOK_DELIVERY_TO_DOOR,
					TransitPorts:         nil,
				},
				OperateLocation:     opLocation.Location,
				OperateLocationCode: opLocation.Code,
				OperatedAt:          event_interface.ConvertToProtoOperateLocation(opLocation),
				Status:              fpl.Status(shipmentCfg.MovementStatusToInternalStatusMap[uint(event.MovementStatusId)]),
			},
		}
	})
	return res, nil
}

func (o *InternalMMCCEventHandler) getPastShipmentEvents(ctx context.Context, shipment *models.Shipment) ([]*models.ShipmentEvent, error) {
	return o.shipmentEventRepo.GetList(ctx,
		qm.Select(models.ShipmentEventColumns.ID, models.ShipmentEventColumns.MovementStatusID,
			models.ShipmentEventColumns.EventTime), models.ShipmentEventWhere.ShipmentID.EQ(shipment.ID),
		models.ShipmentEventWhere.EventTime.IsNotNull(),
	)
}

func (o *InternalMMCCEventHandler) publishMsgWithType(ctx context.Context, msg *fpl.FPLShipmentEvent, msgType fpl.ShipmentEventType) error {
	cloned := proto.Clone(msg)

	protoMsg := cloned.(*fpl.FPLShipmentEvent)
	protoMsg.Metadata = &common.Metadata{
		SystemId:  systemid2.Global.Value,
		Timestamp: time.Now().Unix(),
		RequestId: protobuf.GenerateUUID(),
	}
	protoMsg.Type = msgType

	return o.eventPublisher.Publish(ctx, envs.Instance.KafkaTopics.FplShipmentEventTopic, protoMsg)
}

func getStatusCode(movementStatusId uint) (string, error) {
	internalStatusCode, ok := shipmentCfg.MovementStatusToInternalStatusMap[movementStatusId]
	if !ok {
		return "", errors.New("unmapped internal status code")
	}

	statusCode, ok := fpl.Status_name[int32(internalStatusCode)]
	if !ok {
		return "", errors.New("unmapped status code")
	}

	return statusCode, nil
}

func buildShipmentWeights(parcels models.ParcelSlice, shipment *models.Shipment) *fpl.Weights {
	shipperSubmittedWeight := float64(0)
	actualWeight := float64(0)
	dimWeight := float64(0)

	for _, p := range parcels {
		var parcelDetails models.ParcelDetails
		_ = json.Unmarshal([]byte(p.Metadata.String), &parcelDetails)

		shipperSubmittedWeight += null.Float64FromPtr(parcelDetails.ShipperSubmittedWeight).Float64
		actualWeight += null.Float64FromPtr(parcelDetails.ActualWeight).Float64
		dimWeight += computeDimWeight(&parcelDetails)
	}

	// dim_weight_rate = dim weight / send weight (actual wgt.)
	var dimWeightRate float64 = 0
	if actualWeight != 0 {
		dimWeightRate = roundFloatTo6DecimalPlaces(dimWeight / actualWeight)
	}

	return &fpl.Weights{
		ShipperSubmittedWeight: shipperSubmittedWeight,
		ActualWeight:           actualWeight,
		DimensionalWeight:      dimWeight,
		DimensionalWeightRate:  dimWeightRate,
		ChargeableWeight:       shipment.ChargeableWeight.Float64,
		GrossWeight:            shipment.GrossWeight.Float64,
	}
}

func computeDimWeight(parcelMeta *models.ParcelDetails) float64 {
	if parcelMeta.ShipperSubmittedDimensions == nil {
		return 0
	}

	dim := parcelMeta.ShipperSubmittedDimensions
	if dim.Length == nil || dim.Width == nil || dim.Height == nil {
		return 0
	}

	// 6000 cm³/kg
	// dim weight = L x W x H (cm) / dim factor (6000)
	dimWgt := *dim.Length * *dim.Width * *dim.Height / 6000

	return roundFloatTo6DecimalPlaces(dimWgt)
}

func roundFloatTo6DecimalPlaces(x float64) float64 {
	return math.Round(x*1_000_000) / 1_000_000
}

func (o *InternalMMCCEventHandler) buildShipmentByParcelIDFromParcels(ctx context.Context, parcels models.ParcelSlice) (map[uint]*models.Shipment, error) {
	parcelIDs := make([]uint, 0, len(parcels))
	for _, parcel := range parcels {
		parcelIDs = append(parcelIDs, parcel.ID)
	}

	// V1 assignments
	shipmentWrappers, err := o.shipmentRepo.GetListByParcelIDs(ctx, parcelIDs)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("failed-to-get-shipment-wrappers-by-parcel-ids")
		return nil, err
	}

	// V2 assignments
	v2Wrappers, err := o.getV2AssignedShipments(ctx, parcels)
	shipmentWrappers = append(shipmentWrappers, v2Wrappers...)

	shipmentByParcelID := utils.BuildMap(
		shipmentWrappers,
		func(e *repo_interface.ShipmentParcelWrapper) (key uint, value *models.Shipment) {
			return e.ParcelID, &e.Shipment
		},
	)

	return shipmentByParcelID, nil
}

func (o *InternalMMCCEventHandler) getV2AssignedShipments(ctx context.Context, parcels models.ParcelSlice) ([]*repo_interface.ShipmentParcelWrapper, error) {
	bagParcels := utils.Filter(parcels, func(p *models.Parcel) bool {
		return parcelCfg.IsMMCCBag[p.Type]
	})
	if len(bagParcels) == 0 {
		return nil, nil
	}

	return GetShipmentParcelsOfMMCCV2B2CBags(ctx, o.shipmentRepo, o.shipmentParcelRepo, bagParcels)
}

func buildPartnerByIDFromParcels(ctx context.Context, partnerRepo repo_interface.PartnerRepository, parcels models.ParcelSlice) (map[uint64]*models.Partner, error) {
	partnerIDs := make([]uint64, 0, len(parcels))
	for _, parcel := range parcels {
		if parcel.PartnerID.Valid {
			partnerIDs = append(partnerIDs, parcel.PartnerID.Uint64)
		}
	}

	partnerByID := make(map[uint64]*models.Partner)
	if len(partnerIDs) == 0 {
		return partnerByID, nil
	}

	partners, err := partnerRepo.GetList(ctx, models.PartnerWhere.ID.IN(partnerIDs))
	if err != nil {
		return nil, err
	}

	for _, partner := range partners {
		partnerByID[partner.ID] = partner
	}

	return partnerByID, nil
}

func buildShipperByIDFromParcels(
	ctx context.Context, shipperRepo repo_interface.ShipperRepositoryInterface, parcels models.ParcelSlice,
) (map[uint]*models.Shipper, error) {
	shipperIDs := make([]uint, 0, len(parcels))
	for _, parcel := range parcels {
		if parcel.ShipperID.Valid {
			shipperIDs = append(shipperIDs, parcel.ShipperID.Uint)
		}
	}

	shipperByID := make(map[uint]*models.Shipper)
	if len(shipperIDs) == 0 {
		return shipperByID, nil
	}

	shippers, err := shipperRepo.GetList(ctx, models.ShipperWhere.ID.IN(shipperIDs))
	if err != nil {
		return nil, err
	}

	for _, shipper := range shippers {
		shipperByID[shipper.ID] = shipper
	}

	return shipperByID, nil
}

func buildMMCCParcelEvent(
	event *event_interface.Event,
	parcel *models.Parcel,
	parcelItems []*models.ParcelItem,
	partnerId uint64,
	partnerCode string,
	shipment *models.Shipment,
	globalShipperId int64,
) *fpl.FPLParcelEvent {
	items := make([]*fpl.ParcelItem, len(parcelItems))
	for i, parcelItem := range parcelItems {
		itemMeta := &repo_interface.ParcelItemMetadata{}
		_ = json.Unmarshal([]byte(parcelItem.Metadata), itemMeta)
		items[i] = &fpl.ParcelItem{
			SkuId:             null.StringFromPtr(itemMeta.SKUId).String,
			Description:       itemMeta.Description.String,
			NativeDescription: itemMeta.NativeDescription.String,
			HsCode:            fmt.Sprintf("%d", itemMeta.HSCode.Uint),
			UnitValue:         itemMeta.UnitValue.Float64,
			UnitCode:          itemMeta.GoodsCurrency.String,
			UnitWeight:        uint64(itemMeta.UnitWeight.Float64),
			Category:          itemMeta.Tiktok.Category,
			Specification:     itemMeta.Tiktok.Spec,
			Quantity:          int64(itemMeta.Quantity.Int),
		}
	}

	parcelMeta := &repo_interface.ParcelMetadata{}
	_ = json.Unmarshal([]byte(parcel.Metadata.String), parcelMeta)

	stage, operateLocation, reasonCode := utils.GetExtraEventData(event)
	mawb := ""
	if shipment != nil {
		mawb = shipment.ReferenceID.String
	}
	msg := &fpl.FPLParcelEvent{
		TrackingId: parcel.TrackingID,
		Metadata: &common.Metadata{
			SystemId:  systemid2.Global.Value,
			Timestamp: time.Now().Unix(),
			RequestId: protobuf.GenerateUUID(),
		},
		Partner: &fpl.Partner{
			Id:   partnerId,
			Code: partnerCode,
		},
		Event: &fpl.FPLEvent{
			HappenedAt: event.EventTime.Unix(),
			StatusCode: fpl.Status_name[int32(event.InternalStatusID)],
			LinehaulInformation: &fpl.LinehaulInformation{
				MAWB: mawb,
			},
			Stage:               stage,
			RefTrackingId:       parcel.RefTrackingID.String,
			OperateLocation:     operateLocation.GetLocation(),
			Metadata:            reasonCode,
			OperateLocationCode: operateLocation.GetCode(),
			OperatedAt:          operateLocation,
			Status:              fpl.Status(event.InternalStatusID),
		},
		ShipperId:     globalShipperId,
		SourceOrderId: parcelMeta.ShipperRefNo,
		Bag: &fpl.Bag{
			BigBagNo: parcel.SourceOrderID.String,
		},
		Items: items,
	}
	return msg
}

func (o *InternalMMCCEventHandler) sendBagEvents(ctx context.Context,
	bagEvents []*fpl.FplMMCCEvent_ParcelEvent,
	parcelById map[uint]*models.Parcel, shipmentByParcelID map[uint]*models.Shipment,
	partnerByID map[uint64]*models.Partner, shipperByID map[uint]*models.Shipper,
) error {
	nonTiktokLinehaulScheduledBagEvents := utils.Filter(bagEvents, func(event *fpl.FplMMCCEvent_ParcelEvent) bool {
		return !(parcelById[uint(event.ParcelId)].PartnerID.Uint64 == envs.Instance.Tiktok.PartnerId &&
			fpl.Status(event.InternalStatusId) == fpl.Status_LINEHAUL_SCHEDULED)
	})

	for _, event := range nonTiktokLinehaulScheduledBagEvents {
		parcel := parcelById[uint(event.ParcelId)]
		shipment := shipmentByParcelID[parcel.ID]

		fplBagEvent := o.buildFplBagEvent(ctx, event, parcel, shipment, partnerByID, shipperByID)

		switch parcel.PartnerID.Uint64 {
		case envs.Instance.Lazada.PartnerId:
			err := o.updateLazadaBagEventInline(ctx, fplBagEvent, parcel, shipment)
			if err != nil {
				return &ce.RetriableError{RootCause: err}
			}
		default:
		}

		err := o.eventPublisher.Publish(ctx, envs.Instance.KafkaTopics.FplBagEventTopic, fplBagEvent)
		if err != nil {
			return &ce.RetriableError{RootCause: err}
		}
	}

	return nil
}

func (o *InternalMMCCEventHandler) buildFplBagEvent(
	_ context.Context,
	parcelEvent *fpl.FplMMCCEvent_ParcelEvent,
	parcel *models.Parcel,
	shipment *models.Shipment,
	partnerByID map[uint64]*models.Partner,
	shipperByID map[uint]*models.Shipper,
) *fpl.FPLBagEvent {
	internalStatusID := uint(parcelEvent.InternalStatusId)
	operationAt := parcelEvent.OperateLocation
	if operationAt == nil {
		operationAt = &fpl.OperateLocation{
			Location: getOperateLocation(internalStatusID, parcel, shipment),
		}
	}

	fplBagEvent := &fpl.FPLBagEvent{
		Metadata: &common.Metadata{
			SystemId:  systemid2.Global.Value,
			Timestamp: time.Now().Unix(),
			RequestId: protobuf.GenerateUUID(),
		},
		TrackingId:    parcel.TrackingID,
		SourceOrderId: parcel.SourceOrderID.String,
		Event: &fpl.FPLEvent{
			HappenedAt:          parcelEvent.EventTime.GetSeconds(),
			StatusCode:          fpl.Status_name[int32(internalStatusID)],
			OperateLocation:     operationAt.GetLocation(),
			OperateLocationCode: operationAt.GetCode(),
			Stage:               parcelEvent.Stage,
			OperatedAt:          operationAt,
			Status:              fpl.Status(internalStatusID),
			Metadata:            parcelEvent.Metadata,
		},
	}

	if parcel.ShipperID.Valid && shipperByID[parcel.ShipperID.Uint] != nil {
		fplBagEvent.ShipperId = int64(shipperByID[parcel.ShipperID.Uint].RemoteID.Uint)
	}

	if parcel.PartnerID.Valid && partnerByID[parcel.PartnerID.Uint64] != nil {
		fplBagEvent.Partner = &fpl.Partner{
			Id:   partnerByID[parcel.PartnerID.Uint64].ID,
			Code: partnerByID[parcel.PartnerID.Uint64].Code,
		}
	}

	if fpl.Status(internalStatusID) == fpl.Status_HANDED_OVER_TO_ORIGIN_FACILITY {
		return fplBagEvent
	}

	if shipment == nil {
		return fplBagEvent
	}

	linehaulInformation := buildLinehaulInformation(shipment)
	fplBagEvent.Event.LinehaulInformation = linehaulInformation

	return fplBagEvent
}

// TODO clean up once ops deploy operate location
func getOperateLocation(internalStatusId uint, parcel *models.Parcel, shipment *models.Shipment) string {
	switch fpl.Status(internalStatusId) {
	case fpl.Status_HANDED_OVER_TO_ORIGIN_FACILITY,
		fpl.Status_LINEHAUL_SCHEDULED, fpl.Status_LINEHAUL_DEPARTED,
		fpl.Status_EXPORT_STARTED, fpl.Status_EXPORT_CLEARED:
		if shipment == nil {
			if parcel.FromCity.Valid {
				return parcel.FromCity.String
			}
			return ""
		}
		return utils.LookUpPortName(shipment.OriginCountry, shipment.OriginPort)
	default:
		if shipment == nil {
			if parcel.ToCity.Valid {
				return parcel.ToCity.String
			}
			return ""
		}
		return utils.LookUpPortName(shipment.DestinationCountry, shipment.DestinationPort)
	}
}

func buildLinehaulInformation(shipment *models.Shipment) *fpl.LinehaulInformation {
	transportType, _ := shipmentCfg.TransportTypeMap[shipmentCfg.Type(shipment.Type.Uint8)]
	metadata := &repositories.Metadata{}
	_ = json.Unmarshal([]byte(shipment.Metadata), metadata)

	return &fpl.LinehaulInformation{
		TransportType:      transportType,
		MAWB:               shipment.ReferenceID.String,
		OriginPort:         shipment.OriginPort,
		OriginCountry:      shipment.OriginCountry,
		DestinationPort:    shipment.DestinationPort,
		DestinationCountry: shipment.DestinationCountry,
		Etd:                shipment.Etd.Time.Unix(),
		Eta:                shipment.Eta.Time.Unix(),
		VesselNo:           metadata.VesselNo.String,
	}
}

func mmccParcelEventToEventModel(source *fpl.FplMMCCEvent_ParcelEvent) *event_interface.Event {
	extraData := event_interface.ExtraData{
		OperateLocation: event_interface.ConvertToInternalOperateLocation(source.OperateLocation),
		Stage:           source.Stage,
	}
	return event_interface.NewEvent(&models.Event{
		ParcelID:         uint(source.ParcelId),
		InternalStatusID: uint(source.InternalStatusId),
		Metadata:         null.StringFrom(source.Metadata),
		EventTime:        source.EventTime.AsTime(),
	}, extraData)
}

func GetShipmentParcelsOfMMCCV2B2CBags(
	ctx context.Context,
	shipmentRepo repo_interface.ShipmentRepository, shipmentParcelRepo repo_interface.ShipmentParcelRepository,
	bags models.ParcelSlice,
) ([]*repo_interface.ShipmentParcelWrapper, error) {
	tids := lo.Map(bags, func(item *models.Parcel, index int) string {
		return item.TrackingID
	})
	links, err := shipmentParcelRepo.GetListWithCtx(ctx,
		qm.Select(models.ShipmentParcelColumns.ShipmentID, models.ShipmentParcelColumns.RequestedBagTrackingID),
		models.ShipmentParcelWhere.ParcelID.IsNull(),
		models.ShipmentParcelWhere.RequestedBagTrackingID.IN(tids),
		models.ShipmentParcelWhere.Status.IN([]uint8{
			shipment_parcel.StatusOk.Uint8(), shipment_parcel.StatusHandoverConfirmed.Uint8(),
		}),
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("failed-to-get-shipment-parcels-v2-assignments")
		return nil, err
	}
	if len(links) == 0 {
		return nil, nil
	}
	shipmentIds := lo.Map(links, func(sp *models.ShipmentParcel, i int) uint {
		return sp.ShipmentID
	})
	shipments, err := shipmentRepo.GetCompactList(ctx, models.ShipmentWhere.ID.IN(shipmentIds))
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("failed-to-get-shipments-v2-assignments")
		return nil, err
	}

	parcelIdByTrackingId := utils.BuildMap(bags, func(p *models.Parcel) (key string, value uint) {
		return p.TrackingID, p.ID
	})
	shipmentByID := utils.BuildMap(shipments, func(s *models.Shipment) (key uint, value *models.Shipment) {
		return s.ID, s
	})

	wrappers := make([]*repo_interface.ShipmentParcelWrapper, len(links))
	for i := 0; i < len(links); i++ {
		var currentShipment models.Shipment
		s, ok := shipmentByID[links[i].ShipmentID]
		if ok {
			currentShipment = *s
		}

		wrappers[i] = &repo_interface.ShipmentParcelWrapper{
			Shipment: currentShipment,
			ParcelID: parcelIdByTrackingId[links[i].RequestedBagTrackingID.String],
		}
	}
	return wrappers, nil
}

func (o *InternalMMCCEventHandler) HandleUpdateShipmentMMCCBagRelationship(ctx context.Context, shipmentId uint64, moveBagIds []uint) (err error) {
	logger := log.Ctx(ctx).With().
		Uint("shipment_id", uint(shipmentId)).
		Logger()

	logger = loggerutils.WithContextRequestId(ctx, logger)
	logger.Info().Msg("handle-shipment-bag-relationship")

	shipment, bags, err := o.getShipmentAndBags(ctx, shipmentId)
	if err != nil {
		return fplerror.ErrInternal.NewWithoutStack(failedToGetShipmentAndBagErrMsg, err.Error())
	}

	if len(bags) == 0 {
		return nil
	}

	// TODO: work with PI to sent SHIPMENT_BAGS_UPDATED with current shipment status
	// Move this logic query current shipment status to insert async task handler and save in task data
	// shipmentEvent, err := o.getPastShipmentEvents(ctx, shipment)
	// if shipmentEvent == nil && (err == sql.ErrNoRows || err == errInvalidShipmentEvent) {
	//	return nil
	// } else if err != nil {
	//	return fplerror.ErrInternal.NewWithoutStack("Failed to get current shipment event due to %v", err.Error())
	// }

	shipmentEvts, err := o.buildShipmentEventsDetail(ctx, shipment, bags, &fpl.ShipmentEvent{
		ShipmentId:       shipmentId,
		MovementStatusId: int32(shipmentCfg.LinehaulScheduled),
		EventTime:        time.Now().Unix(),
	})
	if err != nil {
		return fplerror.ErrInternal.NewWithoutStack(failedToBuildShipmentErrMsg, err.Error())
	}
	for _, shipmentEvt := range shipmentEvts {
		switch shipmentEvt.Partner.Id {
		case envs.Instance.Tiktok.PartnerId:
			err = o.handleTiktokUpdateShipmentBagRelationship(ctx, shipment, bags, moveBagIds, shipmentEvt)
		}
		if err != nil {
			logger.Err(err).Uint("shipper_id", uint(shipmentEvt.ShipperId)).Msg("publish-shipment-event-failed")
			return err
		}
	}

	return nil
}

func (o *InternalMMCCEventHandler) handleTiktokUpdateShipmentBagRelationship(
	ctx context.Context,
	shipment *models.Shipment,
	bags models.ParcelSlice,
	moveBagIds []uint,
	fplShipmentEvent *fpl.FPLShipmentEvent,
) error {
	hasTTMMCCbag, err := o.movedBagsHasTTMMCCBag(ctx, moveBagIds)
	if err != nil {
		return err
	}

	if !hasTTMMCCbag {
		log.Ctx(ctx).Info().Msg("skip-for-moved-bags-without-tiktok-mmcc-bag")
		return nil
	}

	tiktokParcels := lo.Filter(bags, func(item *models.Parcel, index int) bool {
		return item.PartnerID.Uint64 == envs.Instance.Tiktok.PartnerId
	})

	shipmentEvents, err := o.shipmentEventRepo.GetList(
		ctx,
		qm.Select(models.ShipmentEventColumns.MovementStatusID, models.ShipmentEventColumns.EventTime),
		models.ShipmentEventWhere.ShipmentID.EQ(shipment.ID),
		models.ShipmentEventWhere.EventTime.IsNotNull(),
	)
	if err != nil {
		return err
	}

	for _, event := range shipmentEvents {
		if event.MovementStatusID == shipmentCfg.LinehaulScheduled {
			// 2.4
			return o.publishTiktokShipmentBags(ctx, fplShipmentEvent, shipment, tiktokParcels)
		}
	}

	log.Ctx(ctx).Info().Msg("skip-tiktok-shipment-event-publishing")

	return nil
}

func (o *InternalMMCCEventHandler) movedBagsHasTTMMCCBag(ctx context.Context, movedBagIds []uint) (bool, error) {
	movedBags, err := o.bagRepo.GetList(ctx, nil, models.BagWhere.ID.IN(movedBagIds))
	if err != nil {
		return false, err
	}

	moveBagsTid := lo.Map(movedBags, func(item *models.Bag, index int) string {
		return item.TrackingID
	})

	parcelsOfMovedBags, err := o.parcelRepo.GetListWithCtx(ctx, models.ParcelWhere.TrackingID.IN(moveBagsTid))
	if err != nil {
		return false, err
	}

	hasTTBag := false
	for _, parcel := range parcelsOfMovedBags {
		if parcel.PartnerID.Uint64 == envs.Instance.Tiktok.PartnerId &&
			parcel.Type == parcelCfg.BagB2CV2 {
			hasTTBag = true
			break
		}
	}

	return hasTTBag, nil
}
