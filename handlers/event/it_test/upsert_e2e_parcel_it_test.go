//go:build integration
// +build integration

package it_test

import (
	"context"
	"strings"
	"time"

	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/protobuf/encoding/protojson"

	"bitbucket.ninjavan.co/fpl/4pl-protos/go/fpl"
	"bitbucket.ninjavan.co/protos/proto-commons/go/common"

	"git.ninjavan.co/3pl/configs/vendor_order"
	"git.ninjavan.co/3pl/envs"
	"git.ninjavan.co/3pl/httpmodels/event"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/testutils"
	"git.ninjavan.co/3pl/testutils/db"
)

func (s *EventE2ECreator) TestIT_CreateE2EEvent_ArrivedAtSortingHub_LM() {
	const (
		e2eTrackingId = "FPLOCESTEDTID1268"
		testMetadata  = "Parcel is arrived at sorting hub"
		vendorMY      = 2
	)

	type args struct {
		ctx     context.Context
		request event.BulkCreateRequest
	}

	parcel, err := repositories.NewParcelRepository().GetOne(context.Background(), models.ParcelWhere.TrackingID.EQ(e2eTrackingId))
	if err != nil {
		s.T().Error(err)
		return
	}

	vendorStatusMyAASH, err := repositories.NewVendorStatus().GetOne(context.Background(),
		models.VendorStatusWhere.ReferenceCode.EQ(null.StringFrom(fpl.Status_name[int32(fpl.Status_ARRIVED_AT_SORTING_HUB)])),
		models.VendorStatusWhere.VendorID.EQ(vendorMY),
	)

	vendorOrders, err := repositories.NewVendorOrderRepository().GetListWithCtx(context.Background(),
		models.VendorOrderWhere.ParcelID.EQ(parcel.ID),
		qm.OrderBy(models.VendorOrderColumns.ID),
	)
	if err != nil {
		s.T().Error(err)
		return
	}

	eventTime := time.Now().Add(-24 * time.Hour).Truncate(time.Second)

	tests := struct {
		name   string
		args   args
		result func()
	}{

		name: "Fire event first mile non-completed",
		args: args{
			ctx: context.Background(),
			request: event.BulkCreateRequest{
				ParcelIDs:        []uint{parcel.ID},
				InternalStatusID: uint(fpl.Status_ARRIVED_AT_SORTING_HUB),
				VendorID:         null.UintFrom(vendorMY).Ptr(),
				VendorStatusID:   null.UintFrom(vendorStatusMyAASH.ID).Ptr(),
				EventTime:        eventTime,
				Metadata:         null.StringFrom(testMetadata).Ptr(),
				InsertNewEvent:   true,
			},
		},
		result: func() {
			// Check parcel after create order
			parcelAfterEvent, err := repositories.NewParcelRepository().GetOne(context.Background(), models.ParcelWhere.TrackingID.EQ(e2eTrackingId))
			if err != nil {
				s.T().Errorf(err.Error())
				return
			}
			if !testutils.CompareNonEmptyFields(parcelAfterEvent, &models.Parcel{
				TrackingID:              e2eTrackingId,
				CurrentVendorID:         null.UintFrom(vendorMY),
				CurrentInternalStatusID: null.UintFrom(uint(fpl.Status_ARRIVED_AT_SORTING_HUB)),
			}) || parcelAfterEvent.CurrentEventTime.Time.UTC().Format("2006-01-02 15:04:05") != eventTime.UTC().Format("2006-01-02 15:04:05") {
				s.T().Errorf("Expected parcel to be created is %v", parcelAfterEvent)
				return
			}

			// check for event
			events, err := repositories.NewEventRepository().GetLatestEvents(context.Background(), []uint{parcel.ID})
			if err != nil || len(events) == 0 {
				s.T().Errorf(err.Error())
				return
			}
			if !testutils.CompareNonEmptyFields(events[0], &models.Event{
				ParcelID:         parcel.ID,
				InternalStatusID: uint(fpl.Status_ARRIVED_AT_SORTING_HUB),
				VendorID:         uint(vendorMY),
				VendorStatusID:   null.UintFrom(vendorStatusMyAASH.ID),
				Metadata:         null.StringFrom(testMetadata),
			}) || events[0].EventTime.UTC().Format("2006-01-02 15:04:05") != eventTime.UTC().Format("2006-01-02 15:04:05") {
				s.T().Errorf("Expected event to be created is %v", events[0])
				return
			}

			// check for vendor order
			if !testutils.CompareNonEmptyFields(vendorOrders[0], &models.VendorOrder{
				ParcelID:         parcel.ID,
				VendorID:         uint(1),
				VendorTrackingID: null.StringFrom(e2eTrackingId),
				Country:          null.StringFrom("sg"),
				Status:           vendor_order.StatusSucceeded,
			}) {
				s.T().Errorf("Expected vendor order sg status to be success")
				return
			}
			if !testutils.CompareNonEmptyFields(vendorOrders[1], &models.VendorOrder{
				ParcelID:         parcel.ID,
				VendorID:         uint(vendorMY),
				VendorTrackingID: null.StringFrom(e2eTrackingId),
				Country:          null.StringFrom("my"),
				Status:           vendor_order.StatusSucceeded,
			}) {
				s.T().Errorf("Expected vendor order my status to be success")
				return
			}

			messages, err := db.NewConsumedMessageMySQL().GetByTopic(context.Background(), envs.Instance.KafkaTopics.FplParcelEventTopic)
			if err != nil {
				s.T().Errorf(err.Error())
				return
			}

			messagesWithTid := lo.Filter(messages, func(message *db.ConsumedMessage, _ int) bool {
				return strings.Contains(message.Message, e2eTrackingId)
			})

			if len(messagesWithTid) != 1 {
				s.T().Errorf("Expected 1 message to be consumed, but got %d", len(messages))
				return
			}

			messageStruct := &fpl.FPLParcelEvent{}
			err = protojson.Unmarshal([]byte(messagesWithTid[0].Message), messageStruct)
			if err != nil {
				s.T().Errorf(err.Error())
				return
			}

			if !CompareFPLParcelEvents(messageStruct, &fpl.FPLParcelEvent{
				Metadata: &common.Metadata{
					SystemId: "global",
				},
				Partner: &fpl.Partner{
					Id:   parcel.PartnerID.Uint64,
					Code: "4PL_OC_EXTERNAL",
				},
				TrackingId: e2eTrackingId,
				Event: &fpl.FPLEvent{
					HappenedAt:             events[0].EventTime.Unix(),
					StatusCode:             fpl.Status_name[int32(fpl.Status_ARRIVED_AT_SORTING_HUB)],
					Note:                   testMetadata,
					VendorCode:             "NVMY",
					Metadata:               testMetadata,
					ShipperSubmittedWeight: 12000,
					ActualWeight:           12000,
					Stage:                  fpl.FPLEvent_EXPORT,
					Status:                 fpl.Status_ARRIVED_AT_SORTING_HUB,
					TplVendor:              fpl.TplVendor_NINJAVAN,
				},
				ShipperId:       int64(5599715),
				LegacyShipperId: int64(946046),
				SourceOrderId:   parcel.SourceOrderID.String,
			}) {
				s.T().Errorf("Expected message to be consumed is %v", messageStruct)
				return
			}

			return

		},
	}

	err = s.usecase.Create(tests.args.ctx, tests.args.request)
	if err != nil {
		s.T().Error(err)
		return
	}

	time.Sleep(3 * time.Second)

	tests.result()

}

func CompareFPLParcelEvents(event1, event2 *fpl.FPLParcelEvent) bool {
	if event1 == nil && event2 == nil {
		return true
	}
	if event1 == nil || event2 == nil {
		return false
	}

	// Compare top-level fields
	if event1.TrackingId != event2.TrackingId ||
		event1.ShipperId != event2.ShipperId ||
		event1.LegacyShipperId != event2.LegacyShipperId ||
		event1.SourceOrderId != event2.SourceOrderId {
		return false
	}

	// Compare Partner
	if !comparePartner(event1.Partner, event2.Partner) {
		return false
	}

	// Compare Metadata
	if !compareMetadata(event1.Metadata, event2.Metadata) {
		return false
	}

	// Compare Event
	if !compareFPLEvent(event1.Event, event2.Event) {
		return false
	}

	if !compareBag(event1.Bag, event2.Bag) {
		return false
	}

	return true
}

func compareBag(b1, b2 *fpl.Bag) bool {
	if b1 == nil && b2 == nil {
		return true
	}
	if b1 == nil || b2 == nil {
		return false
	}

	return b1.GetBigBagNo() == b2.GetBigBagNo()
}

// comparePartner compares Partner fields
func comparePartner(p1, p2 *fpl.Partner) bool {
	if p1 == nil && p2 == nil {
		return true
	}
	if p1 == nil || p2 == nil {
		return false
	}
	return p1.Id == p2.Id && p1.Code == p2.Code
}

// compareMetadata compares Metadata fields
func compareMetadata(m1, m2 *common.Metadata) bool {
	if m1 == nil && m2 == nil {
		return true
	}
	if m1 == nil || m2 == nil {
		return false
	}
	return m1.SystemId == m2.SystemId
}

// compareFPLEvent compares FPLEvent fields
func compareFPLEvent(e1, e2 *fpl.FPLEvent) bool {
	if e1 == nil && e2 == nil {
		return true
	}
	if e1 == nil || e2 == nil {
		return false
	}

	if !compareLinehaulInformation(e1.LinehaulInformation, e2.LinehaulInformation) {
		return false
	}

	return e1.HappenedAt == e2.HappenedAt &&
		e1.StatusCode == e2.StatusCode &&
		e1.Note == e2.Note &&
		e1.VendorCode == e2.VendorCode &&
		e1.ShipperSubmittedWeight == e2.ShipperSubmittedWeight &&
		e1.ActualWeight == e2.ActualWeight &&
		e1.Stage == e2.Stage &&
		e1.Status == e2.Status &&
		e1.TplVendor == e2.TplVendor &&
		e1.Metadata == e2.Metadata &&
		e1.OperateLocation == e2.OperateLocation &&
		e1.OperateLocationCode == e2.OperateLocationCode
}

// compareLinehaulInformation compares fpl.LinehaulInformation fields
func compareLinehaulInformation(l1, l2 *fpl.LinehaulInformation) bool {
	if l1 == nil && l2 == nil {
		return true
	}
	if l1 == nil || l2 == nil {
		return false
	}

	return l1.TransportType == l2.TransportType &&
		l1.MAWB == l2.MAWB &&
		l1.OriginPort == l2.OriginPort &&
		l1.OriginCountry == l2.OriginCountry &&
		l1.DestinationPort == l2.DestinationPort &&
		l1.DestinationCountry == l2.DestinationCountry &&
		l1.Etd == l2.Etd &&
		l1.Eta == l2.Eta &&
		l1.VesselNo == l2.VesselNo

}
