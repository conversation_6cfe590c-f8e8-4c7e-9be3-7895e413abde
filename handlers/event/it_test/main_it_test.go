//go:build integration
// +build integration

package it_test

import (
	"os"
	"testing"

	"github.com/stretchr/testify/suite"

	eventController "git.ninjavan.co/3pl/controllers/event"
	"git.ninjavan.co/3pl/handlers/event"
	"git.ninjavan.co/3pl/testutils"
)

type EventE2ECreator struct {
	suite.Suite
	usecase eventController.BulkEventHandler
}

func TestEventE2ECreator(t *testing.T) {
	suite.Run(t, new(EventE2ECreator))
}

func (s *EventE2ECreator) SetupSuite() {
	s.usecase = event.NewBulkCreator()
}

type EventMMCCStandardCreator struct {
	suite.Suite
	usecase eventController.BulkEventHandler
}

func TestEventMMCCStandardCreator(t *testing.T) {
	suite.Run(t, new(EventMMCCStandardCreator))
}

func (s *EventMMCCStandardCreator) SetupSuite() {
	s.usecase = event.NewBulkCreator()
}

type EventMMCCTiktokCreator struct {
	suite.Suite
	usecase eventController.BulkEventHandler
}

func TestEventMMCCTiktokCreator(t *testing.T) {
	suite.Run(t, new(EventMMCCTiktokCreator))
}

func (s *EventMMCCTiktokCreator) SetupSuite() {
	s.usecase = event.NewBulkCreator()
}

func TestMain(m *testing.M) {
	suite := testutils.BaseIntegration{}
	suite.SetupSuite()
	code := m.Run()
	suite.TearDownSuite() // if we don't terminate containers then we can reuse them again for faster executing time
	os.Exit(code)
}
