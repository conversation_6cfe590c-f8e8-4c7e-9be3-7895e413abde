//go:build integration
// +build integration

package it_test

import (
	"context"
	"strings"
	"time"

	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"google.golang.org/protobuf/encoding/protojson"

	"bitbucket.ninjavan.co/fpl/4pl-protos/go/fpl"
	"bitbucket.ninjavan.co/protos/proto-commons/go/common"

	"git.ninjavan.co/3pl/envs"
	"git.ninjavan.co/3pl/handlers/event/event_interface"
	"git.ninjavan.co/3pl/httpmodels/event"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/testutils"
	"git.ninjavan.co/3pl/testutils/db"
)

func (s *EventMMCCStandardCreator) TestIT_CreateMMCCEvent_LD() {
	const (
		mmccBagTid    = "FPLV2OKYTT5X5RY5CSJJ"
		mmccParcelTid = "FPLDUOIQQ0BYVYO7PQZRHHYF"
		testMetadata  = "Parcel is linehaul departed"
		vendorMMCC    = 7
	)

	type args struct {
		ctx     context.Context
		request event.BulkCreateRequest
	}

	bag, err := repositories.NewParcelRepository().GetOne(context.Background(), models.ParcelWhere.TrackingID.EQ(mmccBagTid))
	if err != nil {
		s.T().Error(err)
		return
	}

	parcel, err := repositories.NewParcelRepository().GetOne(context.Background(), models.ParcelWhere.TrackingID.EQ(mmccParcelTid))
	if err != nil {
		s.T().Error(err)
		return
	}

	if err != nil {
		s.T().Error(err)
		return
	}

	eventTime := time.Now().Add(-24 * time.Hour).Truncate(time.Second)

	tests := struct {
		name   string
		args   args
		result func()
	}{

		name: "Fire event mmcc linehaul departed",
		args: args{
			ctx: context.Background(),
			request: event.BulkCreateRequest{
				ParcelIDs:        []uint{bag.ID},
				InternalStatusID: uint(fpl.Status_LINEHAUL_DEPARTED),
				VendorID:         null.UintFrom(vendorMMCC).Ptr(),
				EventTime:        eventTime,
				Metadata:         null.StringFrom(testMetadata).Ptr(),
				InsertNewEvent:   true,
				ExtraData: event_interface.ExtraData{
					OperateLocation: &event_interface.OperateLocation{
						Location: "SGKCH",
						Code:     "SGKCH",
						Country:  "SG",
						Lat:      null.Float64From(1.3521).Ptr(),
						Long:     null.Float64From(103.8198).Ptr(),
					},
				},
			},
		},
		result: func() {
			// Check bag after create order
			bagAfterEvent, err := repositories.NewParcelRepository().GetOne(context.Background(), models.ParcelWhere.TrackingID.EQ(mmccBagTid))
			if err != nil {
				s.T().Errorf(err.Error())
				return
			}
			if !testutils.CompareNonEmptyFields(bagAfterEvent, &models.Parcel{
				TrackingID:              mmccBagTid,
				CurrentVendorID:         null.UintFrom(vendorMMCC),
				CurrentInternalStatusID: null.UintFrom(uint(fpl.Status_LINEHAUL_DEPARTED)),
			}) || bagAfterEvent.CurrentEventTime.Time.UTC().Format("2006-01-02 15:04:05") != eventTime.UTC().Format("2006-01-02 15:04:05") {
				s.T().Errorf("Expected parcel to be created is %v", bagAfterEvent)
				return
			}

			// check for bag event
			bagEvents, err := repositories.NewEventRepository().GetLatestEvents(context.Background(), []uint{bag.ID})
			if err != nil || len(bagEvents) == 0 {
				s.T().Errorf(err.Error())
				return
			}
			if !testutils.CompareNonEmptyFields(bagEvents[0], &models.Event{
				ParcelID:         bag.ID,
				InternalStatusID: uint(fpl.Status_LINEHAUL_DEPARTED),
				VendorID:         uint(vendorMMCC),
				Metadata:         null.StringFrom(testMetadata),
			}) || bagEvents[0].EventTime.UTC().Format("2006-01-02 15:04:05") != eventTime.UTC().Format("2006-01-02 15:04:05") {
				s.T().Errorf("Expected event to be created is %v", bagEvents[0])
				return
			}

			messages, err := db.NewConsumedMessageMySQL().GetByTopic(context.Background(), envs.Instance.KafkaTopics.FplBagEventTopic)
			if err != nil {
				s.T().Errorf(err.Error())
				return
			}

			messagesWithTid := lo.Filter(messages, func(message *db.ConsumedMessage, _ int) bool {
				return strings.Contains(message.Message, mmccBagTid)
			})

			if len(messagesWithTid) != 1 {
				s.T().Errorf("Expected 1 message to be consumed, but got %d", len(messages))
				return
			}

			messageStruct := &fpl.FPLBagEvent{}
			err = protojson.Unmarshal([]byte(messagesWithTid[0].Message), messageStruct)
			if err != nil {
				s.T().Errorf(err.Error())
				return
			}

			if !CompareFPLBagEvents(messageStruct, &fpl.FPLBagEvent{
				Metadata: &common.Metadata{
					SystemId: "global",
				},
				Partner: &fpl.Partner{
					Id:   bag.PartnerID.Uint64,
					Code: "4PL_OC_EXTERNAL",
				},
				TrackingId: mmccBagTid,
				Event: &fpl.FPLEvent{
					HappenedAt:          bagEvents[0].EventTime.Unix(),
					StatusCode:          fpl.Status_name[int32(fpl.Status_LINEHAUL_DEPARTED)],
					Metadata:            testMetadata,
					OperateLocation:     "SGKCH",
					OperateLocationCode: "SGKCH",
					OperatedAt: &fpl.OperateLocation{
						Location: "SGKCH",
						Code:     "SGKCH",
						Country:  "SG",
					},
					LinehaulInformation: &fpl.LinehaulInformation{
						TransportType:      "SEA",
						MAWB:               "828-17111234",
						OriginPort:         "SIN",
						OriginCountry:      "SG",
						DestinationPort:    "MYY",
						DestinationCountry: "MY",
						Etd:                1748575800,
						Eta:                1748662200,
						VesselNo:           "123456",
					},
					Stage:  fpl.FPLEvent_EXPORT,
					Status: fpl.Status_LINEHAUL_DEPARTED,
				},
				ShipperId:     int64(5599715),
				SourceOrderId: bag.SourceOrderID.String,
			}) {
				s.T().Errorf("Expected message to be consumed is %v", messageStruct)
				return
			}

			// Check parcel after create order
			parcelAfterEvent, err := repositories.NewParcelRepository().GetOne(context.Background(), models.ParcelWhere.TrackingID.EQ(mmccParcelTid))
			if err != nil {
				s.T().Errorf(err.Error())
				return
			}
			if !testutils.CompareNonEmptyFields(parcelAfterEvent, &models.Parcel{
				TrackingID:              mmccParcelTid,
				CurrentVendorID:         null.UintFrom(vendorMMCC),
				CurrentInternalStatusID: null.UintFrom(uint(fpl.Status_LINEHAUL_DEPARTED)),
			}) || bagAfterEvent.CurrentEventTime.Time.UTC().Format("2006-01-02 15:04:05") != eventTime.UTC().Format("2006-01-02 15:04:05") {
				s.T().Errorf("Expected parcel to be created is %v", bagAfterEvent)
				return
			}

			// check for bag event
			parcelEvents, err := repositories.NewEventRepository().GetLatestEvents(context.Background(), []uint{parcel.ID})
			if err != nil || len(parcelEvents) == 0 {
				s.T().Errorf(err.Error())
				return
			}
			if !testutils.CompareNonEmptyFields(parcelEvents[0], &models.Event{
				ParcelID:         parcel.ID,
				InternalStatusID: uint(fpl.Status_LINEHAUL_DEPARTED),
				VendorID:         uint(vendorMMCC),
				Metadata:         null.StringFrom(testMetadata),
			}) || parcelEvents[0].EventTime.UTC().Format("2006-01-02 15:04:05") != eventTime.UTC().Format("2006-01-02 15:04:05") {
				s.T().Errorf("Expected event to be created is %v", parcelEvents[0])
				return
			}

			messages, err = db.NewConsumedMessageMySQL().GetByTopic(context.Background(), envs.Instance.KafkaTopics.FplMmccParcelEventTopic)
			if err != nil {
				s.T().Errorf(err.Error())
				return
			}

			messagesWithTid = lo.Filter(messages, func(message *db.ConsumedMessage, _ int) bool {
				return strings.Contains(message.Message, mmccParcelTid)
			})

			if len(messagesWithTid) != 1 {
				s.T().Errorf("Expected 1 message to be consumed, but got %d", len(messages))
				return
			}

			parcelMessageStruct := &fpl.FPLParcelEvent{}
			err = protojson.Unmarshal([]byte(messagesWithTid[0].Message), parcelMessageStruct)
			if err != nil {
				s.T().Errorf(err.Error())
				return
			}

			if !CompareFPLParcelEvents(parcelMessageStruct, &fpl.FPLParcelEvent{
				Metadata: &common.Metadata{
					SystemId: "global",
				},
				Partner: &fpl.Partner{
					Id:   bag.PartnerID.Uint64,
					Code: "4PL_OC_EXTERNAL",
				},
				TrackingId: mmccParcelTid,
				Event: &fpl.FPLEvent{
					HappenedAt:          parcelEvents[0].EventTime.Unix(),
					StatusCode:          fpl.Status_name[int32(fpl.Status_LINEHAUL_DEPARTED)],
					Metadata:            testMetadata,
					OperateLocation:     "SGKCH",
					OperateLocationCode: "SGKCH",
					OperatedAt: &fpl.OperateLocation{
						Location: "SGKCH",
						Code:     "SGKCH",
						Country:  "SG",
					},
					LinehaulInformation: &fpl.LinehaulInformation{
						MAWB: "828-17111234",
					},
					Stage:  fpl.FPLEvent_EXPORT,
					Status: fpl.Status_LINEHAUL_DEPARTED,
				},
				Bag: &fpl.Bag{
					BigBagNo: bag.SourceOrderID.String,
				},
				ShipperId: int64(5599715),
			}) {
				s.T().Errorf("Expected message to be consumed is %v", parcelMessageStruct)
				return
			}

			return

		},
	}

	err = s.usecase.Create(tests.args.ctx, tests.args.request)
	if err != nil {
		s.T().Error(err)
		return
	}

	time.Sleep(3 * time.Second)

	tests.result()

}

func CompareFPLBagEvents(event1, event2 *fpl.FPLBagEvent) bool {
	if event1 == nil && event2 == nil {
		return true
	}
	if event1 == nil || event2 == nil {
		return false
	}

	// Compare top-level fields
	if event1.TrackingId != event2.TrackingId ||
		event1.ShipperId != event2.ShipperId ||
		event1.SourceOrderId != event2.SourceOrderId {
		return false
	}

	// Compare Partner
	if !comparePartner(event1.Partner, event2.Partner) {
		return false
	}

	// Compare Metadata
	if !compareMetadata(event1.Metadata, event2.Metadata) {
		return false
	}

	// Compare Event
	if !compareFPLEvent(event1.Event, event2.Event) {
		return false
	}

	return true
}
