package order_creation

import (
	"context"
	"database/sql"
	"encoding/gob"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"slices"
	"strings"

	"github.com/rs/zerolog/log"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	systemid2 "bitbucket.ninjavan.co/cg/base-commons---go/systemid"
	"bitbucket.ninjavan.co/cg/datadog-agent---go/nvtracer"
	"bitbucket.ninjavan.co/fpl/4pl-protos/go/fpl"
	"bitbucket.ninjavan.co/protos/proto-commons/go/common"

	"git.ninjavan.co/3pl/configs"
	orderConst "git.ninjavan.co/3pl/configs/orders"
	parcelConst "git.ninjavan.co/3pl/configs/parcel"
	serviceConst "git.ninjavan.co/3pl/configs/services"
	shipperConst "git.ninjavan.co/3pl/configs/shipper"
	vendorOrderConst "git.ninjavan.co/3pl/configs/vendor_order"
	vendorRequestConst "git.ninjavan.co/3pl/configs/vendor_request"
	vendorConst "git.ninjavan.co/3pl/configs/vendors"
	"git.ninjavan.co/3pl/envs"
	"git.ninjavan.co/3pl/errors/fplerror"
	"git.ninjavan.co/3pl/event_publisher"
	"git.ninjavan.co/3pl/event_publisher/event_publisher_interface"
	"git.ninjavan.co/3pl/handlers/async_task"
	"git.ninjavan.co/3pl/handlers/integration/base"
	"git.ninjavan.co/3pl/handlers/integration/nvcore"
	orderHandler "git.ninjavan.co/3pl/handlers/order"
	"git.ninjavan.co/3pl/handlers/order/order_interface"
	"git.ninjavan.co/3pl/handlers/order_creation/order_creation_interface"
	parcel2 "git.ninjavan.co/3pl/handlers/parcel_handler"
	service_interface "git.ninjavan.co/3pl/handlers/services/service_interface"
	shipmentHandler "git.ninjavan.co/3pl/handlers/shipment"
	update_interface "git.ninjavan.co/3pl/handlers/shipment/shipment_interface"
	"git.ninjavan.co/3pl/handlers/shipment_parcel/create"
	"git.ninjavan.co/3pl/httpmodels"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/services/tplvendors/acs"
	"git.ninjavan.co/3pl/services/tplvendors/yun"
	"git.ninjavan.co/3pl/testdata"
	"git.ninjavan.co/3pl/utils"
	internalcontext "git.ninjavan.co/3pl/utils/ctxutils"
	"git.ninjavan.co/3pl/utils/loggerutils"
)

type orderCreator struct {
	partnerSettingRepo repo_interface.PartnerSettingRepository

	storeManager               order_creation_interface.DbAccessor
	shipperGetter              order_creation_interface.ShipperFinder
	trackingIDGenerator        order_creation_interface.TrackingIDGenerator
	orderDeleter               order_interface.Deleter
	parcelRepo                 repo_interface.ParcelRepository
	parcelItemRepo             repo_interface.ParcelItemRepository
	parcelCreationEventCreator async_task.AsyncTaskCreator

	productRepo       repo_interface.ProductRepository
	productVendorRepo repo_interface.ProductVendorRepository
	serviceRepo       repo_interface.ServiceRepository
	shipperRepo       repo_interface.ShipperRepositoryInterface
	eventPublisher    event_publisher_interface.EventPublisher

	shipmentParcelRepo    repo_interface.ShipmentParcelRepository
	noOfItemsUpdater      update_interface.NoOfItemsUpdatable
	vendorRequestRepo     repo_interface.VendorRequestRepoInterface
	shipmentParcelHandler create.Handler
}

const creatorModuleName = "order-creator"

func NewOrderCreator() *orderCreator {
	return &orderCreator{
		partnerSettingRepo:         repositories.NewPartnerSetting(),
		storeManager:               newDbAccessor(),
		shipperGetter:              newShipperGetter(),
		trackingIDGenerator:        GetTrackingIDByRandomStrGenerator(),
		orderDeleter:               orderHandler.NewOrderDeleter(),
		parcelRepo:                 repositories.NewParcelRepository(),
		parcelItemRepo:             repositories.NewParcelItemRepository(),
		parcelCreationEventCreator: async_task.NewFPLParcelCreationCreator(),

		productRepo:       repositories.NewProduct(),
		productVendorRepo: repositories.NewProductVendor(),
		serviceRepo:       repositories.NewService(),
		shipperRepo:       repositories.NewShipperRepository(),
		eventPublisher:    event_publisher.NewEventPublisher(),

		shipmentParcelRepo:    repositories.NewShipmentParcelRepository(),
		noOfItemsUpdater:      shipmentHandler.GetNoOfItemsUpdater(),
		vendorRequestRepo:     repositories.NewVendorRequest(),
		shipmentParcelHandler: create.NewHandler(),
	}
}

const (
	UndefinedServiceType   order_creation_interface.IntegratedServiceType = iota // 0
	OrderCreateServiceType                                                       // 1
	OrderTrackServiceType                                                        // 2
)

type OCConfig struct {
	Services                  []uint `json:"services"`
	IsUsedConsolidatedShipper bool   `json:"is_used_consolidated_shipper"`
}

func init() {
	gob.Register(order_creation_interface.CreateMMCCParcelPayload{})
}

func (c *orderCreator) CreateE2EOrder(
	ctx context.Context, req *order.BaseRequest, partner *models.Partner,
) (*models.Parcel, error) {
	res, _, err := c.createOrder(ctx, req, partner, nil)
	return res, err
}

func (c *orderCreator) CreateE2EOrderDeprecated(
	ctx context.Context, req *order.BaseRequest, partner *models.Partner,
) (parcel *models.Parcel, duplicated bool, err error) {
	return c.createOrder(ctx, req, partner, nil)
}

func (c *orderCreator) CreateMMCCOrder(
	ctx context.Context, payload *order_creation_interface.MMCCOCPayload, partner *models.Partner, txn boil.ContextTransactor,
) (*models.Parcel, error) {
	var err error
	if txn == nil {
		txn, err = repositories.BeginTransaction(ctx)
		if err != nil {
			return nil, err
		}
		defer func() {
			err = repositories.ProcessTransaction(txn, err)
			if err != nil {
				loggerutils.Ctx(ctx, "mmcc-order-creator").Err(err).Msg("fail to process txn")
			}
		}()
	}
	bag, _, err := c.createOrder(ctx, payload.BagRequest, partner, txn)
	if err != nil {
		return nil, fmt.Errorf("create bag error: %w", err)
	}

	itemsByParcelTid := map[string]models.ParcelItemSlice{}
	parcels := lo.Map(payload.MMCCParcels, func(item *order_creation_interface.MMCCParcel, index int) *models.Parcel {
		item.Parcel.Type = parcelConst.MMCCParcel
		if item.Parcel.TrackingID == "" {
			item.Parcel.TrackingID = strings.ToUpper(c.trackingIDGenerator.Generate(ctx, parcelPrefix, defaultTIDLen))
		}
		populateParcelWithBagInfo(bag, item.Parcel)
		itemsByParcelTid[item.Parcel.TrackingID] = item.ParcelItems
		return item.Parcel
	})
	// might consider not IGNORE for other cases
	if err = c.parcelRepo.BulkCreateIgnoreTxn(ctx, parcels, txn); err != nil {
		return nil, fmt.Errorf("bulk create parcels error: %w", err)
	}

	tids := lo.Map(parcels, func(item *models.Parcel, index int) string {
		return item.TrackingID
	})
	parcels, err = c.parcelRepo.GetListWithCtxTxn(ctx,
		txn,
		models.ParcelWhere.PartnerID.EQ(null.Uint64From(bag.PartnerID.Uint64)),
		models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bag.SourceOrderID.String)),
		models.ParcelWhere.Type.EQ(parcelConst.MMCCParcel),
		models.ParcelWhere.TrackingID.IN(tids),
	)
	if err != nil {
		return nil, err
	}
	parcelByTid := lo.Associate(parcels, func(item *models.Parcel) (string, *models.Parcel) {
		return item.TrackingID, item
	})

	shipper, err := c.shipperRepo.GetOne(ctx, models.ShipperWhere.ID.EQ(bag.ShipperID.Uint))
	if err != nil {
		return nil, fmt.Errorf("get shipper failed: %w", err)
	}
	var items models.ParcelItemSlice
	for _, e := range payload.MMCCParcels {
		for _, item := range e.ParcelItems {
			fillTaxesToParcelItem(item, shipper, e.Parcel.ToCountryCode.String)
			item.ParcelID = uint64(parcelByTid[e.Parcel.TrackingID].ID)
		}
		items = append(items, e.ParcelItems...)
	}
	// dont have a mechanism to dedup items yet
	if err = c.parcelItemRepo.BulkCreateWithTxn(ctx, items, txn); err != nil {
		return nil, fmt.Errorf("bulk create parcel items error: %w", err)
	}

	c.createTranslateDescriptionTasks(ctx, parcels)
	return bag, nil
}

func (c *orderCreator) CreateMMCCParcel(ctx context.Context, payload *order_creation_interface.CreateMMCCParcelPayload) error {
	parcels, err := c.createMMCCParcelsAndItems(ctx, []*order_creation_interface.CreateMMCCParcelPayload{payload})
	if err != nil {
		switch {
		case utils.DBError(err).IsDuplicatedEntry() && strings.Contains(err.Error(), "uidx_parcels_partner_unique_key_partner_id_type"):
			if !payload.MoveBagOnDuplicated {
				return err
			}
			return c.moveMMCCBag(ctx, payload)
		case utils.DBError(err).IsDuplicatedEntry() && strings.Contains(err.Error(), "unique_tracking_id"):
			return fmt.Errorf("insert parcel error: %w", err)
		default:
			return fmt.Errorf("insert parcel error: %w, %w", err, fplerror.NonRetryableErr)
		}
	}

	if payload.Bag != nil {
		if err = c.shipmentParcelHandler.UpsertMMCCParcels(
			ctx,
			payload.Bag.TrackingID,
			[]string{parcels[0].TrackingID},
		); err != nil {
			return err
		}
	}

	c.createTranslateDescriptionTasks(ctx, models.ParcelSlice{parcels[0]})
	return nil
}

func (c *orderCreator) moveMMCCBag(ctx context.Context, payload *order_creation_interface.CreateMMCCParcelPayload) error {
	p, err := c.parcelRepo.GetOne(ctx, []qm.QueryMod{
		models.ParcelWhere.RefTrackingID.EQ(payload.MMCCParcel.Parcel.RefTrackingID),
		models.ParcelWhere.PartnerID.EQ(payload.MMCCParcel.Parcel.PartnerID),
	}...)
	if err != nil {
		return err
	}

	bag, err := c.parcelRepo.GetOneByIdentifier(ctx, repositories.NewMmccBagIdentifier(p.PartnerID.Uint64, p.SourceOrderID.String))
	if err != nil {
		return err
	}
	if bag == nil {
		return fmt.Errorf("bag not found")
	}

	p.SourceOrderID = payload.Bag.SourceOrderID
	p.ParentID = null.UintFrom(payload.Bag.ID)
	md := repo_interface.ParcelMetadata{}
	_ = json.Unmarshal([]byte(p.Metadata.String), &md)
	md.BagMovement = append(md.BagMovement, bag.TrackingID)
	p.Metadata = null.StringFrom(utils.JsonMarshalStrIgnoreError(md))

	return c.parcelRepo.UpdateColumns(ctx, nil, p, boil.Whitelist(models.ParcelColumns.SourceOrderID, models.ParcelColumns.ParentID, models.ParcelColumns.Metadata))
}

func (c *orderCreator) CreateCoreB2BBundle(
	ctx context.Context, req *order.BaseRequest, partner *models.Partner,
) (*order_creation_interface.CoreB2BBundleOCResult, error) {
	ocConfig, config, err := c.loadOCConfig(ctx, req, partner)
	if err != nil {
		return nil, err
	}

	if ocConfig.Service.Type != serviceConst.TypeB2BBundle.NullUint8() {
		return nil, fplerror.ErrBadRequest.NewWithoutStack("invalid service type: %d. it needs to be B2B Bundle", ocConfig.Service.Type.Uint8)
	}

	err = c.prepareShippers(ctx, req, ocConfig, config)
	if err != nil {
		return nil, err
	}

	var productVendor *repo_interface.ProductVendorWrapper
	for _, pv := range ocConfig.ProductVendors {
		if pv.Vendor.Handler == vendorConst.VendorHandlerNVCore {
			productVendor = pv
			break
		}
	}

	if productVendor == nil {
		return nil, fmt.Errorf("NV Core vendor is not found")
	}

	stage := productVendor.ProductVendor.Stage.String
	shipper := c.orderableShipper(ctx, stage, req, ocConfig)
	if shipper == nil {
		return nil, fmt.Errorf("can't find valid shipper")
	}

	creator, err := nvcore.New(nvcore.OrderInitParams{
		Request:              req,
		Shipper:              shipper,
		ShipperPlatform:      ocConfig.ShipperPlatform,
		ShipperDetectionType: config.ShipperDetectionType,
		IsConsolidateFlow:    ocConfig.IsNeededConsolidatedShipper(),
		ServiceType:          serviceConst.Type(ocConfig.Service.Type.Uint8),
		Stage:                stage,
		OrderFlow:            ocConfig.OrderFlow,
		SystemID:             strings.ToLower(productVendor.Vendor.Country),
	})
	if err != nil {
		return nil, err
	}

	responder, request, err := creator.Create(ctx, shipper.Country.String)
	vendorRequest := &models.VendorRequest{
		VendorID:      productVendor.Vendor.ID,
		RequestName:   fmt.Sprintf("OC request for %s", productVendor.Vendor.Name),
		Status:        vendorRequestConst.GetStatusFromErr(err),
		ShipperID:     null.UintFrom(shipper.ID),
		RequestBody:   null.NewString(request.GetBody(), request.GetBody() != ""),
		RequestHeader: null.NewString(request.GetHeader(), request.GetHeader() != ""),
	}
	defer func() {
		if err = c.vendorRequestRepo.Create(ctx, vendorRequest); err != nil {
			log.Ctx(ctx).Err(err).Msg("failed to create vendor request")
		}
	}()
	if err != nil {
		vendorRequest.PlainError = null.StringFrom(err.Error())
		if nvcore.IsDuplicated(err) {
			return nil, fmt.Errorf("%w: %w: %v", fplerror.ErrOrderDuplicated, fplerror.NonRetryableErr, err)
		}
		return nil, err
	}
	vendorRequest.ResponseBody = null.NewString(responder.GetBody(), responder.GetBody() != "")
	vendorRequest.ResponseHeader = null.NewString(responder.GetHeader(), responder.GetHeader() != "")
	vendorRequest.TrackingID = responder.GetVendorTrackingID()

	coreResp := responder.(*nvcore.OrderResponse)
	return &order_creation_interface.CoreB2BBundleOCResult{
		TrackingID: responder.GetVendorTrackingID(),
		PieceTrackingIDs: lo.Map(coreResp.BundleInformation.Pieces, func(item nvcore.PieceResponse, index int) string {
			return item.TrackingNumber
		}),
	}, nil
}

func (c *orderCreator) CreateMMCCParcels(ctx context.Context, payloads []*order_creation_interface.CreateMMCCParcelPayload) (err error) {
	parcels, err := c.createMMCCParcelsAndItems(ctx, payloads)
	if err != nil {
		return err
	}

	payloadsByBagTids := lo.GroupBy(payloads, func(item *order_creation_interface.CreateMMCCParcelPayload) string {
		return item.Bag.TrackingID
	})
	for bagTid, ps := range payloadsByBagTids {
		parcelTids := lo.Map(ps, func(item *order_creation_interface.CreateMMCCParcelPayload, index int) string {
			return item.MMCCParcel.Parcel.TrackingID
		})
		if err = c.shipmentParcelHandler.UpsertMMCCParcels(ctx, bagTid, parcelTids); err != nil {
			return err
		}
	}

	c.createTranslateDescriptionTasks(ctx, parcels)

	return nil
}

func (c *orderCreator) createMMCCParcelsAndItems(
	ctx context.Context, payloads []*order_creation_interface.CreateMMCCParcelPayload,
) (res models.ParcelSlice, err error) {
	txn, err := repositories.BeginTransaction(ctx)
	if err != nil {
		return nil, err
	}
	defer func() {
		repositories.ProcessTransaction(txn, err)
	}()

	parcels := lo.Map(payloads, func(payload *order_creation_interface.CreateMMCCParcelPayload, index int) *models.Parcel {
		parcel := payload.MMCCParcel.Parcel
		if parcel.TrackingID == "" {
			parcel.TrackingID = generateFPLTrackingId(ctx, c.parcelRepo, txn)
		}
		parcel.Type = parcelConst.MMCCParcel
		if payload.Bag != nil {
			populateParcelWithBagInfo(payload.Bag, parcel)
		}
		return parcel
	})
	if err = c.parcelRepo.BulkCreateTxn(ctx, parcels, txn); err != nil {
		return nil, err
	}

	// query again to get the list of parcels with ID
	tids := lo.Map(parcels, func(item *models.Parcel, index int) string {
		return item.TrackingID
	})
	parcels, err = c.parcelRepo.GetListWithCtxTxn(ctx, txn, models.ParcelWhere.TrackingID.IN(tids))
	if err != nil {
		return nil, err
	}

	parcelItemsByTrackingId := lo.Associate(payloads, func(item *order_creation_interface.CreateMMCCParcelPayload) (string, models.ParcelItemSlice) {
		return item.MMCCParcel.Parcel.TrackingID, item.MMCCParcel.ParcelItems
	})
	for _, parcel := range parcels {
		for _, item := range parcelItemsByTrackingId[parcel.TrackingID] {
			item.ParcelID = uint64(parcel.ID)
		}
	}

	parcelItems := lo.FlatMap(lo.Values(parcelItemsByTrackingId), func(items models.ParcelItemSlice, index int) []*models.ParcelItem {
		return items
	})
	if err = c.parcelItemRepo.BulkCreateWithTxn(ctx, parcelItems, txn); err != nil {
		return nil, fmt.Errorf("bulk create parcel items error: %w", err)
	}
	return parcels, nil
}

// nullableTxn can be nil, in which case the sub methods will start its own txn.
func (c *orderCreator) createOrder(
	ctx context.Context, req *order.BaseRequest, partner *models.Partner, nullableTxn boil.ContextTransactor,
) (parcel *models.Parcel, duplicated bool, err error) {
	ctx, span := nvtracer.CreateSpanFromContext(ctx)
	defer span.Finish()

	logger := loggerutils.Ctx(ctx, creatorModuleName).With().
		Str("tracking_id", req.RequestedTrackingID).Logger()

	logger.Info().Msg("received-order-creation-request")

	if partner.ID == envs.Instance.SheinPartnerId {
		customSheinRequest(req)
	}

	ocConfig, cliConfig, err := c.loadOCConfig(ctx, req, partner)
	if err != nil {
		logger.Err(err).Msg("Attach data and configure order client")
		return nil, false, err
	}

	requestItems, err := c.newOrderRequestItems(ctx, req, ocConfig, cliConfig)
	if err != nil {
		logger.Err(err).Msg("Prepare request items")
		return nil, false, err
	}

	vendorIds := make([]uint, len(requestItems))
	for i := 0; i < len(requestItems); i++ {
		vendorIds[i] = requestItems[i].Vendor.ID
	}
	logger.Info().Uints("vendor_ids", vendorIds).Msg("prepared-request-items")

	parcel, err = c.CreateParcelAndItemsAndVendorOrdersInDb(ctx, requestItems, req, ocConfig, cliConfig, nullableTxn)
	// [FPL-2334] Parcel already exist, return along with err
	if parcel != nil && err != nil {
		return parcel, true, nil
	} else if err != nil {
		logger.Err(err).Msg("Create parcel and order data")
		return nil, false, err
	}

	duplicated, err = c.performOrderRequests(ctx, parcel, requestItems, req, ocConfig, nullableTxn)
	if err != nil {
		logger.Err(err).Msg("Perform order requests")
		return parcel, duplicated, err
	}

	loggerutils.Ctx(ctx, creatorModuleName).Info().
		Str("requested_tracking_id", req.RequestedTrackingID).
		Str("tracking_id", parcel.TrackingID).
		Uint("parcel_id", parcel.ID).
		Msgf("create order successfully")

	return parcel, duplicated, nil
}

func getPartnerOrderCreationConfigs(
	ctx context.Context, partnerSettingRepo repo_interface.PartnerSettingRepository, partner *models.Partner,
) (*httpmodels.PartnerSettingOCConfig, error) {
	var ocConfig *httpmodels.PartnerSettingOCConfig
	if configs.PartnerOrderCreationMethod(partner.OrderCreationMethod.Uint8) == configs.OrderCreationMethod4PLOC {
		rawSettings, err := partnerSettingRepo.GetOne(
			ctx,
			models.PartnerSettingWhere.PartnerID.EQ(partner.ID),
			models.PartnerSettingWhere.Key.EQ(configs.OCConfigKey),
		)
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fplerror.ErrInternal.NewWithoutStack("Couldn't find OC Config for this partner")
		}

		if err != nil {
			return nil, fplerror.ErrInternal.NewWithoutStack(fplerror.DBInternalServerError)
		}

		if err = json.Unmarshal([]byte(rawSettings.Value.String), &ocConfig); err != nil {
			return nil, fplerror.ErrBadRequest.NewWithoutStack("Couldn't parse OC Config as JSON")
		}
	}

	return ocConfig, nil
}

func (c *orderCreator) loadOCConfig(
	ctx context.Context, req *order.BaseRequest, partner *models.Partner,
) (*order_creation_interface.OrderCreationConfig, *clientConfig, error) {
	ctx, span := nvtracer.CreateSpanFromContext(ctx)
	defer span.Finish()

	serviceWrapper, err := c.loadService(ctx, req.ServiceCode)
	if err != nil {
		return nil, nil, fplerror.ErrBadRequest.NewWithoutStack("order_create.service.not_found")
	}

	partnerOCConfigs, err := getPartnerOrderCreationConfigs(ctx, c.partnerSettingRepo, partner)
	if err != nil {
		return nil, nil, err
	}

	ocConfig := &order_creation_interface.OrderCreationConfig{
		Partner:         partner,
		PartnerOCConfig: partnerOCConfigs,
		Service:         serviceWrapper.Service,
		Product:         serviceWrapper.Product,
		ProductVendors:  serviceWrapper.ProductVendors,
		OrderFlow:       getOrderCreateFlow(serviceWrapper.ProductVendors, serviceWrapper.Service),
		VendorOrders:    make([]*models.VendorOrder, 0),
	}

	if !req.IsInternationalReturn() && configs.PartnerOrderCreationMethod(partner.OrderCreationMethod.Uint8) == configs.OrderCreationMethod4PLOC &&
		!slices.Contains(partnerOCConfigs.Services, serviceWrapper.Service.ID) {
		return nil, nil, fplerror.ErrBadRequest.NewWithoutStack("service %s is not supported", serviceWrapper.Service.Code)
	}

	// Find the order creation method in client configs
	orderCreationMethod := configs.PartnerOrderCreationMethod(partner.OrderCreationMethod.Uint8)
	clientCfg, found := clientConfigs[orderCreationMethod]
	if !found {
		return nil, nil, fplerror.ErrBadRequest.NewWithoutStack("Invalid order creation method configuration", orderCreationMethod)
	}

	// Check if the order flow is supported by this order creation method
	orderFlowFound := lo.Contains(clientCfg.SupportedOrderFlows, ocConfig.OrderFlow)
	if !orderFlowFound {
		return nil, nil, fplerror.ErrBadRequest.NewWithoutStack("We don't support '%+v' service for this client yet", ocConfig.OrderFlow)
	}

	err = validatePickupRequest(req, ocConfig)
	if err != nil {
		return nil, nil, err
	}
	return ocConfig, clientCfg, nil
}

func validatePickupRequest(req *order.BaseRequest, prepared *order_creation_interface.OrderCreationConfig) error {
	if req.Pickup == nil || prepared.Service.Type == serviceConst.TypeMMCCB2C.NullUint8() || prepared.Service.Type == serviceConst.TypeMMCCB2B.NullUint8() {
		return nil
	}

	if !lo.Contains([]orderConst.OrderCreateFlow{orderConst.FlowEndToEnd, orderConst.FlowOnlyFirstMile, orderConst.FlowDomestic}, prepared.OrderFlow) {
		return fplerror.ErrBadRequest.NewWithoutStack("Pickup request is not supported for this order")
	}

	if req.Pickup.Address == nil {
		return nil
	}

	if prepared.OrderFlow == orderConst.FlowDomestic && req.Pickup.Address.CountryCode != req.To.CountryCode {
		return fplerror.ErrBadRequest.NewWithoutStack(
			"Destination country must be same as pick up country when service type is domestic",
		)
	}

	if prepared.OrderFlow != orderConst.FlowDomestic && req.Pickup.Address.CountryCode != req.From.CountryCode {
		return fplerror.ErrBadRequest.NewWithoutStack(
			"Origin country must be same as pick up country when service type is not domestic",
		)
	}
	return nil
}

func customSheinRequest(req *order.BaseRequest) {
	// https://jira.ninjavan.co/browse/FPL-2655
	returnAddress := getCustomSheinReturnAddress(&req.To)
	if returnAddress != nil {
		req.Return = returnAddress
	}

	// https://jira.ninjavan.co/browse/FPL-4897
	if len(req.Items) != 0 {
		for i, item := range req.Items {
			req.Items[i].Description = truncateDescription(item.Description)
		}
	}
	if req.ParcelDetails != nil {
		req.ParcelDetails.CustomsDescription = truncateDescription(req.ParcelDetails.CustomsDescription)
	}
}

func getCustomSheinReturnAddress(address *order.Address) *order.Address {
	switch address.CountryCode {
	case "SG", "PH":
		return CustomSheinCountryReturnAddress[address.CountryCode]
	case "MY":
		if address.StateProvince == nil {
			address.StateProvince = null.StringFrom("").Ptr()
		}
		return getCustomSheinReturnAddressMY(address)
	default:
		return nil
	}
}

func getCustomSheinReturnAddressMY(address *order.Address) *order.Address {
	switch strings.TrimSpace(*address.StateProvince) {
	case "Johor", "Kedah", "Kelantan", "Kuala Lumpur", "Melaka", "Negeri Sembilan",
		"Pahang", "Penang", "Perak", "Perlis", "Putrajaya", "Selangor", "Terengganu", "":
		return CustomSheinMYReturnAddress["WEST"]
	case "Labuan", "Sabah", "Sarawak":
		return CustomSheinMYReturnAddress["EAST"]
	default:
		return nil
	}
}

func truncateDescription(description *string) *string {
	if description != nil && len(*description) > 255 {
		truncatedDescription := (*description)[:255]
		return &truncatedDescription
	}
	return description
}

var (
	SGAddress = order.Address{
		Name:          "Theron Ang",
		AddressLine1:  "5 Changi North Street 1 #3-01",
		AddressLine2:  nil,
		AddressLine3:  nil,
		AddressLine4:  nil,
		City:          null.StringFrom("Singapore").Ptr(),
		StateProvince: nil,
		CountryCode:   "SG",
		PostCode:      null.StringFrom("498766").Ptr(),
		ContactNumber: null.StringFrom("83669742").Ptr(),
		ContactEmail:  nil,
	}
	PHAddress = order.Address{
		Name:          "Tao Zhang",
		AddressLine1:  "Nissan Technopark, Brgy, Pulong Santa Cruz",
		AddressLine2:  nil,
		AddressLine3:  nil,
		AddressLine4:  nil,
		City:          null.StringFrom("Pulong Santa Cruz").Ptr(),
		StateProvince: null.StringFrom("Laguna").Ptr(),
		CountryCode:   "PH",
		PostCode:      null.StringFrom("4026").Ptr(),
		ContactNumber: null.StringFrom("09123456000").Ptr(),
		ContactEmail:  nil,
	}
)

var (
	CustomSheinCountryReturnAddress = map[string]*order.Address{
		"SG": &SGAddress,
		"PH": &PHAddress,
	}

	CustomSheinMYReturnAddress = map[string]*order.Address{
		"WEST": {
			Name:          "Elaine(KERRY)",
			AddressLine1:  "No. 27, Jalan TS 6/8, Taman Perindustrian Subang, 47500 Subang Jaya, Selangor.",
			AddressLine2:  nil,
			AddressLine3:  nil,
			AddressLine4:  nil,
			City:          null.StringFrom("Subang Jaya").Ptr(),
			StateProvince: null.StringFrom("Selangor").Ptr(),
			CountryCode:   "MY",
			PostCode:      null.StringFrom("47500").Ptr(),
			ContactNumber: null.StringFrom("0166241040").Ptr(),
		},
		"EAST": {
			Name:          "Sharifah Aqilah",
			AddressLine1:  "Warehouse E, Lot 72, Jalan Sepanggar, 88450 Kota Kinabalu, Sabah",
			AddressLine2:  nil,
			AddressLine3:  nil,
			AddressLine4:  nil,
			City:          null.StringFrom("Kota Kinabalu").Ptr(),
			StateProvince: null.StringFrom("Sabah").Ptr(),
			CountryCode:   "MY",
			PostCode:      null.StringFrom("88450").Ptr(),
			ContactNumber: null.StringFrom("+60 11-3322 5272").Ptr(),
		},
	}
)

const (
	parcelPrefix  = "FPL"
	defaultTIDLen = 24
	mmccTIDLen    = 20
)

var errIntegrationServiceNotFound = errors.New("couldn't found integrated service by vendor's handler")

func (c *orderCreator) prepareShippers(
	ctx context.Context, request *order.BaseRequest, ocConfig *order_creation_interface.OrderCreationConfig, cliConfig *clientConfig,
) error {
	var (
		orderShippers *order_creation_interface.OrderShippers
		err           error
	)

	if request.IsInternationalReturn() {
		orderShippers, err = c.shipperGetter.FindFulfillment(ctx, null.StringFromPtr(request.RefTrackingID).String)
	} else {
		orderShippers, err = c.shipperGetter.Find(ctx, ocConfig, request.GlobalShipperID, cliConfig.ShipperDetectionType, request.ForceGlobalShipperId)
	}
	if err != nil {
		return err
	}

	ocConfig.DefaultShipper = orderShippers.DefaultShipper
	ocConfig.OriginShipper = orderShippers.OriginShipper
	ocConfig.DestinationShipper = orderShippers.DestinationShipper
	ocConfig.ShipperPlatform = orderShippers.ShipperPlatform

	if cliConfig.ShipperDetectionType == orderConst.OrderShipperFromPartnerShippers {
		fillGlobalShipperID(ocConfig, request)
	}

	if request.IsCorporate() {
		branchShipper := c.parcelCreationShipper(ctx, request, ocConfig)
		loggerutils.Ctx(ctx, creatorModuleName).Info().
			Str("tracking_id", request.RequestedTrackingID).
			Uint("shipper_id", branchShipper.RemoteID.Uint).Msg("getting-corporate-shipper")

		corpShipper, err := c.shipperGetter.FindCorporate(ctx, int64(branchShipper.RemoteID.Uint), branchShipper.Country.String)
		if err != nil {
			return err
		}

		ocConfig.CorporateShipper = corpShipper.Shipper
		request.Corporate = &order.CorporateShipper{
			BranchID:                 corpShipper.BranchExternalRef,
			BranchGlobalShipperID:    branchShipper.RemoteID.Uint,
			CorporateGlobalShipperID: corpShipper.Shipper.RemoteID.Uint,
		}
	}
	return nil
}

func fillGlobalShipperID(ocConfig *order_creation_interface.OrderCreationConfig, request *order.BaseRequest) {
	switch ocConfig.OrderFlow {
	case orderConst.FlowOnlyLastMile, orderConst.FlowDomestic:
		request.GlobalShipperID = ocConfig.DestinationShipper.RemoteID.Uint
	case orderConst.FlowEndToEnd, orderConst.FlowOnlyFirstMile:
		fillOriginGlobalShipperID(ocConfig, request)
		if ocConfig.OriginShipper != nil && !ocConfig.OriginShipper.RemoteID.IsZero() {
			request.GlobalShipperID = ocConfig.OriginShipper.RemoteID.Uint
		}
	case orderConst.FlowMMCC:
		request.GlobalShipperID = ocConfig.DefaultShipper.RemoteID.Uint
	}
}

func fillOriginGlobalShipperID(ocConfig *order_creation_interface.OrderCreationConfig, request *order.BaseRequest) {
	if request.OriginGlobalShipperID != nil {
		return
	}

	if request.IsInternationalReturn() {
		if ocConfig.DestinationShipper != nil && !ocConfig.DestinationShipper.RemoteID.IsZero() {
			request.OriginGlobalShipperID = &ocConfig.DestinationShipper.RemoteID.Uint
		}
		return
	}

	if ocConfig.OriginShipper != nil && !ocConfig.OriginShipper.RemoteID.IsZero() {
		request.OriginGlobalShipperID = &ocConfig.OriginShipper.RemoteID.Uint
		return
	}
}

func (c *orderCreator) orderableShipper(ctx context.Context, stage string, request *order.BaseRequest, prepared *order_creation_interface.OrderCreationConfig) *models.Shipper {
	s := prepared.OriginShipper
	if stage == vendorConst.StageLastMile {
		s = prepared.DestinationShipper
	}

	if request != nil && request.Corporate != nil && request.IsCorporate() && s.RemoteID.Uint == request.Corporate.BranchGlobalShipperID {
		loggerutils.Ctx(ctx, creatorModuleName).Info().Str("tracking_id", request.RequestedTrackingID).
			Uint("branch_shipper", s.RemoteID.Uint).
			Uint("corp_shipper", prepared.CorporateShipper.RemoteID.Uint).
			Msg("using-corp-shipper")
		s = prepared.CorporateShipper
	}

	return s
}

func (c *orderCreator) parcelCreationShipper(ctx context.Context, request *order.BaseRequest, ocConfig *order_creation_interface.OrderCreationConfig) *models.Shipper {
	logger := loggerutils.Ctx(ctx, creatorModuleName)

	if ocConfig.DefaultShipper != nil {
		logger.Info().Msg(fmt.Sprintf("parcelCreationShipper with default global shipper id: %d", ocConfig.DefaultShipper.RemoteID.Uint))
	}

	if ocConfig.OriginShipper != nil {
		logger.Info().Msg(fmt.Sprintf("parcelCreationShipper with origin global shipper id: %d", ocConfig.OriginShipper.RemoteID.Uint))
	}

	if ocConfig.DestinationShipper != nil {
		logger.Info().Msg(fmt.Sprintf("parcelCreationShipper with destination global shipper id: %d", ocConfig.DestinationShipper.RemoteID.Uint))
	}

	if ocConfig.CorporateShipper != nil {
		logger.Info().Msg(fmt.Sprintf("parcelCreationShipper with corporated global shipper id: %d", ocConfig.CorporateShipper.RemoteID.Uint))
	}

	logger.Info().Msg(fmt.Sprintf("prepare order flow: %+v", ocConfig.OrderFlow))

	if ocConfig.OrderFlow == orderConst.FlowUnknown || ocConfig.OrderFlow == orderConst.FlowMMCC || request.IsInternationalReturn() {
		return ocConfig.DefaultShipper
	}

	if ocConfig.OrderFlow == orderConst.FlowOnlyLastMile || ocConfig.OrderFlow == orderConst.FlowDomestic {
		return ocConfig.DestinationShipper
	}

	if ocConfig.OrderFlow == orderConst.FlowEndToEnd && ocConfig.OriginShipper == nil {
		return ocConfig.DestinationShipper
	}

	return ocConfig.OriginShipper
}

func (c *orderCreator) newOrderRequestItem(
	ctx context.Context, productVendorWrapper *repo_interface.ProductVendorWrapper, request *order.BaseRequest,
	ocConfig *order_creation_interface.OrderCreationConfig, config *clientConfig,
) (*order_creation_interface.RequestItem, error) {
	cloneRequest := *request

	productVendor := &productVendorWrapper.ProductVendor
	vendor := &productVendorWrapper.Vendor

	reqItem := &order_creation_interface.RequestItem{
		ProductVendor: productVendor,
		Request:       &cloneRequest,
		Vendor:        vendor,
	}

	stage := productVendor.Stage.String

	integratedOC := orderConst.IntegratedOCVendor[vendor.Handler][stage]
	if integratedOC {
		reqItem.IntegratedServiceType = OrderCreateServiceType
	}

	// if not found both tracking and order-creation handle, so the request item won't be created
	if reqItem.IntegratedServiceType == UndefinedServiceType {
		return nil, errIntegrationServiceNotFound
	}

	switch vendor.Handler {
	case vendorConst.VendorHandlerNVCore:
		reqItem.OrderableShipper = c.orderableShipper(ctx, stage, request, ocConfig)
		creator, err := nvcore.New(nvcore.OrderInitParams{
			Request:              reqItem.Request,
			Shipper:              reqItem.OrderableShipper,
			ShipperPlatform:      ocConfig.ShipperPlatform,
			ShipperDetectionType: config.ShipperDetectionType,
			IsConsolidateFlow:    ocConfig.IsNeededConsolidatedShipper(),
			ServiceType:          serviceConst.Type(ocConfig.Service.Type.Uint8),
			OrderFlow:            ocConfig.OrderFlow,
			Stage:                stage,
			SystemID:             strings.ToLower(vendor.Country),
		})
		if err != nil {
			return nil, err
		}

		// FPL-3242: map goods value of Tiktok OC request with insured_value of Core OC request
		if ocConfig.Partner.ID == envs.Instance.Tiktok.PartnerId {
			if request.ParcelDetails != nil {
				creator.SetInsuredValue(null.Float64FromPtr(request.ParcelDetails.Value).Float64)
			}
		}
		reqItem.VendorAPIService = creator
	case vendorConst.VendorHandlerACS:
		reqItem.VendorAPIService = acs.NewService()
		reqItem.Request.To.CountryCode = "TW"
	case vendorConst.VendorHandlerYun:
		reqItem.VendorAPIService = yun.NewService()
		reqItem.Request.From.CountryCode = "HK"
	}

	return reqItem, nil
}

func (c *orderCreator) newOrderRequestItems(
	ctx context.Context, request *order.BaseRequest, ocConfig *order_creation_interface.OrderCreationConfig, cliConfig *clientConfig,
) ([]*order_creation_interface.RequestItem, error) {
	ctx, span := nvtracer.CreateSpanFromContext(ctx)
	defer span.Finish()

	if err := c.prepareShippers(ctx, request, ocConfig, cliConfig); err != nil {
		return nil, err
	}

	if shipper := c.parcelCreationShipper(ctx, request, ocConfig); shipper != nil {
		c.autoFillTaxes(ctx, request, shipper)
	}

	vendorReqItems := make(map[uint]*order_creation_interface.RequestItem)
	for _, productVendorWrapper := range ocConfig.ProductVendors {
		_, ok := vendorReqItems[productVendorWrapper.Vendor.ID]
		if !ok {
			newItem, err := c.newOrderRequestItem(ctx, productVendorWrapper, request, ocConfig, cliConfig)
			// if no service has been found, so no need to prepare order request item
			if errors.Is(err, errIntegrationServiceNotFound) {
				continue
			}
			if err != nil {
				return nil, err
			}

			vendorReqItems[productVendorWrapper.ProductVendor.VendorID] = newItem
		}
	}

	reqItems := make([]*order_creation_interface.RequestItem, 0, len(vendorReqItems))
	for _, reqItem := range vendorReqItems {
		if orderCreatedInCoreSystem(request.Source, serviceConst.Type(ocConfig.Service.Type.Uint8), ocConfig.ProductVendors, reqItem) {
			loggerutils.Ctx(ctx, creatorModuleName).Info().
				Str("tracking_id", request.RequestedTrackingID).
				Uint("vendor_id", reqItem.Vendor.ID).
				Msg("mark-request-as-skipped")
			reqItem.SkipSendingNVRequest = true
		}

		reqItems = append(reqItems, reqItem)
	}

	// Sort ascending by sequence No. of product vendor in requests
	// This step is a guarantee for the calling API process happens sequentially
	sortRequestItemsBySequence(reqItems)
	return reqItems, nil
}

func orderCreatedInCoreSystem(
	requestSource order.Source, serviceType serviceConst.Type,
	productVendors repo_interface.ProductVendorWrapperSlice, reqItem *order_creation_interface.RequestItem,
) bool {
	isUnifiedOCRequest := requestSource == order.SourceCoreConsumerV2 || serviceType == serviceConst.TypeB2BBundle
	hasNvFm := lo.ContainsBy(productVendors, func(productVendor *repo_interface.ProductVendorWrapper) bool {
		return productVendor.ProductVendor.Stage.String == vendorConst.StageFirstMile &&
			productVendor.Vendor.Handler == vendorConst.VendorHandlerNVCore
	})
	if isUnifiedOCRequest {
		if reqItem.ProductVendor.Stage.String == vendorConst.StageFirstMile ||
			(reqItem.ProductVendor.Stage.String == vendorConst.StageLastMile && !hasNvFm) {
			return true
		}
	}
	return false
}

func getShipperTaxNumber(taxName string, taxes map[string]string) string {
	shipperTaxNumber, ok := taxes[taxName]
	if !ok {
		return ""
	}
	return strings.TrimSpace(shipperTaxNumber)
}

func (c *orderCreator) fillShipperTaxNumber(ctx context.Context, taxName, shipperTaxNumber string, request *order.BaseRequest, shipper *models.Shipper) {
	if shipperTaxNumber == "" {
		return
	}

	diffTaxNumbers := make([]string, 0, len(request.Items))
	for _, item := range request.Items {
		if item == nil {
			continue
		}

		if item.Taxes == nil {
			item.Taxes = make(map[order.TaxName]order.TaxInfo)
		}

		ocTaxInfo := item.Taxes[order.TaxName(taxName)]
		if ocTaxInfo.Number != nil && *ocTaxInfo.Number != shipperTaxNumber {
			diffTaxNumbers = append(diffTaxNumbers, *ocTaxInfo.Number)
		}

		ocTaxInfo.Number = &shipperTaxNumber
		item.Taxes[order.TaxName(taxName)] = ocTaxInfo

		if taxName == parcelConst.GSTTaxName {
			if item.GstRegistrationNumber != nil && *item.GstRegistrationNumber != shipperTaxNumber {
				diffTaxNumbers = append(diffTaxNumbers, *item.GstRegistrationNumber)
			}
			item.GstRegistrationNumber = &shipperTaxNumber
		}
	}

	if len(diffTaxNumbers) > 0 {
		loggerutils.Ctx(ctx, creatorModuleName).Warn().
			Strs("oc_tax_numbers", diffTaxNumbers).
			Str("shipper_tax_number", shipperTaxNumber).
			Str("tax_name", taxName).
			Uint("shipper_id", shipper.ID).
			Msg("detect-inconsistent-tax-numbers")
	}
}

func (c *orderCreator) autoFillTaxes(ctx context.Context, request *order.BaseRequest, shipper *models.Shipper) {
	if request == nil || shipper == nil {
		return
	}

	if !shipper.TaxNumbers.Valid {
		return
	}

	var taxes map[string]string
	err := json.Unmarshal([]byte(shipper.TaxNumbers.String), &taxes)
	if err != nil {
		loggerutils.Ctx(ctx, creatorModuleName).Err(err).Msg("failed-to-parse-taxes-from-shipper-profile")
		return
	}

	for _, taxName := range shipperConst.AvailableTaxesNamesByCountry[request.To.CountryCode] {
		shipperTaxNumber := getShipperTaxNumber(taxName, taxes)
		c.fillShipperTaxNumber(ctx, taxName, shipperTaxNumber, request, shipper)
	}
}

func (c *orderCreator) performOrderRequests(
	ctx context.Context, parcel *models.Parcel, reqItems []*order_creation_interface.RequestItem, request *order.BaseRequest,
	prepared *order_creation_interface.OrderCreationConfig, txn boil.ContextTransactor,
) (duplicated bool, err error) {
	for _, reqItem := range reqItems {
		if reqItem.VendorAPIService == nil {
			continue
		}
		// https://jira.ninjavan.co/browse/FPL-4297
		reqItem.VendorOrder.Country = null.StringFrom(strings.ToLower(reqItem.Vendor.Country))
		prepared.VendorOrders = append(prepared.VendorOrders, reqItem.VendorOrder)

		if reqItem.Vendor.Handler == vendorConst.VendorHandlerNVCore && reqItem.SkipSendingNVRequest {
			loggerutils.Ctx(ctx, creatorModuleName).Info().
				Str("tracking_id", request.RequestedTrackingID).
				Uint("vendor_id", reqItem.Vendor.ID).
				Msg("skip-sending-request")
			continue
		}

		// Downstream vendors will get the tracking id generated from the first vendor
		if !reqItem.IsUpstreamVendor {
			if reqItem.Vendor.Handler == vendorConst.VendorHandlerNVCore {
				if service, ok := reqItem.VendorAPIService.(*nvcore.OrderCreator); ok {
					service.SetRequestedTrackingID(parcel.TrackingID)
				}
			} else {
				reqItem.Request.RequestedTrackingID = parcel.TrackingID
			}
		}

		duplicated, err = c.performOrderRequest(ctx, reqItem, parcel, request)
		// Consider duplications in either FM or LM
		if err != nil {
			return duplicated, err
		}
	}

	// after performing OC requests, some information (such as tracking ID, delivery start date, & metadata) need to be stored with updated values from vendors
	parcelDetails := models.ParcelDetails{}
	_ = json.Unmarshal([]byte(parcel.Metadata.String), &parcelDetails)
	parcelDetails.Status = parcelConst.ParcelStatusSuccess

	bMetadata, _ := json.Marshal(parcelDetails)
	parcel.Metadata = null.StringFrom(string(bMetadata))

	err = c.storeManager.PersistUpdatedParcel(ctx, parcel, txn)
	if err != nil && !utils.DBError(err).IsDuplicatedEntry() {
		loggerutils.Ctx(ctx, creatorModuleName).Err(err).Str("tracking_id", parcel.TrackingID).Msg("failed-to-persist-update-parcel")
		return duplicated, fplerror.ErrInternal.NewWithoutStack("System failed to update parcel")
	}

	if err == nil {
		var event *fpl.FPLParcelCreationEventV2
		event, err = parcel2.BuildFplParcelCreationEventV2(ctx, &parcel2.ParcelCreateData{
			Parcel:       parcel,
			Partner:      prepared.Partner,
			Shipper:      c.parcelCreationShipper(ctx, request, prepared),
			Product:      prepared.Product,
			Service:      prepared.Service,
			VendorOrders: prepared.VendorOrders,
		})
		if err != nil {
			return duplicated, fplerror.ErrInternal.NewWithoutStack("System failed to prepare creation event")
		}
		if err = c.parcelCreationEventCreator.Create(ctx, event); err != nil {
			return duplicated, fplerror.ErrInternal.NewWithoutStack("System failed to create creation event")
		}

		return duplicated, nil
	}

	// update vendor tracking id to prevent Delete function from deleting core orders
	for _, reqItem := range reqItems {
		if reqItem.VendorAPIService == nil {
			continue
		}

		reqItem.VendorOrder.VendorTrackingID = null.String{}

		if err = c.storeManager.UpdateVendorOrder(ctx, reqItem.VendorOrder); err != nil {
			return duplicated, err
		}
	}

	loggerutils.Ctx(ctx, creatorModuleName).Info().Str("tracking_id", parcel.TrackingID).Msg("delete-order-duplicated-error")
	err = c.orderDeleter.Delete(ctx, parcel.PartnerID.Uint64, parcel.ID)
	if err != nil {
		loggerutils.Ctx(ctx, creatorModuleName).Err(err).Uint("parcel_id", parcel.ID).Msg("failed-to-delete-redundant-parcel")
		return duplicated, err
	}

	existedParcel, err := c.storeManager.GetExistParcel(ctx, parcel)
	if err != nil {
		return duplicated, err
	}
	parcel.ID = existedParcel.ID

	return duplicated, err
}

func isDuplicatedError(err error, reqItem *order_creation_interface.RequestItem) bool {
	if err == nil || reqItem == nil || reqItem.Vendor == nil {
		return false
	}

	if nvcore.IsDuplicated(err) {
		return true
	}

	return errors.Is(err, fplerror.ErrOrderDuplicated)
}

func (c *orderCreator) performOrderRequest(
	ctx context.Context, reqItem *order_creation_interface.RequestItem, parcel *models.Parcel, originalReq *order.BaseRequest,
) (bool, error) {
	duplicatedOrder := false
	ctx, span := nvtracer.CreateSpanFromContext(ctx)
	defer span.Finish()

	var (
		responder base.OCResponse
		request   base.OCRequest
		err       error
	)

	if reqItem.Request != nil && testdata.IsLoadTestTraffic(reqItem.Request.RequestedTrackingID) {
		responder, request, err = testdata.FakeCoreOCAPI(reqItem.Request.RequestedTrackingID)
	} else if reqItem.Vendor.Handler == vendorConst.VendorHandlerNVCore {
		responder, request, err = reqItem.VendorAPIService.Create(ctx, reqItem.OrderableShipper.Country.String)
	} else {
		request, responder, err = reqItem.VendorAPIService.OC(ctx, *reqItem.Request)
	}

	if err != nil {
		log.Ctx(ctx).Err(err).Msgf("failed to perform order request of %s", reqItem.Vendor.Handler)
		if responder != nil {
			log.Ctx(ctx).Err(err).Msgf("perform order request failed with response %s and error %s", responder.GetBody(), responder.GetErrorMsg())
		}
	}

	originOCResponder, originOCErr := responder, err
	if isDuplicatedError(err, reqItem) {
		duplicatedOrder = true
		getResponder, getErr := reqItem.VendorAPIService.GetByRequestedTrackingID(ctx, reqItem.Request.RequestedTrackingID)
		if getErr != nil {
			log.Ctx(ctx).Err(getErr).Msg("failed to get order by requested tracking id")
		}
		if getResponder != nil {
			responder, err = getResponder, nil
		}
	}

	// Update vendor_order, parcel
	ocStatus := vendorOrderConst.GetStatusFromErr(err)
	if ocStatus == vendorOrderConst.StatusSucceeded {
		// for core vendor in E2E flow, only update specific fields. Other fields are filled in core backfill order flow.
		if reqItem.Vendor.Handler == vendorConst.VendorHandlerNVCore && parcel.Type != parcelConst.B2BBundle {
			reqItem.VendorOrder.Status = ocStatus
			reqItem.VendorOrder.VendorTrackingID = null.StringFrom(responder.GetVendorTrackingID())
			// update parcel tracking ID based on response from the first NV vendor
			if reqItem.IsUpstreamVendor {
				parcel.TrackingID = responder.GetVendorTrackingID()
			}
			// update parcel delivery based on response from LM vendor
			if _, ok := responder.(*nvcore.OrderResponse); ok && reqItem.IsDeliveryIncluded() && reqItem.Vendor != nil {
				c.setDeliveryStartDate(parcel, (responder.(*nvcore.OrderResponse)).GetDeliveryStartDate(), originalReq)
			}
		} else {
			// for other cases, fill vendor order with order details OC-ed to vendor
			reqItem.Request.FillVendorOrder(reqItem.VendorOrder)
			reqItem.VendorOrder.VendorTrackingID = null.StringFrom(responder.GetVendorTrackingID())
			reqItem.VendorOrder.Country = null.StringFrom(strings.ToLower(reqItem.Vendor.Country))
			reqItem.VendorOrder.Status = ocStatus
			switch reqItem.Vendor.Handler {
			case vendorConst.VendorHandlerYun:
				reqItem.VendorOrder.ShouldPoll = true
			}
		}
	}

	// Init a vendor request as log
	vendorRequest := &models.VendorRequest{
		VendorID:    reqItem.Vendor.ID,
		TrackingID:  reqItem.VendorOrder.VendorTrackingID.String,
		RequestName: fmt.Sprintf("OC request for %s", reqItem.Vendor.Name),
		Status:      vendorRequestConst.GetStatusFromErr(originOCErr),
	}
	if reqItem.OrderableShipper != nil {
		vendorRequest.ShipperID = null.UintFrom(reqItem.OrderableShipper.ID)
	}

	if request != nil {
		vendorRequest.RequestBody = null.NewString(request.GetBody(), request.GetBody() != "")
		vendorRequest.RequestHeader = null.NewString(request.GetHeader(), request.GetHeader() != "")
	}

	if originOCResponder != nil {
		vendorRequest.ResponseBody = null.NewString(originOCResponder.GetBody(), originOCResponder.GetBody() != "")
		vendorRequest.ResponseHeader = null.NewString(originOCResponder.GetHeader(), originOCResponder.GetHeader() != "")
	}

	if reqItem.Vendor.Handler == vendorConst.VendorHandlerNVCore {
		if originOCErr != nil {
			vendorRequest.PlainError = null.StringFrom(originOCErr.Error())
		}
	} else {
		if originOCResponder != nil {
			vendorRequest.PlainError = null.StringFrom(originOCResponder.GetErrorMsg())
		}
	}

	afterPerformErr := c.storeManager.UpdateVendorOrderAndCreateVendorRequest(ctx, reqItem.VendorOrder, vendorRequest)
	if afterPerformErr != nil {
		return duplicatedOrder, afterPerformErr
	}

	return duplicatedOrder, err
}

func (c *orderCreator) setDeliveryStartDate(parcel *models.Parcel, deliDate string, request *order.BaseRequest) {
	if parcel == nil || request == nil || request.Delivery == nil {
		return
	}
	request.Delivery.StartDate = deliDate
	bDeliInfo, _ := json.Marshal(request.Delivery)
	parcel.DeliveryInfo = null.StringFrom(string(bDeliInfo))
}

// all parcels should belong to the same order, and share the same product/service
func (c *orderCreator) createTranslateDescriptionTasks(ctx context.Context, mmccParcels models.ParcelSlice) {
	if len(mmccParcels) == 0 {
		return
	}

	if mmccParcels[0].ServiceID.IsZero() {
		return
	}

	product, err := c.productRepo.GetOne(ctx, models.ProductWhere.ID.EQ(mmccParcels[0].ProductID.Uint))
	if err != nil {
		log.Ctx(ctx).Err(err).Msg("get-product-of-bag-failed")
		return
	}

	bagService, err := c.serviceRepo.GetOneById(ctx, mmccParcels[0].ServiceID.Uint)
	if err != nil {
		log.Ctx(ctx).Err(err).Msg("get-service-of-bag-failed")
		return
	}
	for _, parcel := range mmccParcels {
		_ = c.eventPublisher.Publish(ctx, envs.Instance.KafkaTopics.FplAutoTranslateDescriptionTopic, &fpl.FPLTranslateDescriptionData{
			RequestInfo: &common.RequestInfo{
				RequestId: internalcontext.GetRequestIdFromContext(ctx),
				SystemId:  systemid2.FromString(product.DestinationCountry, systemid2.Global).Country,
			},
			ParcelId:           uint64(parcel.ID),
			PartnerId:          mmccParcels[0].PartnerID.Uint64,
			OriginCountry:      product.OriginCountry,
			DestinationCountry: product.DestinationCountry,
			ServiceType:        uint32(bagService.Type.Uint8),
		})
	}
}

func mergeParcelDetail(originParcelDetail *models.ParcelDetails, requestParcelDetail *models.ParcelDetails) *models.ParcelDetails {
	originParcelDetail.IsRelabel = true

	originParcelDetail.Status = requestParcelDetail.Status

	if requestParcelDetail.ShipperSubmittedWeight != nil {
		originParcelDetail.ShipperSubmittedWeight = requestParcelDetail.ShipperSubmittedWeight
	}

	if requestParcelDetail.Weight != nil {
		originParcelDetail.Weight = requestParcelDetail.Weight
	}

	if requestParcelDetail.ActualWeight != nil {
		originParcelDetail.ActualWeight = requestParcelDetail.ActualWeight
	}

	if requestParcelDetail.BatteryPacking != 0 {
		originParcelDetail.BatteryPacking = requestParcelDetail.BatteryPacking
	}

	if requestParcelDetail.BatteryType != 0 {
		originParcelDetail.BatteryType = requestParcelDetail.BatteryType
	}

	if requestParcelDetail.Value != nil {
		originParcelDetail.Value = requestParcelDetail.Value
	}

	if requestParcelDetail.CustomsCurrency != nil {
		originParcelDetail.CustomsCurrency = requestParcelDetail.CustomsCurrency
	}

	if requestParcelDetail.TaxID != nil {
		originParcelDetail.TaxID = requestParcelDetail.TaxID
	}

	if requestParcelDetail.InvoiceURL != nil {
		originParcelDetail.InvoiceURL = requestParcelDetail.InvoiceURL
	}

	if requestParcelDetail.TradeTerms != nil {
		originParcelDetail.TradeTerms = requestParcelDetail.TradeTerms
	}

	if requestParcelDetail.CustomsDescription != nil {
		originParcelDetail.CustomsDescription = requestParcelDetail.CustomsDescription
	}

	if requestParcelDetail.CustomsDescription != nil {
		originParcelDetail.CustomsDescription = requestParcelDetail.CustomsDescription
	}

	if requestParcelDetail.CustomsNativeDescription != nil {
		originParcelDetail.CustomsNativeDescription = requestParcelDetail.CustomsNativeDescription
	}

	if requestParcelDetail.HSCode != nil {
		originParcelDetail.HSCode = requestParcelDetail.HSCode
	}

	if requestParcelDetail.OriginCountry != nil {
		originParcelDetail.OriginCountry = requestParcelDetail.OriginCountry
	}

	if requestParcelDetail.Metadata != "" {
		originParcelDetail.Metadata = requestParcelDetail.Metadata
	}

	if requestParcelDetail.RoutingType != fpl.RoutingType_UNDEFINED {
		originParcelDetail.RoutingType = requestParcelDetail.RoutingType
	}

	return originParcelDetail
}

func IsHighValueParcel(value *float64, items order.ParcelItemSlice, toCountryCode null.String) bool {
	var isHighValue bool
	var deMinimsValue int

	if highValueByCountry, exists := parcelConst.HighValueMap[toCountryCode.String]; exists {
		deMinimsValue = highValueByCountry.Value
	}

	if deMinimsValue == 0 {
		return isHighValue
	}
	var totalValueOfItems float64
	for _, item := range items {
		if item.Quantity != nil && item.UnitValue != nil {
			value := float64(*item.Quantity) * (*item.UnitValue)
			totalValueOfItems += value
		}
	}

	var totalValueOfParcels float64
	if value != nil {
		totalValueOfParcels = *value
	}

	totalValue := math.Max(totalValueOfItems, totalValueOfParcels)

	if totalValue >= float64(deMinimsValue) {
		isHighValue = true
	}

	return isHighValue
}

func populateParcelWithBagInfo(bag *models.Parcel, parcel *models.Parcel) {
	parcel.ParentID = null.UintFrom(bag.ID)
	parcel.SourceOrderID = bag.SourceOrderID
	parcel.ServiceID = bag.ServiceID
	parcel.ProductID = bag.ProductID
	parcel.OriginVendorID = bag.OriginVendorID
	parcel.PartnerID = bag.PartnerID
	parcel.ShipperID = bag.ShipperID

	// Cascade bundle metadata info
	if bag.Type != parcelConst.B2BBundle {
		return
	}
	bagMetadata := models.ParcelDetails{}
	_ = json.Unmarshal([]byte(bag.Metadata.String), &bagMetadata)

	parcelMetadata := repo_interface.ParcelMetadata{}
	_ = json.Unmarshal([]byte(parcel.Metadata.String), &parcelMetadata)

	parcelMetadata.B2BBundle = bagMetadata.B2BBundle
	parcelMetadata.B2BBundle.PieceTrackingIDs = nil
	parcelMetadata.B2BBundle.RequestedPieceTrackingIDs = nil

	parcelMetadata.ShipperSubmittedDimensions = bagMetadata.ShipperSubmittedDimensions
	parcelMetadata.Quantity = bagMetadata.Quantity
	parcelMetadata.Status = bagMetadata.Status

	parcelMetadataString, _ := json.Marshal(parcelMetadata)
	parcel.Metadata = null.StringFrom(string(parcelMetadataString))

	parcel.PickupInfo = bag.PickupInfo
	parcel.DeliveryInfo = bag.DeliveryInfo
}

func fillTaxesToParcelItem(item *models.ParcelItem, shipper *models.Shipper, toCountryCode string) {
	if !shipper.TaxNumbers.Valid {
		return
	}

	var shipperTaxes map[string]string

	err := json.Unmarshal([]byte(shipper.TaxNumbers.String), &shipperTaxes)
	if err != nil {
		return
	}

	metadata := &repo_interface.ParcelItemMetadata{}
	_ = json.Unmarshal([]byte(item.Metadata), metadata)
	for _, taxName := range shipperConst.AvailableTaxesNamesByCountry[toCountryCode] {
		shipperTaxNumber := getShipperTaxNumber(taxName, shipperTaxes)
		if shipperTaxNumber == "" {
			return
		}
		if metadata.Taxes == nil {
			metadata.Taxes = make(map[order.TaxName]order.TaxInfo)
		}

		ocTaxInfo := metadata.Taxes[order.TaxName(taxName)]

		ocTaxInfo.Number = &shipperTaxNumber
		metadata.Taxes[order.TaxName(taxName)] = ocTaxInfo

		if taxName == parcelConst.GSTTaxName {
			metadata.GstRegistrationNumber = null.StringFrom(shipperTaxNumber).Ptr()
		}
	}
	item.Metadata = utils.JsonMarshalStrIgnoreError(metadata)
}

func (c *orderCreator) loadService(ctx context.Context, serviceCode string) (*service_interface.ServiceWrapper, error) {
	service, err := c.serviceRepo.FindOne(ctx, models.ServiceWhere.Code.EQ(serviceCode))
	if err != nil {
		return nil, fmt.Errorf("load service error: %w", err)
	}

	if service.ProductID.IsZero() {
		return nil, errors.New("no product has been assigned to the service")
	}
	product, err := c.productRepo.GetOne(ctx, models.ProductWhere.ID.EQ(service.ProductID.Uint))
	if err != nil {
		return nil, fmt.Errorf("load product error: %w", err)
	}

	productVendors, err := c.productVendorRepo.GetProductVendorWrappers(ctx, product.ID)
	if err != nil || len(productVendors) == 0 {
		return nil, fmt.Errorf("load product vendor error: %w", err)
	}

	return &service_interface.ServiceWrapper{
		Service:        service,
		Product:        product,
		ProductVendors: productVendors,
	}, nil
}

// create instance for parcel model then attach to request item
func (c *orderCreator) CreateParcelAndItemsAndVendorOrdersInDb(
	ctx context.Context, reqItems []*order_creation_interface.RequestItem, request *order.BaseRequest,
	ocConfig *order_creation_interface.OrderCreationConfig, config *clientConfig, nullableTxn boil.ContextTransactor,
) (*models.Parcel, error) {
	ctx, span := nvtracer.CreateSpanFromContext(ctx)
	defer span.Finish()

	var err error

	service := ocConfig.Service
	product := ocConfig.Product
	firstVendor := ocConfig.ProductVendors[0].Vendor

	parcelType := parcelConst.Parcel
	tidLen := defaultTIDLen
	switch serviceConst.Type(service.Type.Uint8) {
	case serviceConst.TypeMMCCB2C:
		tidLen = mmccTIDLen
		// backward compatible code. TODO remove after clients migrate. Also remove in base request. -> always use BagB2CV2
		if request.IsMMCCB2C {
			parcelType = parcelConst.BagB2CV2
		} else {
			parcelType = parcelConst.BagB2B
		}
	case serviceConst.TypeMMCCB2B:
		tidLen = mmccTIDLen
		parcelType = parcelConst.BagB2B
	case serviceConst.TypeB2BBundle:
		parcelType = parcelConst.B2BBundle
	default:
	}

	parcelTrackingID := request.RequestedTrackingID

	if parcelTrackingID == "" {
		if parcelType == parcelConst.Parcel && ocConfig.OrderFlow == orderConst.FlowUnknown {
			return nil, fplerror.ErrBadRequest.NewWithoutStack("Requested Tracking ID is required for non-NV service")
		}

		if parcelType == parcelConst.Parcel && config.IsRequiredTrackingNumber {
			return nil, fplerror.ErrBadRequest.NewWithoutStack("Requested Tracking ID is required for this specific partner")
		}

		parcelTrackingID = strings.ToUpper(c.trackingIDGenerator.Generate(ctx, parcelPrefix, tidLen))
		loggerutils.Ctx(ctx, creatorModuleName).Info().Str("tracking_id", parcelTrackingID).Msg("tracking-id-generated")
	}

	parcel := &models.Parcel{
		ProductID:      null.UintFrom(product.ID),
		OriginVendorID: firstVendor.ID,
		TrackingID:     parcelTrackingID,
		Source:         uint16(request.Source),
		Type:           parcelType,
		ServiceID:      null.UintFrom(service.ID),
		SourceOrderID:  null.StringFromPtr(request.SourceOrderID),
		RefTrackingID:  null.StringFromPtr(request.RefTrackingID),
		PartnerID:      null.Uint64From(ocConfig.Partner.ID),

		FromName:          null.StringFrom(request.From.Name),
		FromAddressLine1:  null.StringFrom(request.From.AddressLine1),
		FromAddressLine2:  null.StringFromPtr(request.From.AddressLine2),
		FromAddressLine3:  null.StringFromPtr(request.From.AddressLine3),
		FromAddressLine4:  null.StringFromPtr(request.From.AddressLine4),
		FromCity:          null.StringFromPtr(request.From.City),
		FromCountryCode:   null.StringFrom(request.From.CountryCode),
		FromStateProvince: null.StringFromPtr(request.From.StateProvince),
		FromPostcode:      null.StringFromPtr(request.From.PostCode),
		FromContactNumber: null.StringFromPtr(request.From.ContactNumber),
		FromContactEmail:  null.StringFromPtr(request.From.ContactEmail),

		ToName:           null.StringFrom(request.To.Name),
		ToAddressLine1:   null.StringFrom(request.To.AddressLine1),
		ToAddressLine2:   null.StringFromPtr(request.To.AddressLine2),
		ToAddressLine3:   null.StringFromPtr(request.To.AddressLine3),
		ToAddressLine4:   null.StringFromPtr(request.To.AddressLine4),
		ToCity:           null.StringFromPtr(request.To.City),
		ToCountryCode:    null.StringFrom(request.To.CountryCode),
		ToStateProvince:  null.StringFromPtr(request.To.StateProvince),
		ToPostcode:       null.StringFromPtr(request.To.PostCode),
		ToContactNumber:  null.StringFromPtr(request.To.ContactNumber),
		ToContactEmail:   null.StringFromPtr(request.To.ContactEmail),
		PartnerUniqueKey: null.StringFromPtr(request.PartnerUniqueKey),
	}

	if shipper := c.parcelCreationShipper(ctx, request, ocConfig); shipper != nil {
		parcel.ShipperID = null.UintFrom(shipper.ID)
	}

	if request.ParcelDetails == nil {
		request.ParcelDetails = &models.ParcelDetails{}
	}
	request.ParcelDetails.Status = parcelConst.ParcelStatusDraft

	if request.ParcelDetails != nil {
		highValue := IsHighValueParcel(request.ParcelDetails.Value, request.Items, null.StringFrom(request.To.CountryCode))

		request.ParcelDetails.IsHighValue = null.BoolFrom(highValue).Ptr()
		request.ParcelDetails.RequestedTrackingID = request.RequestedTrackingID
		request.ParcelDetails.RoutingType = fpl.RoutingType_FORWARD

		if request.IsInternationalReturn() {
			request.ParcelDetails.RoutingType = fpl.RoutingType_RETURN_TO_ORIGIN
		}

		bMetadata, _ := json.Marshal(request.ParcelDetails)
		parcel.Metadata = null.StringFrom(string(bMetadata))
		if request.ParcelDetails.IsRelabel {
			request.ParcelDetails.RoutingType = fpl.RoutingType_RELABEL
			originMetadata := c.getOriginParcelMetaData(ctx, request)
			if !originMetadata.IsZero() {
				parcel.Metadata = originMetadata
			}
		}
	}

	if request.Return != nil {
		bReturn, _ := json.Marshal(request.Return)
		parcel.ReturnAddress = null.StringFrom(string(bReturn))
	}

	if request.Delivery != nil {
		bDelivery, _ := json.Marshal(request.Delivery)
		parcel.DeliveryInfo = null.StringFrom(string(bDelivery))
	}

	if request.Pickup != nil {
		bPickup, _ := json.Marshal(request.Pickup)
		parcel.PickupInfo = null.StringFrom(string(bPickup))
	}

	err = c.storeManager.CreateParcelAndItemsAndVendorOrdersInDb(ctx, parcel, reqItems, request, nullableTxn)
	if utils.DBError(err).IsDuplicatedEntry() {
		return parcel, fmt.Errorf("tracking id is duplicated")
	}
	if err != nil {
		return nil, err
	}

	// Mark the item as upstream if it is the first NVCore request item
	for _, item := range reqItems {
		if item.Vendor.Handler == vendorConst.VendorHandlerNVCore {
			item.IsUpstreamVendor = true
			break
		}
	}
	return parcel, nil
}

func (c *orderCreator) getOriginParcelMetaData(ctx context.Context, request *order.BaseRequest) null.String {
	reqTID := null.StringFromPtr(request.RefTrackingID).String
	logger := loggerutils.Ctx(ctx, creatorModuleName).With().Str("source_reference_id", reqTID).Logger()

	originParcel, err := c.parcelRepo.GetOne(ctx,
		models.ParcelWhere.TrackingID.EQ(reqTID),
		models.ParcelWhere.Metadata.IsNotNull(),
	)
	if err != nil || originParcel == nil {
		logger.Err(err).Msg("can-not-get-parcel")
		return null.String{}
	}

	originParcelDetails := &models.ParcelDetails{}
	if originParcel.Metadata.Valid {
		if err = json.Unmarshal([]byte(originParcel.Metadata.String), originParcelDetails); err != nil {
			logger.Err(err).Msg("can-not-unmarshal-parcel-detail")
			return null.String{}
		}
	}

	parcelDetailMerged := mergeParcelDetail(originParcelDetails, request.ParcelDetails)
	bMetadata, err := json.Marshal(parcelDetailMerged)
	if err != nil {
		logger.Err(err).Msg("can-not-marshal-parcel-detail")
		return null.String{}
	}

	return null.StringFrom(string(bMetadata))
}
