package order_creation

import (
	"context"
	"database/sql"
	"encoding/json"
	"testing"

	"github.com/bsm/redislock"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"

	"bitbucket.ninjavan.co/fpl/4pl-protos/go/fpl"

	parcelConst "git.ninjavan.co/3pl/configs/parcel"
	"git.ninjavan.co/3pl/errors/fplerror"
	"git.ninjavan.co/3pl/handlers/async_request"
	"git.ninjavan.co/3pl/handlers/batch_order_request"
	"git.ninjavan.co/3pl/handlers/order/order_interface"
	mocks2 "git.ninjavan.co/3pl/handlers/order_creation/mocks"
	"git.ninjavan.co/3pl/handlers/order_creation/order_creation_interface"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	mocks1 "git.ninjavan.co/3pl/repositories/mocks"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/utils"
)

func Test_batchOrderCreator_getBatchRequestItemsReturn(t *testing.T) {
	type fields struct {
		orderCreator                   order_creation_interface.OrderCreator
		batchOrderRequestRepo          repo_interface.BatchOrderRequest
		batchAggregator                batch_order_request.BatchAggregator
		orderRequestRepo               repo_interface.OrderRequestRepository
		parcelRepo                     repo_interface.ParcelRepository
		parcelItemRepo                 repo_interface.ParcelItemRepository
		productRepo                    repo_interface.ProductRepository
		internationalReturnServiceRepo repo_interface.InternationalReturnServices
		shipperRepo                    repo_interface.ShipperRepositoryInterface
		trackingIdGenerator            order_creation_interface.TrackingIDGenerator
		documentsRepo                  repo_interface.DocumentsRepository
		orderDeleter                   order_interface.Deleter
		asyncRequestCreator            async_request.AsyncRequestCreator2
		asyncTaskRepo                  repo_interface.AsyncTaskRepository
		locker                         *redislock.Client
	}
	type args struct {
		ctx             context.Context
		parcels         []*models.Parcel
		prepareData     prepareReturnData
		req             *order.BatchReturnRequest
		globalShipperId null.Uint
	}
	// Create test data
	ctx := context.Background()

	// Test parcels
	parcel1 := &models.Parcel{
		ID:             1,
		TrackingID:     "TID1",
		ProductID:      null.UintFrom(101),
		ShipperID:      null.UintFrom(201),
		ToName:         null.StringFrom("John Doe"),
		ToAddressLine1: null.StringFrom("123 Main St"),
		ToCountryCode:  null.StringFrom("SG"),
		Metadata:       null.StringFrom("{}"),
	}

	parcel2 := &models.Parcel{
		ID:             2,
		TrackingID:     "TID2",
		ProductID:      null.UintFrom(102),
		ShipperID:      null.UintFrom(202),
		ToName:         null.StringFrom("Jane Smith"),
		ToAddressLine1: null.StringFrom("456 Second St"),
		ToCountryCode:  null.StringFrom("MY"),
		Metadata:       null.StringFrom("{}"),
	}

	// Test parcel items
	parcelItem1 := &models.ParcelItem{
		ID:       1,
		ParcelID: 1,
		Metadata: `{"description":{"String":"Item 1","Valid":true},"unit_value":{"Float64":10.5,"Valid":true},"quantity":{"Int":2,"Valid":true}}`,
	}

	parcelItem2 := &models.ParcelItem{
		ID:       2,
		ParcelID: 2,
		Metadata: `{"description":{"String":"Item 2","Valid":true},"unit_value":{"Float64":20.5,"Valid":true},"quantity":{"Int":1,"Valid":true}}`,
	}

	// Test products
	product1 := &models.Product{
		ID:                 101,
		Name:               "Product 1",
		OriginCountry:      "SG",
		DestinationCountry: "MY",
	}

	product2 := &models.Product{
		ID:                 102,
		Name:               "Product 2",
		OriginCountry:      "MY",
		DestinationCountry: "SG",
	}

	// Test shippers
	shipper1 := &models.Shipper{
		ID:       201,
		Name:     null.StringFrom("Shipper 1"),
		Country:  null.StringFrom("SG"),
		RemoteID: null.UintFrom(301),
	}

	shipper2 := &models.Shipper{
		ID:       202,
		Name:     null.StringFrom("Shipper 2"),
		Country:  null.StringFrom("MY"),
		RemoteID: null.UintFrom(302),
	}

	// Test request
	testReq := &order.BatchReturnRequest{
		RequestID:         "REQ123",
		OriginTrackingIds: []string{"TID1", "TID2"},
		To: order.ExternalFromAddress{
			Name:         "Return Center",
			AddressLine1: "789 Return St",
			CountryCode:  "US",
		},
	}

	// Test data maps
	parcelItemsByParcelId := map[uint64]models.ParcelItemSlice{
		1: {parcelItem1},
		2: {parcelItem2},
	}

	productsById := map[uint]*models.Product{
		101: product1,
		102: product2,
	}

	shippersById := map[uint]*models.Shipper{
		201: shipper1,
		202: shipper2,
	}

	servicesByLane := map[string]map[string]string{
		"MY": {
			"SG": "SVC-MY-SG",
		},
		"SG": {
			"MY": "SVC-SG-MY",
		},
	}

	// Prepare data struct
	prepareData := prepareReturnData{
		parcelItemsByParcelId: parcelItemsByParcelId,
		productsById:          productsById,
		shippersById:          shippersById,
		servicesByLane:        servicesByLane,
	}

	// Expected batch request items
	expectedBatchItem1 := &order.BatchRequestItem{
		BatchSequenceNo: 1,
		BatchExternalRequest: order.BatchExternalRequest{
			Source: order.SourceDashKeyBoard,
			ExternalRequest: order.ExternalRequest{
				RequestedTrackingID: "RETURN-TID1",
				Type:                null.StringFrom(parcelConst.InternationalReturn.String()).Ptr(),
				ServiceCode:         "SVC-MY-SG",
				GlobalShipperID:     100,
			},
		},
	}

	expectedBatchItem2 := &order.BatchRequestItem{
		BatchSequenceNo: 2,
		BatchExternalRequest: order.BatchExternalRequest{
			Source: order.SourceDashKeyBoard,
			ExternalRequest: order.ExternalRequest{
				RequestedTrackingID: "RETURN-TID2",
				Type:                null.StringFrom(parcelConst.InternationalReturn.String()).Ptr(),
				ServiceCode:         "SVC-SG-MY",
				GlobalShipperID:     100,
			},
		},
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*order.BatchRequestItem
		wantErr error
	}{
		{
			name: "successful creation of batch request items",
			fields: fields{
				trackingIdGenerator: func() order_creation_interface.TrackingIDGenerator {
					r := mocks2.NewMockTrackingIDGenerator(ctrl)
					// First call for parcel1
					r.EXPECT().GenerateFulfillmentTID(
						gomock.Any(),
						"SG",      // shipper1.Country
						uint(301), // shipper1.RemoteID
						"TID1",    // parcel1.TrackingID,
						false,     // req.IsCorporateInternationalReturn()
					).Return("RETURN-TID1", nil)
					// Second call for parcel2
					r.EXPECT().GenerateFulfillmentTID(
						gomock.Any(),
						"MY",      // shipper2.Country
						uint(302), // shipper2.RemoteID
						"TID2",    // parcel2.TrackingID
						false,     // req.IsCorporateInternationalReturn()
					).Return("RETURN-TID2", nil)
					return r
				}(),
			},
			args: args{
				ctx:             ctx,
				parcels:         []*models.Parcel{parcel1, parcel2},
				prepareData:     prepareData,
				req:             testReq,
				globalShipperId: null.UintFrom(100),
			},
			want:    []*order.BatchRequestItem{expectedBatchItem1, expectedBatchItem2},
			wantErr: nil,
		},
		{
			name: "error generating tracking ID",
			fields: fields{
				trackingIdGenerator: func() order_creation_interface.TrackingIDGenerator {
					r := mocks2.NewMockTrackingIDGenerator(ctrl)
					r.EXPECT().GenerateFulfillmentTID(
						gomock.Any(),
						gomock.Any(),
						gomock.Any(),
						gomock.Any(),
						gomock.Any(),
					).Return("", fplerror.ErrInternal.NewWithoutStack("failed to generate tracking ID"))
					return r
				}(),
			},
			args: args{
				ctx:             ctx,
				parcels:         []*models.Parcel{parcel1},
				prepareData:     prepareData,
				req:             testReq,
				globalShipperId: null.UintFrom(100),
			},
			want:    nil,
			wantErr: fplerror.ErrInternal.NewWithoutStack("failed to generate tracking ID"),
		},
		{
			name:   "empty parcels list",
			fields: fields{},
			args: args{
				ctx:             ctx,
				parcels:         []*models.Parcel{},
				prepareData:     prepareData,
				req:             testReq,
				globalShipperId: null.UintFrom(100),
			},
			want:    []*order.BatchRequestItem{},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &batchOrderCreator{
				orderCreator:                   tt.fields.orderCreator,
				batchOrderRequestRepo:          tt.fields.batchOrderRequestRepo,
				batchAggregator:                tt.fields.batchAggregator,
				orderRequestRepo:               tt.fields.orderRequestRepo,
				parcelRepo:                     tt.fields.parcelRepo,
				parcelItemRepo:                 tt.fields.parcelItemRepo,
				productRepo:                    tt.fields.productRepo,
				internationalReturnServiceRepo: tt.fields.internationalReturnServiceRepo,
				shipperRepo:                    tt.fields.shipperRepo,
				trackingIdGenerator:            tt.fields.trackingIdGenerator,
				documentsRepo:                  tt.fields.documentsRepo,
				orderDeleter:                   tt.fields.orderDeleter,
				asyncRequestCreator:            tt.fields.asyncRequestCreator,
				asyncTaskRepo:                  tt.fields.asyncTaskRepo,
				locker:                         tt.fields.locker,
			}
			got, err := c.getBatchRequestItemsReturn(tt.args.ctx, tt.args.parcels, tt.args.prepareData, tt.args.req, tt.args.globalShipperId)
			if tt.wantErr != nil {
				require.Error(t, err)
				assert.Equal(t, tt.wantErr.Error(), err.Error())
				return
			}
			require.NoError(t, err)

			if len(tt.want) == 0 {
				assert.Empty(t, got)
				return
			}

			assert.Equal(t, len(tt.want), len(got))

			for i, item := range tt.want {
				if i < len(got) {
					assert.Equal(t, item.BatchSequenceNo, got[i].BatchSequenceNo)
					assert.Equal(t, item.Source, got[i].Source)
					assert.Equal(t, item.RequestedTrackingID, got[i].RequestedTrackingID)
					assert.Equal(t, item.ServiceCode, got[i].ServiceCode)
					assert.Equal(t, item.GlobalShipperID, got[i].GlobalShipperID)
					if item.Type != nil && got[i].Type != nil {
						assert.Equal(t, *item.Type, *got[i].Type)
					}
				}
			}
		})
	}
}

func Test_batchOrderCreator_getParcelItemsByParcelIds(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type fields struct {
		orderCreator                   order_creation_interface.OrderCreator
		batchOrderRequestRepo          repo_interface.BatchOrderRequest
		batchAggregator                batch_order_request.BatchAggregator
		orderRequestRepo               repo_interface.OrderRequestRepository
		parcelRepo                     repo_interface.ParcelRepository
		parcelItemRepo                 repo_interface.ParcelItemRepository
		productRepo                    repo_interface.ProductRepository
		internationalReturnServiceRepo repo_interface.InternationalReturnServices
		shipperRepo                    repo_interface.ShipperRepositoryInterface
		trackingIdGenerator            order_creation_interface.TrackingIDGenerator
		documentsRepo                  repo_interface.DocumentsRepository
		orderDeleter                   order_interface.Deleter
		asyncRequestCreator            async_request.AsyncRequestCreator2
		asyncTaskRepo                  repo_interface.AsyncTaskRepository
		locker                         *redislock.Client
	}

	type args struct {
		ctx       context.Context
		parcelIds []uint64
	}

	// Create test data
	ctx := context.Background()

	// Test parcel items
	parcelItem1 := &models.ParcelItem{
		ID:       1,
		ParcelID: 101,
		Metadata: `{"description":{"String":"Item 1","Valid":true},"unit_value":{"Float64":10.5,"Valid":true},"quantity":{"Int":2,"Valid":true}}`,
	}

	parcelItem2 := &models.ParcelItem{
		ID:       2,
		ParcelID: 101,
		Metadata: `{"description":{"String":"Item 2","Valid":true},"unit_value":{"Float64":20.5,"Valid":true},"quantity":{"Int":1,"Valid":true}}`,
	}

	parcelItem3 := &models.ParcelItem{
		ID:       3,
		ParcelID: 102,
		Metadata: `{"description":{"String":"Item 3","Valid":true},"unit_value":{"Float64":15.0,"Valid":true},"quantity":{"Int":3,"Valid":true}}`,
	}

	// Expected results
	expectedParcelItems := models.ParcelItemSlice{parcelItem1, parcelItem2, parcelItem3}
	expectedGroupedItems := map[uint64]models.ParcelItemSlice{
		101: {parcelItem1, parcelItem2},
		102: {parcelItem3},
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[uint64]models.ParcelItemSlice
		wantErr error
	}{
		{
			name: "successful retrieval of parcel items",
			fields: fields{
				parcelItemRepo: func() repo_interface.ParcelItemRepository {
					r := mocks1.NewMockParcelItemRepository(ctrl)
					r.EXPECT().GetListWithCtx(
						gomock.Any(),
						gomock.Any(),
					).Return(expectedParcelItems, nil)
					return r
				}(),
			},
			args: args{
				ctx:       ctx,
				parcelIds: []uint64{101, 102},
			},
			want:    expectedGroupedItems,
			wantErr: nil,
		},
		{
			name: "empty parcel IDs list",
			fields: fields{
				parcelItemRepo: func() repo_interface.ParcelItemRepository {
					r := mocks1.NewMockParcelItemRepository(ctrl)
					r.EXPECT().GetListWithCtx(
						gomock.Any(),
						gomock.Any(),
					).Return(models.ParcelItemSlice{}, nil)
					return r
				}(),
			},
			args: args{
				ctx:       ctx,
				parcelIds: []uint64{},
			},
			want:    map[uint64]models.ParcelItemSlice{},
			wantErr: nil,
		},
		{
			name: "database error",
			fields: fields{
				parcelItemRepo: func() repo_interface.ParcelItemRepository {
					var nilParcelItems models.ParcelItemSlice
					r := mocks1.NewMockParcelItemRepository(ctrl)
					r.EXPECT().GetListWithCtx(
						gomock.Any(),
						gomock.Any(),
					).Return(nilParcelItems, sql.ErrConnDone)
					return r
				}(),
			},
			args: args{
				ctx:       ctx,
				parcelIds: []uint64{101, 102},
			},
			want:    nil,
			wantErr: sql.ErrConnDone,
		},
		{
			name: "no items found for parcel IDs",
			fields: fields{
				parcelItemRepo: func() repo_interface.ParcelItemRepository {
					r := mocks1.NewMockParcelItemRepository(ctrl)
					r.EXPECT().GetListWithCtx(
						gomock.Any(),
						gomock.Any(),
					).Return(models.ParcelItemSlice{}, nil)
					return r
				}(),
			},
			args: args{
				ctx:       ctx,
				parcelIds: []uint64{999, 1000},
			},
			want:    map[uint64]models.ParcelItemSlice{},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &batchOrderCreator{
				orderCreator:                   tt.fields.orderCreator,
				batchOrderRequestRepo:          tt.fields.batchOrderRequestRepo,
				batchAggregator:                tt.fields.batchAggregator,
				orderRequestRepo:               tt.fields.orderRequestRepo,
				parcelRepo:                     tt.fields.parcelRepo,
				parcelItemRepo:                 tt.fields.parcelItemRepo,
				productRepo:                    tt.fields.productRepo,
				internationalReturnServiceRepo: tt.fields.internationalReturnServiceRepo,
				shipperRepo:                    tt.fields.shipperRepo,
				trackingIdGenerator:            tt.fields.trackingIdGenerator,
				documentsRepo:                  tt.fields.documentsRepo,
				orderDeleter:                   tt.fields.orderDeleter,
				asyncRequestCreator:            tt.fields.asyncRequestCreator,
				asyncTaskRepo:                  tt.fields.asyncTaskRepo,
				locker:                         tt.fields.locker,
			}
			got, err := c.getParcelItemsByParcelIds(tt.args.ctx, tt.args.parcelIds)

			if tt.wantErr != nil {
				require.Error(t, err)
				assert.Equal(t, tt.wantErr.Error(), err.Error())
				return
			}

			require.NoError(t, err)
			assert.Equalf(t, tt.want, got, "getParcelItemsByParcelIds(%v, %v)", tt.args.ctx, tt.args.parcelIds)
		})
	}
}

func Test_batchOrderCreator_getProductsById(t *testing.T) {
	type fields struct {
		orderCreator                   order_creation_interface.OrderCreator
		batchOrderRequestRepo          repo_interface.BatchOrderRequest
		batchAggregator                batch_order_request.BatchAggregator
		orderRequestRepo               repo_interface.OrderRequestRepository
		parcelRepo                     repo_interface.ParcelRepository
		parcelItemRepo                 repo_interface.ParcelItemRepository
		productRepo                    repo_interface.ProductRepository
		internationalReturnServiceRepo repo_interface.InternationalReturnServices
		shipperRepo                    repo_interface.ShipperRepositoryInterface
		trackingIdGenerator            order_creation_interface.TrackingIDGenerator
		documentsRepo                  repo_interface.DocumentsRepository
		orderDeleter                   order_interface.Deleter
		asyncRequestCreator            async_request.AsyncRequestCreator2
		asyncTaskRepo                  repo_interface.AsyncTaskRepository
		locker                         *redislock.Client
	}
	type args struct {
		ctx     context.Context
		parcels []*models.Parcel
	}
	// Create test data
	ctx := context.Background()

	// Test parcels
	parcel1 := &models.Parcel{
		ID:         1,
		ProductID:  null.UintFrom(101),
		TrackingID: "TID1",
	}

	parcel2 := &models.Parcel{
		ID:         2,
		ProductID:  null.UintFrom(102),
		TrackingID: "TID2",
	}

	parcel3 := &models.Parcel{
		ID:         3,
		ProductID:  null.UintFrom(101), // Same product as parcel1
		TrackingID: "TID3",
	}

	parcelWithoutProduct := &models.Parcel{
		ID:         4,
		ProductID:  null.Uint{}, // No product ID
		TrackingID: "TID4",
	}

	// Test products
	product1 := &models.Product{
		ID:                 1,
		Name:               "Product 1",
		OriginCountry:      "SG",
		DestinationCountry: "MY",
	}

	product2 := &models.Product{
		ID:                 2,
		Name:               "Product 2",
		OriginCountry:      "MY",
		DestinationCountry: "SG",
	}

	// Expected results
	expectedProducts := models.ProductSlice{product1, product2}
	expectedProductsMap := map[uint]*models.Product{
		1: product1,
		2: product2,
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[uint]*models.Product
		wantErr error
	}{
		{
			name: "successful retrieval of products",
			fields: fields{
				productRepo: func() repo_interface.ProductRepository {
					r := mocks1.NewMockProductRepository(ctrl)
					r.EXPECT().GetListWithCtx(
						gomock.Any(),
						gomock.Any(),
					).Return(expectedProducts, nil)
					return r
				}(),
			},
			args: args{
				ctx:     ctx,
				parcels: []*models.Parcel{parcel1, parcel2, parcel3},
			},
			want:    expectedProductsMap,
			wantErr: nil,
		},
		{
			name: "empty parcels list",
			fields: fields{
				productRepo: func() repo_interface.ProductRepository {
					r := mocks1.NewMockProductRepository(ctrl)
					r.EXPECT().GetListWithCtx(
						gomock.Any(),
						gomock.Any(),
					).Return(models.ProductSlice{}, nil)
					return r
				}(),
			},
			args: args{
				ctx:     ctx,
				parcels: []*models.Parcel{},
			},
			want:    map[uint]*models.Product{},
			wantErr: nil,
		},
		{
			name: "database error",
			fields: fields{
				productRepo: func() repo_interface.ProductRepository {
					var nilProducts models.ProductSlice
					r := mocks1.NewMockProductRepository(ctrl)
					r.EXPECT().GetListWithCtx(
						gomock.Any(),
						gomock.Any(),
					).Return(nilProducts, sql.ErrConnDone)
					return r
				}(),
			},
			args: args{
				ctx:     ctx,
				parcels: []*models.Parcel{parcel1, parcel2},
			},
			want:    nil,
			wantErr: sql.ErrConnDone,
		},
		{
			name: "parcel without product ID",
			fields: fields{
				productRepo: func() repo_interface.ProductRepository {
					r := mocks1.NewMockProductRepository(ctrl)
					r.EXPECT().GetListWithCtx(
						gomock.Any(),
						gomock.Any(),
					).Return(models.ProductSlice{}, nil)
					return r
				}(),
			},
			args: args{
				ctx:     ctx,
				parcels: []*models.Parcel{parcelWithoutProduct},
			},
			want:    map[uint]*models.Product{},
			wantErr: nil,
		},
		{
			name: "no products found for product IDs",
			fields: fields{
				productRepo: func() repo_interface.ProductRepository {
					r := mocks1.NewMockProductRepository(ctrl)
					r.EXPECT().GetListWithCtx(
						gomock.Any(),
						gomock.Any(),
					).Return(models.ProductSlice{}, nil)
					return r
				}(),
			},
			args: args{
				ctx:     ctx,
				parcels: []*models.Parcel{parcel1, parcel2},
			},
			want:    map[uint]*models.Product{},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &batchOrderCreator{
				orderCreator:                   tt.fields.orderCreator,
				batchOrderRequestRepo:          tt.fields.batchOrderRequestRepo,
				batchAggregator:                tt.fields.batchAggregator,
				orderRequestRepo:               tt.fields.orderRequestRepo,
				parcelRepo:                     tt.fields.parcelRepo,
				parcelItemRepo:                 tt.fields.parcelItemRepo,
				productRepo:                    tt.fields.productRepo,
				internationalReturnServiceRepo: tt.fields.internationalReturnServiceRepo,
				shipperRepo:                    tt.fields.shipperRepo,
				trackingIdGenerator:            tt.fields.trackingIdGenerator,
				documentsRepo:                  tt.fields.documentsRepo,
				orderDeleter:                   tt.fields.orderDeleter,
				asyncRequestCreator:            tt.fields.asyncRequestCreator,
				asyncTaskRepo:                  tt.fields.asyncTaskRepo,
				locker:                         tt.fields.locker,
			}
			got, err := c.getProductsById(tt.args.ctx, tt.args.parcels)
			if tt.wantErr != nil {
				require.Error(t, err)
				assert.Equal(t, tt.wantErr.Error(), err.Error())
				return
			}
			assert.Equalf(t, tt.want, got, "getProductsById(%v, %v)", tt.args.ctx, tt.args.parcels)
		})
	}
}

func Test_modelParcelToReturnBatchItem(t *testing.T) {
	t.Parallel()

	type args struct {
		parcel           *models.Parcel
		parcelItems      []*models.ParcelItem
		req              *order.BatchReturnRequest
		returnTrackingId string
		serviceCode      string
		globalShipperId  uint
		sequenceNo       uint16
	}

	// Create test metadata
	parcelMetadata := repo_interface.ParcelMetadata{
		ParcelDetails: models.ParcelDetails{
			Weight:             null.Float64From(1.5).Ptr(),
			ActualWeight:       null.Float64From(1.2).Ptr(),
			BatteryPacking:     1,
			BatteryType:        2,
			IsRelabel:          true,
			OriginCountry:      null.StringFrom("SG").Ptr(),
			CustomsCurrency:    null.StringFrom("SGD").Ptr(),
			Value:              null.Float64From(100.0).Ptr(),
			CustomsDescription: null.StringFrom("Test description").Ptr(),
			TaxID:              null.StringFrom("TAX123").Ptr(),
		},
	}
	parcelMetadataJSON, _ := json.Marshal(parcelMetadata)

	// Create test parcel item metadata
	parcelItemMetadata := repo_interface.ParcelItemMetadata{
		Description:   null.StringFrom("Item 1"),
		UnitValue:     null.Float64From(10.5),
		Quantity:      null.IntFrom(2),
		HSCode:        null.UintFrom(123456),
		OriginCountry: null.StringFrom("SG"),
		UnitWeight:    null.Float64From(0.5),
		GoodsCurrency: null.StringFrom("SGD"),
	}
	parcelItemMetadataJSON, _ := json.Marshal(parcelItemMetadata)

	// Create test data
	testParcel := &models.Parcel{
		ID:              1,
		SourceOrderID:   null.StringFrom("SO123"),
		TrackingID:      "TID123",
		ToName:          null.StringFrom("John Doe"),
		ToAddressLine1:  null.StringFrom("123 Main St"),
		ToAddressLine2:  null.StringFrom("Apt 4B"),
		ToAddressLine3:  null.StringFrom("Building C"),
		ToAddressLine4:  null.StringFrom("Block 2"),
		ToCity:          null.StringFrom("Singapore"),
		ToStateProvince: null.StringFrom("Central"),
		ToCountryCode:   null.StringFrom("SG"),
		ToPostcode:      null.StringFrom("123456"),
		ToContactNumber: null.StringFrom("12345678"),
		ToContactEmail:  null.StringFrom("<EMAIL>"),
		Metadata:        null.StringFrom(string(parcelMetadataJSON)),
	}

	testParcelItems := []*models.ParcelItem{
		{
			ID:       1,
			ParcelID: 1,
			Metadata: string(parcelItemMetadataJSON),
		},
	}

	testToAddress := order.ExternalFromAddress{
		Name:          "Jane Smith",
		AddressLine1:  "456 Second St",
		AddressLine2:  null.StringFrom("Suite 7C").Ptr(),
		AddressLine3:  null.StringFrom("Tower B").Ptr(),
		AddressLine4:  null.StringFrom("Zone 3").Ptr(),
		City:          null.StringFrom("New York").Ptr(),
		StateProvince: null.StringFrom("NY").Ptr(),
		CountryCode:   "US",
		PostCode:      null.StringFrom("10001").Ptr(),
		ContactNumber: null.StringFrom("87654321").Ptr(),
		ContactEmail:  null.StringFrom("<EMAIL>").Ptr(),
	}

	tests := []struct {
		name string
		args args
		want *order.BatchRequestItem
	}{
		{
			name: "complete parcel with items",
			args: args{
				parcel:      testParcel,
				parcelItems: testParcelItems,
				req: &order.BatchReturnRequest{
					To: testToAddress,
				},
				returnTrackingId: "RETURN123",
				serviceCode:      "SVC001",
				globalShipperId:  1,
				sequenceNo:       1,
			},
			want: &order.BatchRequestItem{
				BatchSequenceNo: 1,
				BatchExternalRequest: order.BatchExternalRequest{
					Source: order.SourceDashKeyBoard,
					ExternalRequest: order.ExternalRequest{
						SourceOrderID:       null.StringFrom("SO123").Ptr(),
						SourceReferenceID:   null.StringFrom("TID123").Ptr(),
						RequestedTrackingID: "RETURN123",
						Type:                null.StringFrom(parcelConst.InternationalReturn.String()).Ptr(),
						GlobalShipperID:     1,
						ServiceCode:         "SVC001",
						From: order.ExternalFromAddress{
							Name:          "John Doe",
							AddressLine1:  "123 Main St",
							AddressLine2:  null.StringFrom("Apt 4B").Ptr(),
							AddressLine3:  null.StringFrom("Building C").Ptr(),
							AddressLine4:  null.StringFrom("Block 2").Ptr(),
							City:          null.StringFrom("Singapore").Ptr(),
							StateProvince: null.StringFrom("Central").Ptr(),
							CountryCode:   "SG",
							PostCode:      null.StringFrom("123456").Ptr(),
							ContactNumber: null.StringFrom("12345678").Ptr(),
							ContactEmail:  null.StringFrom("<EMAIL>").Ptr(),
						},
						To: order.ExternalToAddress{
							Name:          "Jane Smith",
							AddressLine1:  "456 Second St",
							AddressLine2:  null.StringFrom("Suite 7C").Ptr(),
							AddressLine3:  null.StringFrom("Tower B").Ptr(),
							AddressLine4:  null.StringFrom("Zone 3").Ptr(),
							City:          null.StringFrom("New York").Ptr(),
							StateProvince: null.StringFrom("NY").Ptr(),
							CountryCode:   "US",
							PostCode:      null.StringFrom("10001").Ptr(),
							ContactNumber: null.StringFrom("87654321").Ptr(),
							ContactEmail:  null.StringFrom("<EMAIL>").Ptr(),
						},
						ParcelDetails: &order.ExternalParcelDetails{
							Weight:             null.Float64From(1.5).Ptr(),
							ActualWeight:       null.Float64From(1.2).Ptr(),
							BatteryPacking:     1,
							BatteryType:        2,
							IsRelabel:          null.BoolFrom(true).Ptr(),
							OriginCountry:      null.StringFrom("SG").Ptr(),
							CustomsCurrency:    null.StringFrom("SGD").Ptr(),
							Value:              null.Float64From(100.0).Ptr(),
							CustomsDescription: null.StringFrom("Test description").Ptr(),
							TaxID:              null.StringFrom("TAX123").Ptr(),
						},
						Items: []*order.ParcelItem{
							{
								Description:   null.StringFrom("Item 1").Ptr(),
								UnitValue:     null.Float64From(10.5).Ptr(),
								Quantity:      null.IntFrom(2).Ptr(),
								HSCode:        null.UintFrom(123456).Ptr(),
								OriginCountry: null.StringFrom("SG").Ptr(),
								UnitWeight:    null.Float64From(0.5).Ptr(),
								GoodsCurrency: null.StringFrom("SGD").Ptr(),
							},
						},
					},
				},
			},
		},
		{
			name: "parcel without items",
			args: args{
				parcel:      testParcel,
				parcelItems: []*models.ParcelItem{},
				req: &order.BatchReturnRequest{
					To: testToAddress,
				},
				returnTrackingId: "RETURN123",
				serviceCode:      "SVC001",
				globalShipperId:  1,
				sequenceNo:       1,
			},
			want: &order.BatchRequestItem{
				BatchSequenceNo: 1,
				BatchExternalRequest: order.BatchExternalRequest{
					Source: order.SourceDashKeyBoard,
					ExternalRequest: order.ExternalRequest{
						SourceOrderID:       null.StringFrom("SO123").Ptr(),
						SourceReferenceID:   null.StringFrom("TID123").Ptr(),
						RequestedTrackingID: "RETURN123",
						Type:                null.StringFrom(parcelConst.InternationalReturn.String()).Ptr(),
						GlobalShipperID:     1,
						ServiceCode:         "SVC001",
						From: order.ExternalFromAddress{
							Name:          "John Doe",
							AddressLine1:  "123 Main St",
							AddressLine2:  null.StringFrom("Apt 4B").Ptr(),
							AddressLine3:  null.StringFrom("Building C").Ptr(),
							AddressLine4:  null.StringFrom("Block 2").Ptr(),
							City:          null.StringFrom("Singapore").Ptr(),
							StateProvince: null.StringFrom("Central").Ptr(),
							CountryCode:   "SG",
							PostCode:      null.StringFrom("123456").Ptr(),
							ContactNumber: null.StringFrom("12345678").Ptr(),
							ContactEmail:  null.StringFrom("<EMAIL>").Ptr(),
						},
						To: order.ExternalToAddress{
							Name:          "Jane Smith",
							AddressLine1:  "456 Second St",
							AddressLine2:  null.StringFrom("Suite 7C").Ptr(),
							AddressLine3:  null.StringFrom("Tower B").Ptr(),
							AddressLine4:  null.StringFrom("Zone 3").Ptr(),
							City:          null.StringFrom("New York").Ptr(),
							StateProvince: null.StringFrom("NY").Ptr(),
							CountryCode:   "US",
							PostCode:      null.StringFrom("10001").Ptr(),
							ContactNumber: null.StringFrom("87654321").Ptr(),
							ContactEmail:  null.StringFrom("<EMAIL>").Ptr(),
						},
						ParcelDetails: &order.ExternalParcelDetails{
							Weight:             null.Float64From(1.5).Ptr(),
							ActualWeight:       null.Float64From(1.2).Ptr(),
							BatteryPacking:     1,
							BatteryType:        2,
							IsRelabel:          null.BoolFrom(true).Ptr(),
							OriginCountry:      null.StringFrom("SG").Ptr(),
							CustomsCurrency:    null.StringFrom("SGD").Ptr(),
							Value:              null.Float64From(100.0).Ptr(),
							CustomsDescription: null.StringFrom("Test description").Ptr(),
							TaxID:              null.StringFrom("TAX123").Ptr(),
						},
						Items: []*order.ParcelItem{},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := modelParcelToReturnBatchItem(
				tt.args.parcel,
				tt.args.parcelItems,
				tt.args.req,
				tt.args.returnTrackingId,
				tt.args.serviceCode,
				tt.args.globalShipperId,
				tt.args.sequenceNo,
			)

			// Compare important fields individually for better error messages
			assert.Equal(t, tt.want.BatchSequenceNo, result.BatchSequenceNo)
			assert.Equal(t, tt.want.Source, result.Source)

			// Compare source order ID
			if tt.want.SourceOrderID != nil {
				assert.Equal(t, *tt.want.SourceOrderID, *result.SourceOrderID)
			} else {
				assert.Nil(t, result.SourceOrderID)
			}

			// Compare source reference ID
			if tt.want.SourceReferenceID != nil {
				assert.Equal(t, *tt.want.SourceReferenceID, *result.SourceReferenceID)
			} else {
				assert.Nil(t, result.SourceReferenceID)
			}

			assert.Equal(t, tt.want.RequestedTrackingID, result.RequestedTrackingID)
			assert.Equal(t, tt.want.GlobalShipperID, result.GlobalShipperID)
			assert.Equal(t, tt.want.ServiceCode, result.ServiceCode)

			// Compare From address
			assert.Equal(t, tt.want.From.Name, result.From.Name)
			assert.Equal(t, tt.want.From.AddressLine1, result.From.AddressLine1)
			assert.Equal(t, tt.want.From.CountryCode, result.From.CountryCode)

			// Compare To address
			assert.Equal(t, tt.want.To.Name, result.To.Name)
			assert.Equal(t, tt.want.To.AddressLine1, result.To.AddressLine1)
			assert.Equal(t, tt.want.To.CountryCode, result.To.CountryCode)

			// Compare ParcelDetails
			assert.Equal(t, tt.want.ParcelDetails.Weight, result.ParcelDetails.Weight)
			assert.Equal(t, tt.want.ParcelDetails.ActualWeight, result.ParcelDetails.ActualWeight)
			assert.Equal(t, tt.want.ParcelDetails.OriginCountry, result.ParcelDetails.OriginCountry)

			// Compare Items
			assert.Equal(t, len(tt.want.Items), len(result.Items))
			if len(tt.want.Items) > 0 && len(result.Items) > 0 {
				assert.Equal(t, *tt.want.Items[0].Description, *result.Items[0].Description)
				assert.Equal(t, *tt.want.Items[0].UnitValue, *result.Items[0].UnitValue)
				assert.Equal(t, *tt.want.Items[0].Quantity, *result.Items[0].Quantity)
			}
		})
	}
}

func TestValidateAllowReturn(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name       string
		parcels    []*models.Parcel
		originTids []string
		partnerId  uint64
		wantErr    error
	}{
		{
			name: "valid parcels",
			parcels: []*models.Parcel{
				{ID: 1, TrackingID: "TID1", PartnerID: null.Uint64From(100), Metadata: null.StringFrom("{}")},
				{ID: 2, TrackingID: "TID2", PartnerID: null.Uint64From(100), Metadata: null.StringFrom("{}")},
			},
			originTids: []string{"TID1", "TID2"},
			partnerId:  100,
			wantErr:    nil,
		},
		{
			name: "partner id mismatch",
			parcels: []*models.Parcel{
				{ID: 1, TrackingID: "TID1", PartnerID: null.Uint64From(100), Metadata: null.StringFrom("{}")},
				{ID: 2, TrackingID: "TID2", PartnerID: null.Uint64From(200), Metadata: null.StringFrom("{}")},
			},
			originTids: []string{"TID1", "TID2"},
			partnerId:  100,
			wantErr:    fplerror.ErrBadRequest.NewWithoutStack("tracking ids [TID2] are not belong to partner 100"),
		},
		{
			name: "tracking ids not found",
			parcels: []*models.Parcel{
				{ID: 1, TrackingID: "TID1", PartnerID: null.Uint64From(100), Metadata: null.StringFrom("{}")},
			},
			originTids: []string{"TID1", "TID2", "TID3"},
			partnerId:  100,
			wantErr:    fplerror.ErrBadRequest.NewWithoutStack("tracking ids [TID2 TID3] are not found"),
		},
		{
			name: "international return parcels",
			parcels: []*models.Parcel{
				{
					ID:         1,
					TrackingID: "TID1",
					PartnerID:  null.Uint64From(100),
					Metadata: null.StringFrom(utils.JsonMarshalStrIgnoreError(repo_interface.ParcelMetadata{
						ParcelDetails: models.ParcelDetails{
							RoutingType: fpl.RoutingType_RETURN_TO_ORIGIN,
						},
					})), // RETURN_TO_ORIGIN
				},
			},
			originTids: []string{"TID1"},
			partnerId:  100,
			wantErr:    fplerror.ErrBadRequest.NewWithoutStack("tracking ids [TID1] is international return order"),
		},
		{
			name: "multiple partner id mismatches",
			parcels: []*models.Parcel{
				{ID: 1, TrackingID: "TID1", PartnerID: null.Uint64From(100), Metadata: null.StringFrom("{}")},
				{ID: 2, TrackingID: "TID2", PartnerID: null.Uint64From(200), Metadata: null.StringFrom("{}")},
				{ID: 3, TrackingID: "TID3", PartnerID: null.Uint64From(300), Metadata: null.StringFrom("{}")},
			},
			originTids: []string{"TID1", "TID2", "TID3"},
			partnerId:  100,
			wantErr:    fplerror.ErrBadRequest.NewWithoutStack("tracking ids [TID2 TID3] are not belong to partner 100"),
		},
		{
			name: "mixed errors - partner mismatch and return to origin",
			parcels: []*models.Parcel{
				{ID: 1, TrackingID: "TID1", PartnerID: null.Uint64From(100), Metadata: null.StringFrom("{}")},
				{ID: 2, TrackingID: "TID2", PartnerID: null.Uint64From(200), Metadata: null.StringFrom("{}")},
				{
					ID:         3,
					TrackingID: "TID3",
					PartnerID:  null.Uint64From(100),
					Metadata:   null.StringFrom(`{"routing_type":2}`), // RETURN_TO_ORIGIN
				},
			},
			originTids: []string{"TID1", "TID2", "TID3"},
			partnerId:  100,
			wantErr:    fplerror.ErrBadRequest.NewWithoutStack("tracking ids [TID2] are not belong to partner 100"),
		},
		{
			name:       "empty parcels",
			parcels:    []*models.Parcel{},
			originTids: []string{"TID1", "TID2"},
			partnerId:  100,
			wantErr:    fplerror.ErrBadRequest.NewWithoutStack("tracking ids [TID1 TID2] are not found"),
		},
		{
			name: "empty origin tracking IDs",
			parcels: []*models.Parcel{
				{ID: 1, TrackingID: "TID1", PartnerID: null.Uint64From(100), Metadata: null.StringFrom("{}")},
			},
			originTids: []string{},
			partnerId:  100,
			wantErr:    nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateAllowReturn(tt.parcels, tt.originTids, tt.partnerId)

			if tt.wantErr != nil {
				require.Error(t, err)
				assert.Equal(t, tt.wantErr.Error(), err.Error())
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func Test_batchOrderCreator_getShippersById(t *testing.T) {
	type fields struct {
		orderCreator                   order_creation_interface.OrderCreator
		batchOrderRequestRepo          repo_interface.BatchOrderRequest
		batchAggregator                batch_order_request.BatchAggregator
		orderRequestRepo               repo_interface.OrderRequestRepository
		parcelRepo                     repo_interface.ParcelRepository
		parcelItemRepo                 repo_interface.ParcelItemRepository
		productRepo                    repo_interface.ProductRepository
		internationalReturnServiceRepo repo_interface.InternationalReturnServices
		shipperRepo                    repo_interface.ShipperRepositoryInterface
		trackingIdGenerator            order_creation_interface.TrackingIDGenerator
		documentsRepo                  repo_interface.DocumentsRepository
		orderDeleter                   order_interface.Deleter
		asyncRequestCreator            async_request.AsyncRequestCreator2
		asyncTaskRepo                  repo_interface.AsyncTaskRepository
		locker                         *redislock.Client
	}
	type args struct {
		ctx     context.Context
		parcels []*models.Parcel
	}
	// Create test data
	ctx := context.Background()

	// Test parcels
	parcel1 := &models.Parcel{
		ID:         1,
		ShipperID:  null.UintFrom(101),
		TrackingID: "TID1",
	}

	parcel2 := &models.Parcel{
		ID:         2,
		ShipperID:  null.UintFrom(102),
		TrackingID: "TID2",
	}

	parcel3 := &models.Parcel{
		ID:         3,
		ShipperID:  null.UintFrom(101), // Same shipper as parcel1
		TrackingID: "TID3",
	}

	parcelWithoutShipper := &models.Parcel{
		ID:         4,
		ShipperID:  null.Uint{}, // No shipper ID
		TrackingID: "TID4",
	}

	// Test shippers
	shipper1 := &models.Shipper{
		ID:       101,
		Name:     null.StringFrom("Shipper 1"),
		Country:  null.StringFrom("SG"),
		RemoteID: null.UintFrom(301),
	}

	shipper2 := &models.Shipper{
		ID:       102,
		Name:     null.StringFrom("Shipper 2"),
		Country:  null.StringFrom("MY"),
		RemoteID: null.UintFrom(302),
	}

	// Expected results
	expectedShippers := models.ShipperSlice{shipper1, shipper2}
	expectedShippersMap := map[uint]*models.Shipper{
		101: shipper1,
		102: shipper2,
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[uint]*models.Shipper
		wantErr error
	}{
		{
			name: "successful retrieval of shippers",
			fields: fields{
				shipperRepo: func() repo_interface.ShipperRepositoryInterface {
					r := mocks1.NewMockShipperRepositoryInterface(ctrl)
					r.EXPECT().GetList(
						gomock.Any(),
						gomock.Any(),
					).Return(expectedShippers, nil)
					return r
				}(),
			},
			args: args{
				ctx:     ctx,
				parcels: []*models.Parcel{parcel1, parcel2, parcel3},
			},
			want:    expectedShippersMap,
			wantErr: nil,
		},
		{
			name: "empty parcels list",
			fields: fields{
				shipperRepo: func() repo_interface.ShipperRepositoryInterface {
					r := mocks1.NewMockShipperRepositoryInterface(ctrl)
					r.EXPECT().GetList(
						gomock.Any(),
						gomock.Any(),
					).Return(models.ShipperSlice{}, nil)
					return r
				}(),
			},
			args: args{
				ctx:     ctx,
				parcels: []*models.Parcel{},
			},
			want:    map[uint]*models.Shipper{},
			wantErr: nil,
		},
		{
			name: "database error",
			fields: fields{
				shipperRepo: func() repo_interface.ShipperRepositoryInterface {
					r := mocks1.NewMockShipperRepositoryInterface(ctrl)
					r.EXPECT().GetList(
						gomock.Any(),
						gomock.Any(),
					).Return(nil, sql.ErrConnDone)
					return r
				}(),
			},
			args: args{
				ctx:     ctx,
				parcels: []*models.Parcel{parcel1, parcel2},
			},
			want:    nil,
			wantErr: sql.ErrConnDone,
		},
		{
			name: "parcel without shipper ID",
			fields: fields{
				shipperRepo: func() repo_interface.ShipperRepositoryInterface {
					r := mocks1.NewMockShipperRepositoryInterface(ctrl)
					r.EXPECT().GetList(
						gomock.Any(),
						gomock.Any(),
					).Return(models.ShipperSlice{}, nil)
					return r
				}(),
			},
			args: args{
				ctx:     ctx,
				parcels: []*models.Parcel{parcelWithoutShipper},
			},
			want:    map[uint]*models.Shipper{},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &batchOrderCreator{
				orderCreator:                   tt.fields.orderCreator,
				batchOrderRequestRepo:          tt.fields.batchOrderRequestRepo,
				batchAggregator:                tt.fields.batchAggregator,
				orderRequestRepo:               tt.fields.orderRequestRepo,
				parcelRepo:                     tt.fields.parcelRepo,
				parcelItemRepo:                 tt.fields.parcelItemRepo,
				productRepo:                    tt.fields.productRepo,
				internationalReturnServiceRepo: tt.fields.internationalReturnServiceRepo,
				shipperRepo:                    tt.fields.shipperRepo,
				trackingIdGenerator:            tt.fields.trackingIdGenerator,
				documentsRepo:                  tt.fields.documentsRepo,
				orderDeleter:                   tt.fields.orderDeleter,
				asyncRequestCreator:            tt.fields.asyncRequestCreator,
				asyncTaskRepo:                  tt.fields.asyncTaskRepo,
				locker:                         tt.fields.locker,
			}
			got, err := c.getShippersById(tt.args.ctx, tt.args.parcels)
			if tt.wantErr != nil {
				require.Error(t, err)
				assert.Equal(t, tt.wantErr.Error(), err.Error())
				return
			}
			assert.Equalf(t, tt.want, got, "getShippersById(%v, %v)", tt.args.ctx, tt.args.parcels)
		})
	}
}

func Test_batchOrderCreator_getServiceCodeByLane(t *testing.T) {
	type fields struct {
		orderCreator                   order_creation_interface.OrderCreator
		batchOrderRequestRepo          repo_interface.BatchOrderRequest
		batchAggregator                batch_order_request.BatchAggregator
		orderRequestRepo               repo_interface.OrderRequestRepository
		parcelRepo                     repo_interface.ParcelRepository
		parcelItemRepo                 repo_interface.ParcelItemRepository
		productRepo                    repo_interface.ProductRepository
		internationalReturnServiceRepo repo_interface.InternationalReturnServices
		shipperRepo                    repo_interface.ShipperRepositoryInterface
		trackingIdGenerator            order_creation_interface.TrackingIDGenerator
		documentsRepo                  repo_interface.DocumentsRepository
		orderDeleter                   order_interface.Deleter
		asyncRequestCreator            async_request.AsyncRequestCreator2
		asyncTaskRepo                  repo_interface.AsyncTaskRepository
		locker                         *redislock.Client
	}
	type args struct {
		ctx          context.Context
		parcels      []*models.Parcel
		productsById map[uint]*models.Product
	}
	// Create test data
	ctx := context.Background()

	// Test parcels
	parcel1 := &models.Parcel{
		ID:         1,
		ProductID:  null.UintFrom(101),
		TrackingID: "TID1",
	}

	parcel2 := &models.Parcel{
		ID:         2,
		ProductID:  null.UintFrom(102),
		TrackingID: "TID2",
	}

	// Test products
	product1 := &models.Product{
		ID:                 101,
		Name:               "Product 1",
		OriginCountry:      "SG",
		DestinationCountry: "MY",
	}

	product2 := &models.Product{
		ID:                 102,
		Name:               "Product 2",
		OriginCountry:      "MY",
		DestinationCountry: "SG",
	}

	// Test products map
	productsById := map[uint]*models.Product{
		101: product1,
		102: product2,
	}

	// Expected service codes map
	expectedServiceCodes := map[string]map[string]string{
		"MY": {
			"SG": "SVC-MY-SG",
		},
		"SG": {
			"MY": "SVC-SG-MY",
		},
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[string]map[string]string
		wantErr error
	}{
		{
			name: "successful retrieval of service codes",
			fields: fields{
				internationalReturnServiceRepo: func() repo_interface.InternationalReturnServices {
					r := mocks1.NewMockInternationalReturnServices(ctrl)
					r.EXPECT().GetServiceCodesByLanes(
						gomock.Any(),
						[]string{"MY", "SG"}, // currentCountries (destination countries)
						[]string{"SG", "MY"}, // returnCountries (origin countries)
					).Return(expectedServiceCodes, nil).AnyTimes()

					r.EXPECT().GetServiceCodesByLanes(
						gomock.Any(),
						[]string{"SG", "MY"}, // returnCountries (origin countries)
						[]string{"MY", "SG"}, // currentCountries (destination countries)
					).Return(expectedServiceCodes, nil).AnyTimes()
					return r
				}(),
			},
			args: args{
				ctx:          ctx,
				parcels:      []*models.Parcel{parcel1, parcel2},
				productsById: productsById,
			},
			want:    expectedServiceCodes,
			wantErr: nil,
		},
		{
			name: "error from GetServiceCodesByLanes",
			fields: fields{
				internationalReturnServiceRepo: func() repo_interface.InternationalReturnServices {
					r := mocks1.NewMockInternationalReturnServices(ctrl)
					r.EXPECT().GetServiceCodesByLanes(
						gomock.Any(),
						gomock.Any(),
						gomock.Any(),
					).Return(nil, sql.ErrConnDone)
					return r
				}(),
			},
			args: args{
				ctx:          ctx,
				parcels:      []*models.Parcel{parcel1, parcel2},
				productsById: productsById,
			},
			want:    nil,
			wantErr: sql.ErrConnDone,
		},
		{
			name: "lane not configured for international return",
			fields: fields{
				internationalReturnServiceRepo: func() repo_interface.InternationalReturnServices {
					r := mocks1.NewMockInternationalReturnServices(ctrl)
					// Return a service codes map with missing lane
					incompleteServiceCodes := map[string]map[string]string{
						"MY": {
							"SG": "SVC-MY-SG",
						},
						// Missing SG->MY lane
					}
					r.EXPECT().GetServiceCodesByLanes(
						gomock.Any(),
						gomock.Any(),
						gomock.Any(),
					).Return(incompleteServiceCodes, nil)
					return r
				}(),
			},
			args: args{
				ctx:          ctx,
				parcels:      []*models.Parcel{parcel1, parcel2},
				productsById: productsById,
			},
			want:    nil,
			wantErr: fplerror.ErrBadRequest.NewWithoutStack("tracking ids [TID2] lane are not configure for international return"),
		},
		{
			name: "empty parcels list",
			fields: fields{
				internationalReturnServiceRepo: func() repo_interface.InternationalReturnServices {
					r := mocks1.NewMockInternationalReturnServices(ctrl)
					r.EXPECT().GetServiceCodesByLanes(
						gomock.Any(),
						[]string{}, // empty currentCountries
						[]string{}, // empty returnCountries
					).Return(map[string]map[string]string{}, nil)
					return r
				}(),
			},
			args: args{
				ctx:          ctx,
				parcels:      []*models.Parcel{},
				productsById: map[uint]*models.Product{},
			},
			want:    map[string]map[string]string{},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &batchOrderCreator{
				orderCreator:                   tt.fields.orderCreator,
				batchOrderRequestRepo:          tt.fields.batchOrderRequestRepo,
				batchAggregator:                tt.fields.batchAggregator,
				orderRequestRepo:               tt.fields.orderRequestRepo,
				parcelRepo:                     tt.fields.parcelRepo,
				parcelItemRepo:                 tt.fields.parcelItemRepo,
				productRepo:                    tt.fields.productRepo,
				internationalReturnServiceRepo: tt.fields.internationalReturnServiceRepo,
				shipperRepo:                    tt.fields.shipperRepo,
				trackingIdGenerator:            tt.fields.trackingIdGenerator,
				documentsRepo:                  tt.fields.documentsRepo,
				orderDeleter:                   tt.fields.orderDeleter,
				asyncRequestCreator:            tt.fields.asyncRequestCreator,
				asyncTaskRepo:                  tt.fields.asyncTaskRepo,
				locker:                         tt.fields.locker,
			}
			got, err := c.getServiceCodeByLane(tt.args.ctx, tt.args.parcels, tt.args.productsById)
			if tt.wantErr != nil {
				require.Error(t, err)
				assert.Equal(t, tt.wantErr.Error(), err.Error())
				return
			}
			require.NoError(t, err)
			assert.Equal(t, tt.want, got)
		})
	}
}
