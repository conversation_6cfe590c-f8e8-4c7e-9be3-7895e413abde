package order_creation

import (
	"context"
	"fmt"

	"bitbucket.ninjavan.co/cg/datadog-agent---go/nvtracer"

	"git.ninjavan.co/3pl/configs"
	"git.ninjavan.co/3pl/handlers/order_creation/order_creation_interface"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/utils/loggerutils"
)

type LazadaCCOrderCreator interface {
	CreateMMCCOrder(ctx context.Context, partner *models.Partner, parcelReq *order.BaseRequest) (*models.Parcel, error)
}

type lazadaCCOrderCreator struct {
	orderCreator     order_creation_interface.OrderCreator
	orderRequestRepo repo_interface.OrderRequestRepository
	parcelRepo       repo_interface.ParcelRepository
}

func NewLazadaCCOrderCreator() *lazadaCCOrderCreator {
	return &lazadaCCOrderCreator{
		orderCreator:     NewOrderCreator(),
		orderRequestRepo: repositories.NewOrderRequestRepository(),
		parcelRepo:       repositories.NewParcelRepository(),
	}
}

func (l *lazadaCCOrderCreator) CreateMMCCOrder(ctx context.Context, partner *models.Partner, parcelReq *order.BaseRequest) (*models.Parcel, error) {
	ctx, span := nvtracer.CreateSpanFromContext(ctx)
	defer span.Finish()

	orderReqUniqueId := &models.UniqueRequestID{
		RequestID: parcelReq.RequestId,
		IDScheme:  configs.RequestIdSchemeLazadaCCOC.String(),
	}
	txn, err := repositories.BeginTransaction(ctx)
	if err != nil {
		return nil, err
	}
	defer func() {
		err = repositories.ProcessTransaction(txn, err)
		if err != nil {
			loggerutils.Ctx(ctx, "lazada-cc-order-creator").Err(err).Msg("fail to process txn")
		}
	}()

	if err = l.orderRequestRepo.CreateUniqueRequestIdTxn(ctx, orderReqUniqueId, txn); err != nil {
		parcel, _ := l.parcelRepo.GetOneByIdentifier(ctx, repositories.NewParcelIdentifier(partner.ID, *parcelReq.SourceOrderID))
		if parcel != nil {
			return parcel, nil
		}
		return nil, fmt.Errorf("duplicated_order_request_id: %s, %w", parcelReq.RequestId, err)
	}
	parcel, err := l.orderCreator.CreateE2EOrder(ctx, parcelReq, partner)
	if err != nil {
		return nil, err
	}
	return parcel, nil
}
