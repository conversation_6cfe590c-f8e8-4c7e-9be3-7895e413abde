// Code generated by MockGen. DO NOT EDIT.
// Source: ./handlers/order_creation/lazada_mm.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	order "git.ninjavan.co/3pl/httpmodels/order"
	models "git.ninjavan.co/3pl/models"
	gomock "github.com/golang/mock/gomock"
)

// MockLazadaMMOrderCreator is a mock of LazadaMMOrderCreator interface.
type MockLazadaMMOrderCreator struct {
	ctrl     *gomock.Controller
	recorder *MockLazadaMMOrderCreatorMockRecorder
}

// MockLazadaMMOrderCreatorMockRecorder is the mock recorder for MockLazadaMMOrderCreator.
type MockLazadaMMOrderCreatorMockRecorder struct {
	mock *MockLazadaMMOrderCreator
}

// NewMockLazadaMMOrderCreator creates a new mock instance.
func NewMockLazadaMMOrderCreator(ctrl *gomock.Controller) *MockLazadaMMOrderCreator {
	mock := &MockLazadaMMOrderCreator{ctrl: ctrl}
	mock.recorder = &MockLazadaMMOrderCreatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLazadaMMOrderCreator) EXPECT() *MockLazadaMMOrderCreatorMockRecorder {
	return m.recorder
}

// CreateAIDCMMCCBag mocks base method.
func (m *MockLazadaMMOrderCreator) CreateAIDCMMCCBag(ctx context.Context, partner *models.Partner, req *order.LazadaAIDCMMCreateBagRequest) (*models.Parcel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAIDCMMCCBag", ctx, partner, req)
	ret0, _ := ret[0].(*models.Parcel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAIDCMMCCBag indicates an expected call of CreateAIDCMMCCBag.
func (mr *MockLazadaMMOrderCreatorMockRecorder) CreateAIDCMMCCBag(ctx, partner, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAIDCMMCCBag", reflect.TypeOf((*MockLazadaMMOrderCreator)(nil).CreateAIDCMMCCBag), ctx, partner, req)
}

// CreateMMCCBag mocks base method.
func (m *MockLazadaMMOrderCreator) CreateMMCCBag(ctx context.Context, partner *models.Partner, req *order.LazadaMMCreateBagRequest) (*models.Parcel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMMCCBag", ctx, partner, req)
	ret0, _ := ret[0].(*models.Parcel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateMMCCBag indicates an expected call of CreateMMCCBag.
func (mr *MockLazadaMMOrderCreatorMockRecorder) CreateMMCCBag(ctx, partner, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMMCCBag", reflect.TypeOf((*MockLazadaMMOrderCreator)(nil).CreateMMCCBag), ctx, partner, req)
}

// CreateMMCCParcel mocks base method.
func (m *MockLazadaMMOrderCreator) CreateMMCCParcel(ctx context.Context, partner *models.Partner, bigBagID string, mmccParcel *order.BaseRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMMCCParcel", ctx, partner, bigBagID, mmccParcel)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateMMCCParcel indicates an expected call of CreateMMCCParcel.
func (mr *MockLazadaMMOrderCreatorMockRecorder) CreateMMCCParcel(ctx, partner, bigBagID, mmccParcel interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMMCCParcel", reflect.TypeOf((*MockLazadaMMOrderCreator)(nil).CreateMMCCParcel), ctx, partner, bigBagID, mmccParcel)
}

// CreateMMCCParcelV2 mocks base method.
func (m *MockLazadaMMOrderCreator) CreateMMCCParcelAIDC(ctx context.Context, partner *models.Partner, mmccParcel *order.BaseRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMMCCParcelAIDC", ctx, partner, mmccParcel)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateMMCCParcelV2 indicates an expected call of CreateMMCCParcelV2.
func (mr *MockLazadaMMOrderCreatorMockRecorder) CreateMMCCParcelV2(ctx, partner, mmccParcel interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMMCCParcelAIDC", reflect.TypeOf((*MockLazadaMMOrderCreator)(nil).CreateMMCCParcelAIDC), ctx, partner, mmccParcel)
}

// UpdateMMCCBag mocks base method.
func (m *MockLazadaMMOrderCreator) UpdateMMCCBag(ctx context.Context, partner *models.Partner, req *order.LazadaMMUpdateRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMMCCBag", ctx, partner, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateMMCCBag indicates an expected call of UpdateMMCCBag.
func (mr *MockLazadaMMOrderCreatorMockRecorder) UpdateMMCCBag(ctx, partner, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMMCCBag", reflect.TypeOf((*MockLazadaMMOrderCreator)(nil).UpdateMMCCBag), ctx, partner, req)
}
