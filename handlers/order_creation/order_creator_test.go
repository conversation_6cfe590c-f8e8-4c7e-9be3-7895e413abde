package order_creation

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"

	"bitbucket.ninjavan.co/commons/go/apiclients/authapi"
	"bitbucket.ninjavan.co/fpl/4pl-protos/go/fpl"

	"git.ninjavan.co/3pl/configs"
	orderConst "git.ninjavan.co/3pl/configs/orders"
	"git.ninjavan.co/3pl/configs/parcel"
	serviceCfg "git.ninjavan.co/3pl/configs/services"
	vendorConst "git.ninjavan.co/3pl/configs/vendors"
	"git.ninjavan.co/3pl/envs"
	"git.ninjavan.co/3pl/errors/fplerror"
	"git.ninjavan.co/3pl/event_publisher"
	"git.ninjavan.co/3pl/handlers/async_task"
	"git.ninjavan.co/3pl/handlers/integration/base"
	integrationMocks "git.ninjavan.co/3pl/handlers/integration/base/mocks"
	"git.ninjavan.co/3pl/handlers/integration/nvcore"
	orderCreationMocks "git.ninjavan.co/3pl/handlers/order_creation/mocks"
	"git.ninjavan.co/3pl/handlers/order_creation/order_creation_interface"
	serviceInterface "git.ninjavan.co/3pl/handlers/services/service_interface"
	"git.ninjavan.co/3pl/handlers/shipment_parcel/create/mocks"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	repoMocks "git.ninjavan.co/3pl/repositories/mocks"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/utils"
)

func TestNewOrderCreator(t *testing.T) {
	t.Parallel()
	NewOrderCreator()
}

func TestOrderCreator_CreateMMCCParcel(t *testing.T) {
	db := repositories.InitInMemoryDBWithTables()
	defer db.Close()
	t.Run("should_create_parcel_when_there_is_no_duplicate", func(t *testing.T) {
		defer repositories.CleanUpAllTestData(db)
		payload := &order_creation_interface.CreateMMCCParcelPayload{
			Bag: &models.Parcel{
				TrackingID:    "abc",
				ServiceID:     null.UintFrom(1),
				ProductID:     null.UintFrom(3),
				SourceOrderID: null.StringFrom("source"),
				PartnerID:     null.Uint64From(2),
			},
			MMCCParcel: &order_creation_interface.MMCCParcel{
				Parcel: &models.Parcel{
					RefTrackingID: null.StringFrom("ref"),
				},
				ParcelItems: models.ParcelItemSlice{
					&models.ParcelItem{
						RefTrackingID: null.StringFrom("abc"),
						Metadata:      "abc",
					},
					&models.ParcelItem{
						RefTrackingID: null.StringFrom("abc"),
						Metadata:      "abc",
					},
				},
			},
		}
		product := &models.Product{
			ID: payload.Bag.ProductID.Uint,
		}
		service := &models.Service{
			ID: payload.Bag.ServiceID.Uint,
		}
		defer repositories.SaveDbRecords(product, service).CleanUp()

		eventRecorder := event_publisher.NewEventRecorder()
		m := mocks.NewMockHandler(gomock.NewController(t))
		m.EXPECT().UpsertMMCCParcels(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()

		oc := NewOrderCreator()
		oc.shipmentParcelHandler = m
		oc.eventPublisher = eventRecorder
		err := oc.CreateMMCCParcel(context.TODO(), payload)
		require.Nil(t, err)

		parcels, err := models.Parcels(
			models.ParcelWhere.SourceOrderID.EQ(payload.Bag.SourceOrderID),
			models.ParcelWhere.PartnerID.EQ(payload.Bag.PartnerID),
			models.ParcelWhere.Type.EQ(parcel.MMCCParcel),
		).AllG(context.TODO())
		require.Nil(t, err)
		assert.Equal(t, 1, len(parcels))

		parcel := parcels[0]
		assert.Equal(t, payload.MMCCParcel.Parcel.RefTrackingID, parcel.RefTrackingID)
		assert.Equal(t, payload.MMCCParcel.Parcel.Metadata, parcel.Metadata)

		t.Run("should_populate_with_bag_info", func(t *testing.T) {
			assert.Equal(t, payload.Bag.ServiceID, parcel.ServiceID)
			assert.Equal(t, payload.Bag.SourceOrderID, parcel.SourceOrderID)
			assert.Equal(t, payload.Bag.PartnerID, parcel.PartnerID)
		})

		t.Run("should_create_parcel_items", func(t *testing.T) {
			parcelItems, err := models.ParcelItems(models.ParcelItemWhere.ParcelID.EQ(uint64(parcel.ID))).AllG(context.TODO())
			require.Nil(t, err)
			assert.Equal(t, len(payload.MMCCParcel.ParcelItems), len(parcelItems))
		})

		t.Run("should_publish_translate_description_task", func(t *testing.T) {
			assert.Equal(t, 1, len(eventRecorder.GetEventsByTopic(envs.Instance.KafkaTopics.FplAutoTranslateDescriptionTopic)))
		})
	})

	t.Run("should_return_error_when_there_is_duplicate", func(t *testing.T) {
		defer repositories.CleanUpAllTestData(db)
		payload := &order_creation_interface.CreateMMCCParcelPayload{
			Bag: &models.Parcel{
				TrackingID:    "abc",
				ServiceID:     null.UintFrom(1),
				ProductID:     null.UintFrom(3),
				SourceOrderID: null.StringFrom("source"),
				PartnerID:     null.Uint64From(2),
			},
			MMCCParcel: &order_creation_interface.MMCCParcel{
				Parcel: &models.Parcel{
					RefTrackingID:    null.StringFrom("ref"),
					PartnerUniqueKey: null.StringFrom("tiktok-ref"),
				},
				ParcelItems: models.ParcelItemSlice{
					&models.ParcelItem{
						RefTrackingID: null.StringFrom("abc"),
						Metadata:      "abc",
					},
					&models.ParcelItem{
						RefTrackingID: null.StringFrom("abc"),
						Metadata:      "abc",
					},
				},
			},
		}
		dupParcel := &models.Parcel{
			TrackingID:       "zzzzz",
			PartnerUniqueKey: payload.MMCCParcel.Parcel.PartnerUniqueKey,
			PartnerID:        payload.Bag.PartnerID,
			Type:             parcel.MMCCParcel,
		}
		defer repositories.SaveDbRecords(dupParcel).CleanUp()

		oc := NewOrderCreator()
		oc.shipmentParcelHandler = mocks.NewMockHandler(gomock.NewController(t))
		oc.eventPublisher = event_publisher.NewEventRecorder()
		err := oc.CreateMMCCParcel(context.TODO(), payload)
		require.NotNil(t, err)

		t.Run("should_not_create_parcels", func(t *testing.T) {
			parcels, err := models.Parcels(
				models.ParcelWhere.SourceOrderID.EQ(payload.Bag.SourceOrderID),
				models.ParcelWhere.PartnerID.EQ(payload.Bag.PartnerID),
				models.ParcelWhere.Type.EQ(parcel.MMCCParcel),
			).AllG(context.TODO())
			require.Nil(t, err)
			assert.Equal(t, 0, len(parcels))
		})

		t.Run("should_not_create_parcel_items", func(t *testing.T) {
			parcels, err := models.ParcelItems().AllG(context.TODO())
			require.Nil(t, err)
			assert.Equal(t, 0, len(parcels))
		})
	})
}

func TestOrderCreator_moveMMCCBag(t *testing.T) {
	db := repositories.InitInMemoryDBWithTables()
	defer db.Close()

	t.Run("should_move_bag_when_parcel_exists", func(t *testing.T) {
		defer repositories.CleanUpAllTestData(db)

		existingParcel := &models.Parcel{
			TrackingID:    "existing",
			RefTrackingID: null.StringFrom("ref-123"),
			PartnerID:     null.Uint64From(1),
			SourceOrderID: null.StringFrom("old-source"),
			ParentID:      null.Uint{},
			Metadata:      null.StringFrom("{}"),
		}

		existingBag := &models.Parcel{
			TrackingID:    "existing-bag",
			RefTrackingID: null.StringFrom("bag-ref-123"),
			PartnerID:     null.Uint64From(1),
			SourceOrderID: null.StringFrom("old-source"),
			ParentID:      null.Uint{},
			Metadata:      null.StringFrom("{}"),
			Type:          parcel.BagB2CV2,
		}

		bag := &models.Parcel{
			ID:            100,
			TrackingID:    "bag-123",
			SourceOrderID: null.StringFrom("new-source"),
			Type:          parcel.BagB2CV2,
		}

		payload := &order_creation_interface.CreateMMCCParcelPayload{
			MoveBagOnDuplicated: true,
			Bag:                 bag,
			MMCCParcel: &order_creation_interface.MMCCParcel{
				Parcel: &models.Parcel{
					RefTrackingID: existingParcel.RefTrackingID,
					PartnerID:     existingParcel.PartnerID,
				},
			},
		}

		defer repositories.SaveDbRecords(existingBag, existingParcel).CleanUp()

		oc := NewOrderCreator()
		err := oc.moveMMCCBag(context.TODO(), payload)
		require.NoError(t, err)

		updatedParcel, err := models.Parcels(
			models.ParcelWhere.TrackingID.EQ(existingParcel.TrackingID),
		).OneG(context.TODO())
		require.NoError(t, err)

		assert.Equal(t, bag.SourceOrderID, updatedParcel.SourceOrderID)
		assert.Equal(t, null.UintFrom(bag.ID), updatedParcel.ParentID)

		var metadata repo_interface.ParcelMetadata
		err = json.Unmarshal([]byte(updatedParcel.Metadata.String), &metadata)
		require.NoError(t, err)
		assert.Contains(t, metadata.BagMovement, existingBag.TrackingID)
	})

	t.Run("should_return_error_when_parcel_not_found", func(t *testing.T) {
		defer repositories.CleanUpAllTestData(db)

		payload := &order_creation_interface.CreateMMCCParcelPayload{
			MoveBagOnDuplicated: true,
			MMCCParcel: &order_creation_interface.MMCCParcel{
				Parcel: &models.Parcel{
					RefTrackingID: null.StringFrom("non-existent"),
					PartnerID:     null.Uint64From(1),
				},
			},
		}

		oc := NewOrderCreator()
		err := oc.moveMMCCBag(context.TODO(), payload)
		require.Error(t, err)
	})

	t.Run("should_return_error_when_bag_not_found", func(t *testing.T) {
		defer repositories.CleanUpAllTestData(db)

		existingParcel := &models.Parcel{
			TrackingID:    "existing",
			RefTrackingID: null.StringFrom("ref-123"),
			PartnerID:     null.Uint64From(1),
			SourceOrderID: null.StringFrom("old-source"),
		}

		payload := &order_creation_interface.CreateMMCCParcelPayload{
			MoveBagOnDuplicated: true,
			MMCCParcel: &order_creation_interface.MMCCParcel{
				Parcel: &models.Parcel{
					RefTrackingID: existingParcel.RefTrackingID,
					PartnerID:     existingParcel.PartnerID,
				},
			},
		}

		defer repositories.SaveDbRecords(existingParcel).CleanUp()

		oc := NewOrderCreator()
		err := oc.moveMMCCBag(context.TODO(), payload)
		require.ErrorContains(t, err, "bag not found")
	})
}

func TestOrderCreator_CreateMMCCParcels(t *testing.T) {
	var newCreateMMCCParcelPayloadWith2ParcelItems = func(bag, parcel *models.Parcel) *order_creation_interface.CreateMMCCParcelPayload {
		return &order_creation_interface.CreateMMCCParcelPayload{
			Bag: bag,
			MMCCParcel: &order_creation_interface.MMCCParcel{
				Parcel: parcel,
				ParcelItems: models.ParcelItemSlice{
					&models.ParcelItem{
						RefTrackingID: null.StringFrom("abc"),
						Metadata:      "abc",
					},
					&models.ParcelItem{
						RefTrackingID: null.StringFrom("abc"),
						Metadata:      "abc",
					},
				},
			},
		}
	}
	db := repositories.InitInMemoryDBWithTables()
	defer db.Close()
	t.Run("should_create_parcels_when_there_is_no_duplicate", func(t *testing.T) {
		defer repositories.CleanUpAllTestData(db)
		bag1 := &models.Parcel{
			TrackingID:    "abc",
			ServiceID:     null.UintFrom(1),
			ProductID:     null.UintFrom(3),
			SourceOrderID: null.StringFrom("source"),
			PartnerID:     null.Uint64From(2),
		}
		parcel1Bag1 := &models.Parcel{
			RefTrackingID:    null.StringFrom("ref"),
			PartnerUniqueKey: null.StringFrom("tiktok-ref1"),
		}
		parcel2Bag1 := &models.Parcel{
			RefTrackingID:    null.StringFrom("ref"),
			PartnerUniqueKey: null.StringFrom("tiktok-ref2"),
		}
		payload1Bag1 := newCreateMMCCParcelPayloadWith2ParcelItems(bag1, parcel1Bag1)
		payload2Bag1 := newCreateMMCCParcelPayloadWith2ParcelItems(bag1, parcel2Bag1)
		product1 := &models.Product{
			ID:   bag1.ProductID.Uint,
			Code: null.StringFrom(uuid.New().String()),
		}
		service1 := &models.Service{
			ID:   bag1.ServiceID.Uint,
			Code: uuid.New().String(),
		}
		defer repositories.SaveDbRecords(product1, service1).CleanUp()

		bag2 := &models.Parcel{
			TrackingID:    "abc2",
			ServiceID:     null.UintFrom(4),
			ProductID:     null.UintFrom(5),
			SourceOrderID: null.StringFrom("source2"),
			PartnerID:     null.Uint64From(6),
		}
		parcelBag2 := &models.Parcel{
			RefTrackingID:    null.StringFrom("ref"),
			PartnerUniqueKey: null.StringFrom("tiktok2-ref1"),
		}
		payloadBag2 := newCreateMMCCParcelPayloadWith2ParcelItems(bag2, parcelBag2)
		product2 := &models.Product{
			ID:   bag2.ProductID.Uint,
			Code: null.StringFrom(uuid.New().String()),
		}
		service2 := &models.Service{
			ID:   bag2.ServiceID.Uint,
			Code: uuid.New().String(),
		}
		defer repositories.SaveDbRecords(product2, service2).CleanUp()

		payloads := []*order_creation_interface.CreateMMCCParcelPayload{payload1Bag1, payload2Bag1, payloadBag2}

		eventRecorder := event_publisher.NewEventRecorder()
		m := mocks.NewMockHandler(gomock.NewController(t))
		m.EXPECT().UpsertMMCCParcels(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()

		oc := NewOrderCreator()
		oc.shipmentParcelHandler = m
		oc.eventPublisher = eventRecorder
		err := oc.CreateMMCCParcels(context.TODO(), payloads)
		require.Nil(t, err)

		parcelsBag1, err := models.Parcels(
			models.ParcelWhere.SourceOrderID.EQ(bag1.SourceOrderID),
			models.ParcelWhere.PartnerID.EQ(bag1.PartnerID),
			models.ParcelWhere.Type.EQ(parcel.MMCCParcel),
		).AllG(context.TODO())
		require.Nil(t, err)
		assert.Equal(t, 2, len(parcelsBag1))

		p1 := parcelsBag1[0]

		parcelsBag2, err := models.Parcels(
			models.ParcelWhere.SourceOrderID.EQ(bag2.SourceOrderID),
			models.ParcelWhere.PartnerID.EQ(bag2.PartnerID),
			models.ParcelWhere.Type.EQ(parcel.MMCCParcel),
		).AllG(context.TODO())
		require.Nil(t, err)
		assert.Equal(t, 1, len(parcelsBag2))
		p2 := parcelsBag2[0]

		t.Run("should_populate_with_bag_info", func(t *testing.T) {
			assert.Equal(t, bag1.ServiceID, p1.ServiceID)
			assert.Equal(t, bag1.SourceOrderID, p1.SourceOrderID)
			assert.Equal(t, bag1.PartnerID, p1.PartnerID)

			assert.Equal(t, bag2.ServiceID, p2.ServiceID)
			assert.Equal(t, bag2.SourceOrderID, p2.SourceOrderID)
			assert.Equal(t, bag2.PartnerID, p2.PartnerID)
		})

		t.Run("should_create_parcel_items", func(t *testing.T) {
			parcelItems, err := models.ParcelItems().AllG(context.TODO())
			require.Nil(t, err)
			// 2 parcel items per payload
			assert.Equal(t, len(payloads)*2, len(parcelItems))
		})

		t.Run("should_publish_translate_description_task", func(t *testing.T) {
			assert.Equal(t, len(payloads), len(eventRecorder.GetEventsByTopic(envs.Instance.KafkaTopics.FplAutoTranslateDescriptionTopic)))
		})
	})

	t.Run("should_return_error_when_there_is_duplicate", func(t *testing.T) {
		defer repositories.CleanUpAllTestData(db)
		bag1 := &models.Parcel{
			TrackingID:    "abc",
			ServiceID:     null.UintFrom(1),
			ProductID:     null.UintFrom(3),
			SourceOrderID: null.StringFrom("source"),
			PartnerID:     null.Uint64From(2),
		}
		parcel1Bag1 := &models.Parcel{
			RefTrackingID:    null.StringFrom("ref"),
			PartnerUniqueKey: null.StringFrom("tiktok-ref1"),
		}
		payloadBag1 := newCreateMMCCParcelPayloadWith2ParcelItems(bag1, parcel1Bag1)

		bag2 := &models.Parcel{
			TrackingID:    "abc2",
			ServiceID:     null.UintFrom(4),
			ProductID:     null.UintFrom(5),
			SourceOrderID: null.StringFrom("source2"),
			PartnerID:     null.Uint64From(6),
		}
		parcelBag2 := &models.Parcel{
			RefTrackingID:    null.StringFrom("ref"),
			PartnerUniqueKey: null.StringFrom("tiktok2-ref1"),
		}
		payloadBag2 := newCreateMMCCParcelPayloadWith2ParcelItems(bag2, parcelBag2)

		payloads := []*order_creation_interface.CreateMMCCParcelPayload{payloadBag1, payloadBag2}

		dupParcel := &models.Parcel{
			TrackingID:       "zzzzz",
			PartnerUniqueKey: parcel1Bag1.PartnerUniqueKey,
			PartnerID:        bag1.PartnerID,
			Type:             parcel.MMCCParcel,
		}
		defer repositories.SaveDbRecords(dupParcel).CleanUp()

		oc := NewOrderCreator()
		oc.shipmentParcelHandler = mocks.NewMockHandler(gomock.NewController(t))
		oc.eventPublisher = event_publisher.NewEventRecorder()
		err := oc.CreateMMCCParcels(context.TODO(), payloads)
		require.NotNil(t, err)

		t.Run("should_not_create_parcels", func(t *testing.T) {
			parcels, err := models.Parcels(
				models.ParcelWhere.Type.EQ(parcel.MMCCParcel),
			).AllG(context.TODO())
			require.Nil(t, err)
			// the dup parcel
			assert.Equal(t, 1, len(parcels))
		})

		t.Run("should_not_create_parcel_items", func(t *testing.T) {
			parcels, err := models.ParcelItems().AllG(context.TODO())
			require.Nil(t, err)
			assert.Equal(t, 0, len(parcels))
		})
	})
}

func Test_init(t *testing.T) {
	type fields struct {
		serviceWrapper *serviceInterface.ServiceWrapper
	}
	type args struct {
		req     *order.BaseRequest
		partner *models.Partner
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "retrieve service but got an error",
			fields: fields{
				serviceWrapper: nil,
			},
			args: args{
				req: &order.BaseRequest{
					ServiceCode: "ABC-XYZ",
				},
				partner: &models.Partner{ID: 1},
			},
			wantErr: true,
		},
		{
			name: "retrieve partner OC_CONFIG setting but couldn't find",
			fields: fields{
				serviceWrapper: &serviceInterface.ServiceWrapper{
					ProductVendors: repo_interface.ProductVendorWrapperSlice{},
				},
			},
			args: args{
				req: &order.BaseRequest{
					ServiceCode: "ABC-XYZ",
				},
				partner: &models.Partner{
					ID:                  1,
					OrderCreationMethod: null.Uint8From(uint8(configs.OrderCreationMethod4PLOC)),
				},
			},
			wantErr: true,
		},
		{
			name: "the input service is not allowed by partner settings",
			fields: fields{
				serviceWrapper: &serviceInterface.ServiceWrapper{
					Service:        &models.Service{ID: 3, Code: "ABC-XYZ"},
					ProductVendors: repo_interface.ProductVendorWrapperSlice{},
				},
			},
			args: args{
				req: &order.BaseRequest{
					ServiceCode: "ABC-XYZ",
				},
				partner: &models.Partner{
					ID:                  1,
					OrderCreationMethod: null.Uint8From(uint8(configs.OrderCreationMethod4PLOC)),
				},
			},
			wantErr: true,
		},
		{
			name: "the order flow of product vendor has not been supported by the client config",
			fields: fields{
				serviceWrapper: &serviceInterface.ServiceWrapper{
					Service: &models.Service{ID: 1, Code: "ABC-XYZ", ProductID: null.UintFrom(2)},
					Product: &models.Product{ID: 2},
					ProductVendors: repo_interface.ProductVendorWrapperSlice{
						{
							ProductVendor: models.ProductVendor{ProductID: 2, Stage: null.StringFrom("LM"), VendorID: 3},
							Vendor:        models.Vendor{ID: 3},
						},
					},
				},
			},
			args: args{
				req: &order.BaseRequest{
					ServiceCode: "ABC-XYZ",
				},
				partner: &models.Partner{
					ID:                  1,
					OrderCreationMethod: null.Uint8From(uint8(configs.OrderCreationMethodWish)),
				},
			},
			wantErr: true,
		},
		{
			name: "pickup is not supported",
			fields: fields{
				serviceWrapper: &serviceInterface.ServiceWrapper{
					Service: &models.Service{ID: 1, Code: "ABC-XYZ", ProductID: null.UintFrom(2)},
					Product: &models.Product{ID: 2},
					ProductVendors: repo_interface.ProductVendorWrapperSlice{
						{
							ProductVendor: models.ProductVendor{ProductID: 2, Stage: null.StringFrom("LM"), VendorID: 3},
							Vendor:        models.Vendor{ID: 3},
						},
					},
				},
			},
			args: args{
				req: &order.BaseRequest{
					ServiceCode: "ABC-XYZ",
					Pickup:      &order.Pickup{},
				},
				partner: &models.Partner{
					ID:                  1,
					OrderCreationMethod: null.Uint8From(uint8(configs.OrderCreationMethod4PLOC)),
				},
			},
			wantErr: true,
		},
		{
			name: "pickup country mismatch destination country in case domestic",
			fields: fields{
				serviceWrapper: &serviceInterface.ServiceWrapper{
					Service: &models.Service{ID: 1, Code: "ABC-XYZ", ProductID: null.UintFrom(2)},
					Product: &models.Product{ID: 2},
					ProductVendors: repo_interface.ProductVendorWrapperSlice{
						{
							ProductVendor: models.ProductVendor{ProductID: 2, Stage: null.StringFrom("LM"), VendorID: 3},
							Vendor:        models.Vendor{ID: 3},
						},
					},
				},
			},
			args: args{
				req: &order.BaseRequest{
					ServiceCode: "ABC-XYZ",
					To:          order.Address{CountryCode: "VN"},
					Pickup:      &order.Pickup{Address: &order.Address{CountryCode: "ID"}},
				},
				partner: &models.Partner{ID: 1, OrderCreationMethod: null.Uint8From(
					uint8(configs.OrderCreationMethod4PLOC),
				)},
			},
			wantErr: true,
		},
		{
			name: "the order flow should be supported by client config",
			fields: fields{
				serviceWrapper: &serviceInterface.ServiceWrapper{
					Service: &models.Service{ID: 1, Code: "ABC-XYZ", ProductID: null.UintFrom(2)},
					Product: &models.Product{ID: 2},
					ProductVendors: repo_interface.ProductVendorWrapperSlice{
						{
							ProductVendor: models.ProductVendor{ProductID: 2, Stage: null.StringFrom("LM"), VendorID: 3},
							Vendor:        models.Vendor{ID: 3},
						},
					},
				},
			},
			args: args{
				req: &order.BaseRequest{
					ServiceCode: "ABC-XYZ",
				},
				partner: &models.Partner{ID: 1, OrderCreationMethod: null.Uint8From(
					uint8(configs.OrderCreationMethod4PLOC),
				)},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer repositories.InitInMemoryDBWithTables().Close()
			partnerSetting := &models.PartnerSetting{
				ID:        1,
				PartnerID: 1,
				Key:       configs.OCConfigKey,
				Value:     null.StringFrom(`{"services": [1,2], "is_used_consolidated_shipper": true}`),
			}
			defer repositories.SaveDbRecords(partnerSetting).CleanUp()
			if tt.fields.serviceWrapper != nil {
				if tt.fields.serviceWrapper.Service != nil {
					defer repositories.SaveDbRecords(tt.fields.serviceWrapper.Service).CleanUp()
				}
				if tt.fields.serviceWrapper.Product != nil {
					defer repositories.SaveDbRecords(tt.fields.serviceWrapper.Product).CleanUp()
				}
				if tt.fields.serviceWrapper.ProductVendors != nil {
					for _, item := range tt.fields.serviceWrapper.ProductVendors {
						defer repositories.SaveDbRecords(&item.ProductVendor, &item.Vendor).CleanUp()
					}
				}
			}
			creator := NewOrderCreator()
			if tt.name == "retrieve partner OC_CONFIG setting but couldn't find" {
				partnerSetting.DeleteG(context.Background())
			}
			_, _, err := creator.loadOCConfig(context.TODO(), tt.args.req, tt.args.partner)
			if (err != nil) != tt.wantErr {
				t.Errorf("get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestGetCustomSheinReturnAddressMY(t *testing.T) {
	t.Parallel()
	type args struct {
		address *order.Address
	}
	tests := []struct {
		name string
		args args
		want *order.Address
	}{
		{
			name: "Elaine RTS",
			args: args{
				address: &order.Address{
					StateProvince: null.StringFrom("Johor").Ptr(),
					CountryCode:   "MY",
				},
			},
			want: &order.Address{
				Name:          "Elaine(KERRY)",
				AddressLine1:  "No. 27, Jalan TS 6/8, Taman Perindustrian Subang, 47500 Subang Jaya, Selangor.",
				AddressLine2:  nil,
				AddressLine3:  nil,
				AddressLine4:  nil,
				City:          null.StringFrom("Subang Jaya").Ptr(),
				StateProvince: null.StringFrom("Selangor").Ptr(),
				CountryCode:   "MY",
				PostCode:      null.StringFrom("47500").Ptr(),
				ContactNumber: null.StringFrom("0166241040").Ptr(),
			},
		},
		{
			name: "Sharifah RTS",
			args: args{
				address: &order.Address{
					StateProvince: null.StringFrom("Sabah").Ptr(),
					CountryCode:   "MY",
				},
			},
			want: &order.Address{
				Name:          "Sharifah Aqilah",
				AddressLine1:  "Warehouse E, Lot 72, Jalan Sepanggar, 88450 Kota Kinabalu, Sabah",
				AddressLine2:  nil,
				AddressLine3:  nil,
				AddressLine4:  nil,
				City:          null.StringFrom("Kota Kinabalu").Ptr(),
				StateProvince: null.StringFrom("Sabah").Ptr(),
				CountryCode:   "MY",
				PostCode:      null.StringFrom("88450").Ptr(),
				ContactNumber: null.StringFrom("+60 11-3322 5272").Ptr(),
			},
		},
		{
			name: "empt province",
			args: args{
				address: &order.Address{
					StateProvince: null.StringFrom("").Ptr(),
					CountryCode:   "MY",
				},
			},
			want: &order.Address{
				Name:          "Elaine(KERRY)",
				AddressLine1:  "No. 27, Jalan TS 6/8, Taman Perindustrian Subang, 47500 Subang Jaya, Selangor.",
				AddressLine2:  nil,
				AddressLine3:  nil,
				AddressLine4:  nil,
				City:          null.StringFrom("Subang Jaya").Ptr(),
				StateProvince: null.StringFrom("Selangor").Ptr(),
				CountryCode:   "MY",
				PostCode:      null.StringFrom("47500").Ptr(),
				ContactNumber: null.StringFrom("0166241040").Ptr(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, getCustomSheinReturnAddressMY(tt.args.address), "getCustomSheinReturnAddressMY(%v)", tt.args.address)
		})
	}
}

func TestGetCustomSheinReturnAddress(t *testing.T) {
	t.Parallel()
	type args struct {
		address *order.Address
	}
	tests := []struct {
		name string
		args args
		want *order.Address
	}{
		{
			name: "SG country",
			args: args{
				address: &order.Address{
					CountryCode: "SG",
				},
			},
			want: &SGAddress,
		},
		{
			name: "PH country",
			args: args{
				address: &order.Address{
					CountryCode: "PH",
				},
			},
			want: &PHAddress,
		},
		{
			name: "MY country ",
			args: args{
				address: &order.Address{
					StateProvince: null.StringFrom("").Ptr(),
					CountryCode:   "MY",
				},
			},
			want: &order.Address{
				Name:          "Elaine(KERRY)",
				AddressLine1:  "No. 27, Jalan TS 6/8, Taman Perindustrian Subang, 47500 Subang Jaya, Selangor.",
				AddressLine2:  nil,
				AddressLine3:  nil,
				AddressLine4:  nil,
				City:          null.StringFrom("Subang Jaya").Ptr(),
				StateProvince: null.StringFrom("Selangor").Ptr(),
				CountryCode:   "MY",
				PostCode:      null.StringFrom("47500").Ptr(),
				ContactNumber: null.StringFrom("0166241040").Ptr(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, getCustomSheinReturnAddress(tt.args.address), "getCustomSheinReturnAddress(%v)", tt.args.address)
		})
	}
}

func Test_orderClient_prepareShippers(t *testing.T) {
	t.Parallel()
	type fields struct {
		accessToken   *authapi.OauthAccessToken
		request       *order.BaseRequest
		prepared      *order_creation_interface.OrderCreationConfig
		config        *clientConfig
		storeManager  order_creation_interface.DbAccessor
		shipperGetter order_creation_interface.ShipperFinder
	}

	ctrl := gomock.NewController(t)

	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "shipper finder by payload and data not found",
			fields: fields{
				config: &clientConfig{
					ShipperDetectionType: orderConst.OrderShipperFromPayload,
				},
				prepared: &order_creation_interface.OrderCreationConfig{},
				request: &order.BaseRequest{
					GlobalShipperID: 100,
				},
				shipperGetter: func() order_creation_interface.ShipperFinder {
					g := orderCreationMocks.NewMockShipperFinder(ctrl)
					g.EXPECT().Find(gomock.Any(), &order_creation_interface.OrderCreationConfig{}, uint(100), gomock.Any(), gomock.Any()).Return(nil, errors.New("can't"))

					return g
				}(),
			},
			wantErr: true,
		},
		{
			name: "shipper finder by payload reversed and data not found",
			fields: fields{
				config: &clientConfig{
					ShipperDetectionType: orderConst.OrderShipperFromPayloadReversed,
				},
				prepared: &order_creation_interface.OrderCreationConfig{},
				request: &order.BaseRequest{
					GlobalShipperID: 100,
				},
				shipperGetter: func() order_creation_interface.ShipperFinder {
					g := orderCreationMocks.NewMockShipperFinder(ctrl)
					g.EXPECT().Find(gomock.Any(), &order_creation_interface.OrderCreationConfig{}, uint(100), gomock.Any(), nil).Return(nil, errors.New("can't"))

					return g
				}(),
			},
			wantErr: true,
		},
		{
			name: "shipper finder by auth credential and data not found",
			fields: fields{
				config: &clientConfig{
					ShipperDetectionType: orderConst.OrderShipperFromPartnerShippers,
				},
				prepared: &order_creation_interface.OrderCreationConfig{},
				request: &order.BaseRequest{
					GlobalShipperID: 100,
				},
				accessToken: &authapi.OauthAccessToken{},
				shipperGetter: func() order_creation_interface.ShipperFinder {
					g := orderCreationMocks.NewMockShipperFinder(ctrl)
					g.EXPECT().Find(gomock.Any(), &order_creation_interface.OrderCreationConfig{}, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("can't"))

					return g
				}(),
			},
			wantErr: true,
		},
		{
			name: "able to detect global shipper id when order flow is LM for client credential",
			fields: fields{
				config: &clientConfig{
					ShipperDetectionType: orderConst.OrderShipperFromPartnerShippers,
				},
				prepared: &order_creation_interface.OrderCreationConfig{
					OrderFlow: orderConst.FlowOnlyLastMile,
				},
				request:     &order.BaseRequest{},
				accessToken: &authapi.OauthAccessToken{},
				shipperGetter: func() order_creation_interface.ShipperFinder {
					g := orderCreationMocks.NewMockShipperFinder(ctrl)
					g.EXPECT().Find(gomock.Any(), &order_creation_interface.OrderCreationConfig{OrderFlow: orderConst.FlowOnlyLastMile}, gomock.Any(), gomock.Any(), gomock.Any()).Return(&order_creation_interface.OrderShippers{
						DestinationShipper: &models.Shipper{},
					}, nil)

					return g
				}(),
			},
		},
		{
			name: "able to detect global shipper id when order flow is FM for client credential",
			fields: fields{
				config: &clientConfig{
					ShipperDetectionType: orderConst.OrderShipperFromPartnerShippers,
				},
				prepared: &order_creation_interface.OrderCreationConfig{
					OrderFlow: orderConst.FlowOnlyFirstMile,
				},
				request:     &order.BaseRequest{},
				accessToken: &authapi.OauthAccessToken{},
				shipperGetter: func() order_creation_interface.ShipperFinder {
					g := orderCreationMocks.NewMockShipperFinder(ctrl)
					g.EXPECT().Find(gomock.Any(), &order_creation_interface.OrderCreationConfig{OrderFlow: orderConst.FlowOnlyFirstMile}, gomock.Any(), gomock.Any(), gomock.Any()).Return(&order_creation_interface.OrderShippers{
						OriginShipper: &models.Shipper{},
					}, nil)

					return g
				}(),
			},
		},
		{
			name: "able to detect global shipper id when order flow is E2E for client credential",
			fields: fields{
				config: &clientConfig{
					ShipperDetectionType: orderConst.OrderShipperFromPartnerShippers,
				},
				prepared: &order_creation_interface.OrderCreationConfig{
					OrderFlow: orderConst.FlowEndToEnd,
				},
				request:     &order.BaseRequest{},
				accessToken: &authapi.OauthAccessToken{},
				shipperGetter: func() order_creation_interface.ShipperFinder {
					g := orderCreationMocks.NewMockShipperFinder(ctrl)
					g.EXPECT().Find(gomock.Any(), &order_creation_interface.OrderCreationConfig{OrderFlow: orderConst.FlowEndToEnd}, gomock.Any(), gomock.Any(), gomock.Any()).Return(&order_creation_interface.OrderShippers{
						OriginShipper: &models.Shipper{},
					}, nil)
					return g
				}(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &orderCreator{
				storeManager:  tt.fields.storeManager,
				shipperGetter: tt.fields.shipperGetter,
			}
			if err := o.prepareShippers(context.TODO(), tt.fields.request, tt.fields.prepared, tt.fields.config); (err != nil) != tt.wantErr {
				t.Errorf("orderClient.prepareShippers() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_orderClient_orderableShipper(t *testing.T) {
	t.Parallel()
	type fields struct {
		request       *order.BaseRequest
		prepared      *order_creation_interface.OrderCreationConfig
		config        *clientConfig
		storeManager  order_creation_interface.DbAccessor
		shipperGetter order_creation_interface.ShipperFinder
	}
	type args struct {
		stage string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *models.Shipper
	}{
		{
			name: "when the stage = LM, the order shipper should be destination country shipper",
			fields: fields{
				prepared: &order_creation_interface.OrderCreationConfig{
					DestinationShipper: &models.Shipper{ID: 100},
				},
			},
			args: args{
				vendorConst.StageLastMile,
			},
			want: &models.Shipper{ID: 100},
		},
		{
			name: "when the stage = FM, the order shipper should be origin country shipper",
			fields: fields{
				prepared: &order_creation_interface.OrderCreationConfig{
					OriginShipper: &models.Shipper{ID: 101},
				},
			},
			args: args{
				vendorConst.StageFirstMile,
			},
			want: &models.Shipper{ID: 101},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &orderCreator{
				storeManager:  tt.fields.storeManager,
				shipperGetter: tt.fields.shipperGetter,
			}
			if got := o.orderableShipper(context.TODO(), tt.args.stage, tt.fields.request, tt.fields.prepared); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("orderClient.orderableShipper() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_orderClient_parcelCreationShipper(t *testing.T) {
	t.Parallel()
	type fields struct {
		request       *order.BaseRequest
		prepared      *order_creation_interface.OrderCreationConfig
		config        *clientConfig
		storeManager  order_creation_interface.DbAccessor
		shipperGetter order_creation_interface.ShipperFinder
	}
	tests := []struct {
		name   string
		fields fields
		want   *models.Shipper
	}{
		{
			name: "able to get default shipper as parcel shipper in non-NV flow",
			fields: fields{
				request: &order.BaseRequest{},
				prepared: &order_creation_interface.OrderCreationConfig{
					DefaultShipper: &models.Shipper{ID: 1000},
					OrderFlow:      orderConst.FlowUnknown,
				},
			},
			want: &models.Shipper{ID: 1000},
		},
		{
			name: "able to get destination country shipper as parcel shipper in only LM flow",
			fields: fields{
				request: &order.BaseRequest{},
				prepared: &order_creation_interface.OrderCreationConfig{
					DestinationShipper: &models.Shipper{ID: 1000},
					OrderFlow:          orderConst.FlowOnlyLastMile,
				},
			},
			want: &models.Shipper{ID: 1000},
		},
		{
			name: "able to get destination country shipper as parcel shipper in only FM flow",
			fields: fields{
				request: &order.BaseRequest{},
				prepared: &order_creation_interface.OrderCreationConfig{
					OriginShipper: &models.Shipper{ID: 1000},
					OrderFlow:     orderConst.FlowOnlyFirstMile,
				},
			},
			want: &models.Shipper{ID: 1000},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &orderCreator{
				storeManager:  tt.fields.storeManager,
				shipperGetter: tt.fields.shipperGetter,
			}
			if got := o.parcelCreationShipper(context.Background(), tt.fields.request, tt.fields.prepared); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("orderClient.parcelCreationShipper() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_orderClient_newOrderRequestItem(t *testing.T) {
	type fields struct {
		request       *order.BaseRequest
		ocConfig      *order_creation_interface.OrderCreationConfig
		config        *clientConfig
		storeManager  order_creation_interface.DbAccessor
		shipperGetter order_creation_interface.ShipperFinder
	}
	type args struct {
		productVendorWrapper *repo_interface.ProductVendorWrapper
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *order_creation_interface.RequestItem
		wantErr error
	}{
		{
			name: "normal request item",
			fields: fields{
				request: &order.BaseRequest{},
			},
			args: args{
				productVendorWrapper: &repo_interface.ProductVendorWrapper{
					ProductVendor: models.ProductVendor{},
					Vendor:        models.Vendor{Handler: vendorConst.VendorHandlerDefault},
				},
			},
			want:    nil,
			wantErr: errIntegrationServiceNotFound,
		},
		{
			name: "request item for vendor order creatable but fail due to validate request",
			fields: fields{
				request: &order.BaseRequest{
					From: order.Address{
						AddressLine1: "AL1",
					},
					To: order.Address{
						CountryCode: "XX",
					},
				},
				ocConfig: &order_creation_interface.OrderCreationConfig{
					OrderFlow:     orderConst.FlowOnlyFirstMile,
					OriginShipper: &models.Shipper{Country: null.StringFrom("SG"), RemoteID: null.UintFrom(1)},
					Service:       &models.Service{Type: serviceCfg.TypeE2E.NullUint8()},
				},
				config: &clientConfig{
					SupportedOrderFlows: clientConfigs[configs.OrderCreationMethod4PLOC].SupportedOrderFlows,
				},
			},
			args: args{
				productVendorWrapper: &repo_interface.ProductVendorWrapper{
					ProductVendor: models.ProductVendor{Stage: null.StringFrom(vendorConst.StageFirstMile)},
					Vendor:        models.Vendor{Handler: vendorConst.VendorHandlerNVCore, Country: "XX"},
				},
			},
			wantErr: fplerror.ErrBadRequest.NewWithoutStack(
				"order_create.Shipper.not_match_system_id",
				uint(1), "xx",
			),
		},
		{
			name: "missing orderable shipper in last mile (wrong data setup)",
			fields: fields{
				request: &order.BaseRequest{
					From: order.Address{
						AddressLine1: "AL1",
					},
					To: order.Address{
						CountryCode: "SG",
					},
				},
				ocConfig: &order_creation_interface.OrderCreationConfig{
					OrderFlow:     orderConst.FlowOnlyFirstMile,
					OriginShipper: &models.Shipper{Country: null.StringFrom("SG")},
					Service:       &models.Service{Type: serviceCfg.TypeE2E.NullUint8()},
				},
				config: &clientConfig{
					SupportedOrderFlows: clientConfigs[configs.OrderCreationMethod4PLOC].SupportedOrderFlows,
				},
			},
			args: args{
				productVendorWrapper: &repo_interface.ProductVendorWrapper{
					ProductVendor: models.ProductVendor{Stage: null.StringFrom(vendorConst.StageLastMile)},
					Vendor:        models.Vendor{Handler: vendorConst.VendorHandlerNVCore, Country: "XX"},
				},
			},
			wantErr: fplerror.ErrBadRequest.NewWithoutStack("the service contains NV vendor but with wrong stage"),
		},
		{
			name: "request item for vendor order creatable but fail due to different country between vendor and orderable shipper",
			fields: fields{
				request: &order.BaseRequest{
					From: order.Address{
						AddressLine1: "AL1",
					},
					To: order.Address{
						CountryCode: "SG",
					},
				},
				ocConfig: &order_creation_interface.OrderCreationConfig{
					OrderFlow:     orderConst.FlowOnlyFirstMile,
					OriginShipper: &models.Shipper{Country: null.StringFrom("SG"), RemoteID: null.UintFrom(1)},
					Service:       &models.Service{Type: serviceCfg.TypeE2E.NullUint8()},
				},
				config: &clientConfig{
					SupportedOrderFlows: clientConfigs[configs.OrderCreationMethod4PLOC].SupportedOrderFlows,
				},
			},
			args: args{
				productVendorWrapper: &repo_interface.ProductVendorWrapper{
					ProductVendor: models.ProductVendor{Stage: null.StringFrom(vendorConst.StageFirstMile)},
					Vendor:        models.Vendor{Handler: vendorConst.VendorHandlerNVCore, Country: "XX"},
				},
			},
			wantErr: fplerror.ErrBadRequest.NewWithoutStack(
				"order_create.Shipper.not_match_system_id", uint(1), "xx",
			),
		},
		{
			name: "request item for vendor order creatable not Tiktok",
			fields: fields{
				request: &order.BaseRequest{
					From: order.Address{
						AddressLine1: "AL1",
					},
					To: order.Address{
						CountryCode: "SG",
					},
				},
				ocConfig: &order_creation_interface.OrderCreationConfig{
					OrderFlow:     orderConst.FlowOnlyFirstMile,
					OriginShipper: &models.Shipper{Country: null.StringFrom("SG")},
					Partner:       &models.Partner{ID: 1},
					Service:       &models.Service{Type: serviceCfg.TypeE2E.NullUint8()},
				},
				config: &clientConfig{
					SupportedOrderFlows: clientConfigs[configs.OrderCreationMethod4PLOC].SupportedOrderFlows,
				},
			},
			args: args{
				productVendorWrapper: &repo_interface.ProductVendorWrapper{
					ProductVendor: models.ProductVendor{Stage: null.StringFrom(vendorConst.StageFirstMile)},
					Vendor:        models.Vendor{Handler: vendorConst.VendorHandlerNVCore, Country: "SG"},
				},
			},
			want: &order_creation_interface.RequestItem{
				Request: &order.BaseRequest{
					From: order.Address{
						AddressLine1: "AL1",
					},
					To: order.Address{
						CountryCode: "SG",
					},
				},
				OrderableShipper: &models.Shipper{Country: null.StringFrom("SG")},
				VendorAPIService: func() base.VendorOrderCreator {
					vendorOrderCreator, _ := nvcore.New(nvcore.OrderInitParams{
						Request: &order.BaseRequest{
							From: order.Address{
								AddressLine1: "AL1",
							},
							To: order.Address{
								CountryCode: "SG",
							},
						},
						Shipper:              &models.Shipper{Country: null.StringFrom("SG")},
						ShipperDetectionType: orderConst.OrderShipperFromPartnerShippers,
						IsConsolidateFlow:    false,
						OrderFlow:            orderConst.FlowOnlyFirstMile,
						Stage:                vendorConst.StageFirstMile,
						SystemID:             "sg",
					})

					return vendorOrderCreator
				}(),
				IntegratedServiceType: OrderCreateServiceType,
				ProductVendor:         &models.ProductVendor{Stage: null.StringFrom(vendorConst.StageFirstMile)},
				Vendor:                &models.Vendor{Handler: vendorConst.VendorHandlerNVCore, Country: "SG"},
			},
		},
		{
			name: "request item for vendor order creatable Tiktok with value and merchant order metadata null",
			fields: fields{
				request: &order.BaseRequest{
					From: order.Address{
						AddressLine1: "AL1",
					},
					To: order.Address{
						CountryCode: "SG",
					},
					ParcelDetails: nil,
				},
				ocConfig: &order_creation_interface.OrderCreationConfig{
					OrderFlow:     orderConst.FlowOnlyFirstMile,
					OriginShipper: &models.Shipper{Country: null.StringFrom("SG")},
					Partner:       &models.Partner{ID: envs.Instance.Tiktok.PartnerId},
					Service:       &models.Service{Type: serviceCfg.TypeE2E.NullUint8()},
				},
				config: &clientConfig{
					SupportedOrderFlows: clientConfigs[configs.OrderCreationMethod4PLOC].SupportedOrderFlows,
				},
			},
			args: args{
				productVendorWrapper: &repo_interface.ProductVendorWrapper{
					ProductVendor: models.ProductVendor{Stage: null.StringFrom(vendorConst.StageFirstMile)},
					Vendor:        models.Vendor{Handler: vendorConst.VendorHandlerNVCore, Country: "SG"},
				},
			},
			want: &order_creation_interface.RequestItem{
				Request: &order.BaseRequest{
					From: order.Address{
						AddressLine1: "AL1",
					},
					To: order.Address{
						CountryCode: "SG",
					},
					ParcelDetails: nil,
				},
				OrderableShipper: &models.Shipper{Country: null.StringFrom("SG")},
				VendorAPIService: func() base.VendorOrderCreator {
					vendorOrderCreator, _ := nvcore.New(nvcore.OrderInitParams{
						Request: &order.BaseRequest{
							From: order.Address{
								AddressLine1: "AL1",
							},
							To: order.Address{
								CountryCode: "SG",
							},
						},
						Shipper:              &models.Shipper{Country: null.StringFrom("SG")},
						ShipperDetectionType: orderConst.OrderShipperFromPartnerShippers,
						IsConsolidateFlow:    false,
						OrderFlow:            orderConst.FlowOnlyFirstMile,
						Stage:                vendorConst.StageFirstMile,
						SystemID:             "sg",
					})
					return vendorOrderCreator
				}(),
				IntegratedServiceType: OrderCreateServiceType,
				ProductVendor:         &models.ProductVendor{Stage: null.StringFrom(vendorConst.StageFirstMile)},
				Vendor:                &models.Vendor{Handler: vendorConst.VendorHandlerNVCore, Country: "SG"},
			},
		},
		{
			name: "request item for vendor order creatable Tiktok",
			fields: fields{
				request: &order.BaseRequest{
					From: order.Address{
						AddressLine1: "AL1",
					},
					To: order.Address{
						CountryCode: "SG",
					},
					ParcelDetails: &models.ParcelDetails{Value: null.Float64From(13.421).Ptr()},
				},
				ocConfig: &order_creation_interface.OrderCreationConfig{
					OrderFlow:     orderConst.FlowOnlyFirstMile,
					OriginShipper: &models.Shipper{Country: null.StringFrom("SG")},
					Partner:       &models.Partner{ID: envs.Instance.Tiktok.PartnerId},
					Service:       &models.Service{Type: serviceCfg.TypeE2E.NullUint8()},
				},
				config: &clientConfig{
					SupportedOrderFlows: clientConfigs[configs.OrderCreationMethod4PLOC].SupportedOrderFlows,
				},
			},
			args: args{
				productVendorWrapper: &repo_interface.ProductVendorWrapper{
					ProductVendor: models.ProductVendor{Stage: null.StringFrom(vendorConst.StageFirstMile)},
					Vendor:        models.Vendor{Handler: vendorConst.VendorHandlerNVCore, Country: "SG"},
				},
			},
			want: &order_creation_interface.RequestItem{
				Request: &order.BaseRequest{
					From: order.Address{
						AddressLine1: "AL1",
					},
					To: order.Address{
						CountryCode: "SG",
					},
					ParcelDetails: &models.ParcelDetails{Value: null.Float64From(13.421).Ptr()},
				},
				OrderableShipper: &models.Shipper{Country: null.StringFrom("SG")},
				VendorAPIService: func() base.VendorOrderCreator {
					vendorOrderCreator, _ := nvcore.New(nvcore.OrderInitParams{
						Request: &order.BaseRequest{
							From: order.Address{
								AddressLine1: "AL1",
							},
							To: order.Address{
								CountryCode: "SG",
							},
							Delivery:      &order.Delivery{InsuredValue: 13.421},
							ParcelDetails: &models.ParcelDetails{},
						},
						Shipper:              &models.Shipper{Country: null.StringFrom("SG")},
						ShipperDetectionType: orderConst.OrderShipperFromPartnerShippers,
						IsConsolidateFlow:    false,
						OrderFlow:            orderConst.FlowOnlyFirstMile,
						Stage:                vendorConst.StageFirstMile,
						SystemID:             "sg",
					})

					return vendorOrderCreator
				}(),
				IntegratedServiceType: OrderCreateServiceType,
				ProductVendor:         &models.ProductVendor{Stage: null.StringFrom(vendorConst.StageFirstMile)},
				Vendor:                &models.Vendor{Handler: vendorConst.VendorHandlerNVCore, Country: "SG"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &orderCreator{
				storeManager:  tt.fields.storeManager,
				shipperGetter: tt.fields.shipperGetter,
			}
			got, err := o.newOrderRequestItem(context.TODO(), tt.args.productVendorWrapper, tt.fields.request, tt.fields.ocConfig, tt.fields.config)
			if tt.wantErr != nil {
				assert.Equal(t, tt.wantErr, err)
				return
			}
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_orderClient_newOrderRequestItems(t *testing.T) {
	t.Parallel()
	type fields struct {
		request       *order.BaseRequest
		ocConfig      *order_creation_interface.OrderCreationConfig
		config        *clientConfig
		storeManager  order_creation_interface.DbAccessor
		shipperGetter order_creation_interface.ShipperFinder
	}

	remoteOriginShipperId := null.NewUint(99, true)

	ctrl := gomock.NewController(t)
	tests := []struct {
		name    string
		fields  fields
		want    []*order_creation_interface.RequestItem
		wantErr error
	}{
		{
			name: "fail to prepare request items due to invalid shipper detection type",
			fields: fields{
				shipperGetter: func() order_creation_interface.ShipperFinder {
					return newShipperGetter()
				}(),
				ocConfig: &order_creation_interface.OrderCreationConfig{
					ProductVendors: repo_interface.ProductVendorWrapperSlice{
						{
							ProductVendor: models.ProductVendor{Stage: null.StringFrom(vendorConst.StageCustomClearance)},
							Vendor:        models.Vendor{Handler: vendorConst.VendorHandlerDefault},
						},
						{
							ProductVendor: models.ProductVendor{Stage: null.StringFrom(vendorConst.StageFirstMile)},
							Vendor:        models.Vendor{Handler: vendorConst.VendorHandlerNVCore, Country: "SG"},
						},
					},
					OrderFlow: orderConst.FlowOnlyFirstMile,
					Service: &models.Service{
						Type: null.Uint8From(uint8(serviceCfg.TypeE2E)),
					},
				},
				config: &clientConfig{
					SupportedOrderFlows: clientConfigs[configs.OrderCreationMethod4PLOC].SupportedOrderFlows,
				},
				request: &order.BaseRequest{
					From: order.Address{
						AddressLine1: "AL1",
					},
					To: order.Address{
						CountryCode: "XX",
					},
				},
			},
			wantErr: errors.New("invalid shipper detection type"),
		},
		{
			name: "prepare order request items successfully",
			fields: fields{
				shipperGetter: func() order_creation_interface.ShipperFinder {
					s := orderCreationMocks.NewMockShipperFinder(ctrl)
					s.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&order_creation_interface.OrderShippers{OriginShipper: &models.Shipper{ID: 1, RemoteID: remoteOriginShipperId, Country: null.StringFrom("SG")}}, nil)

					return s
				}(),
				ocConfig: &order_creation_interface.OrderCreationConfig{
					ProductVendors: repo_interface.ProductVendorWrapperSlice{
						{
							ProductVendor: models.ProductVendor{Stage: null.StringFrom(vendorConst.StageCustomClearance)},
							Vendor:        models.Vendor{Handler: vendorConst.VendorHandlerDefault},
						},
						{
							ProductVendor: models.ProductVendor{Stage: null.StringFrom(vendorConst.StageFirstMile)},
							Vendor:        models.Vendor{Handler: vendorConst.VendorHandlerNVCore, Country: "SG"},
						},
					},
					OrderFlow: orderConst.FlowOnlyFirstMile,
					Partner:   &models.Partner{ID: 1},
					Service: &models.Service{
						Type: null.Uint8From(uint8(serviceCfg.TypeE2E)),
					},
				},
				config: &clientConfig{
					ShipperDetectionType: clientConfigs[configs.OrderCreationMethod4PLOC].ShipperDetectionType,
					SupportedOrderFlows:  clientConfigs[configs.OrderCreationMethod4PLOC].SupportedOrderFlows,
				},
				request: &order.BaseRequest{
					From: order.Address{
						AddressLine1: "AL1",
					},
					To: order.Address{
						CountryCode: "SG",
					},
				},
			},
			want: []*order_creation_interface.RequestItem{
				{
					ProductVendor:    &models.ProductVendor{Stage: null.StringFrom(vendorConst.StageFirstMile)},
					Vendor:           &models.Vendor{Handler: vendorConst.VendorHandlerNVCore, Country: "SG"},
					OrderableShipper: &models.Shipper{ID: 1, RemoteID: remoteOriginShipperId, Country: null.StringFrom("SG")},
					Request: &order.BaseRequest{
						From: order.Address{
							AddressLine1: "AL1",
						},
						To: order.Address{
							CountryCode: "SG",
						},
						GlobalShipperID:       remoteOriginShipperId.Uint,
						OriginGlobalShipperID: &remoteOriginShipperId.Uint,
					},
					IntegratedServiceType: OrderCreateServiceType,
					VendorAPIService: func() base.VendorOrderCreator {
						vendorOrderCreator, _ := nvcore.New(nvcore.OrderInitParams{
							Request: &order.BaseRequest{
								From: order.Address{
									AddressLine1: "AL1",
								},
								To: order.Address{
									CountryCode: "SG",
								},
								GlobalShipperID:       remoteOriginShipperId.Uint,
								OriginGlobalShipperID: &remoteOriginShipperId.Uint,
							},
							Shipper:              &models.Shipper{ID: 1, RemoteID: remoteOriginShipperId, Country: null.StringFrom("SG")},
							ShipperDetectionType: orderConst.OrderShipperFromPartnerShippers,
							IsConsolidateFlow:    false,
							OrderFlow:            orderConst.FlowOnlyFirstMile,
							Stage:                vendorConst.StageFirstMile,
							SystemID:             "sg",
						})
						return vendorOrderCreator
					}(),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			o := &orderCreator{
				storeManager:  tt.fields.storeManager,
				shipperGetter: tt.fields.shipperGetter,
			}
			got, err := o.newOrderRequestItems(context.TODO(), tt.fields.request, tt.fields.ocConfig, tt.fields.config)
			if tt.wantErr != nil {
				assert.Equal(t, tt.wantErr, err)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("orderClient.newOrderRequestItems() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_orderClient_newParcel(t *testing.T) {
	t.Parallel()
	type fields struct {
		request             *order.BaseRequest
		prepared            *order_creation_interface.OrderCreationConfig
		config              *clientConfig
		storeManager        order_creation_interface.DbAccessor
		shipperGetter       order_creation_interface.ShipperFinder
		trackingIDGenerator order_creation_interface.TrackingIDGenerator
		parcelRepo          repo_interface.ParcelRepository
	}
	type args struct {
		reqItems []*order_creation_interface.RequestItem
	}

	ctrl := gomock.NewController(t)
	testCtx := context.TODO()

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *models.Parcel
		wantErr bool
	}{
		{
			name: "require tracking id if order flow is unknown",
			fields: fields{
				request: &order.BaseRequest{
					ParcelDetails: &models.ParcelDetails{},
				},
				prepared: &order_creation_interface.OrderCreationConfig{
					OrderFlow: orderConst.FlowUnknown,
					Service:   &models.Service{},
					Product:   &models.Product{},
					ProductVendors: repo_interface.ProductVendorWrapperSlice{
						{
							Vendor: models.Vendor{},
						},
					},
					OriginShipper: &models.Shipper{Country: null.StringFrom("SG")},
					Partner:       &models.Partner{},
				},
				config: &clientConfig{},
				storeManager: func() order_creation_interface.DbAccessor {
					m := orderCreationMocks.NewMockDbAccessor(ctrl)
					m.EXPECT().CreateParcelAndItemsAndVendorOrdersInDb(gomock.Any(), gomock.Any(), gomock.Any(), &order.BaseRequest{
						ParcelDetails: &models.ParcelDetails{},
					}, nil).Return(errors.New("can't"))

					return m
				}(),
				trackingIDGenerator: orderCreationMocks.NewMockTrackingIDGenerator(ctrl),
			},
			args: args{
				reqItems: []*order_creation_interface.RequestItem{
					{
						Vendor:           &models.Vendor{Handler: vendorConst.VendorHandlerNVCore},
						IsUpstreamVendor: true,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "require tracking id if client config condition mentioned",
			fields: fields{
				request: &order.BaseRequest{
					ParcelDetails: &models.ParcelDetails{},
				},
				prepared: &order_creation_interface.OrderCreationConfig{
					OrderFlow: orderConst.FlowOnlyFirstMile,
					Service:   &models.Service{},
					Product:   &models.Product{},
					ProductVendors: repo_interface.ProductVendorWrapperSlice{
						{
							Vendor: models.Vendor{},
						},
					},
					OriginShipper: &models.Shipper{Country: null.StringFrom("SG")},
					Partner:       &models.Partner{},
				},
				config: &clientConfig{
					IsRequiredTrackingNumber: true,
				},
				storeManager: func() order_creation_interface.DbAccessor {
					m := orderCreationMocks.NewMockDbAccessor(ctrl)
					m.EXPECT().CreateParcelAndItemsAndVendorOrdersInDb(gomock.Any(), gomock.Any(), gomock.Any(), &order.BaseRequest{
						ParcelDetails: &models.ParcelDetails{},
					}, nil).Return(errors.New("can't"))

					return m
				}(),
				trackingIDGenerator: orderCreationMocks.NewMockTrackingIDGenerator(ctrl),
			},
			args: args{
				reqItems: []*order_creation_interface.RequestItem{
					{
						Vendor:           &models.Vendor{Handler: vendorConst.VendorHandlerNVCore},
						IsUpstreamVendor: true,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "fail to create new parcel",
			fields: fields{
				request: &order.BaseRequest{},
				prepared: &order_creation_interface.OrderCreationConfig{
					OrderFlow: orderConst.FlowOnlyFirstMile,
					Service:   &models.Service{},
					Product:   &models.Product{},
					ProductVendors: repo_interface.ProductVendorWrapperSlice{
						{
							Vendor: models.Vendor{},
						},
					},
					OriginShipper: &models.Shipper{Country: null.StringFrom("SG")},
					Partner:       &models.Partner{},
				},
				config: &clientConfig{},
				storeManager: func() order_creation_interface.DbAccessor {
					isHighValue := false
					m := orderCreationMocks.NewMockDbAccessor(ctrl)
					m.EXPECT().GetExistParcel(gomock.Any(), gomock.Any()).Return(nil, nil)
					m.EXPECT().CreateParcelAndItemsAndVendorOrdersInDb(gomock.Any(), gomock.Any(), gomock.Any(), &order.BaseRequest{
						ParcelDetails: &models.ParcelDetails{IsHighValue: null.BoolFrom(isHighValue).Ptr(),
							RoutingType: fpl.RoutingType_FORWARD,
						},
					}, nil).Return(errors.New("can't"))

					return m
				}(),
				trackingIDGenerator: func() order_creation_interface.TrackingIDGenerator {
					m := orderCreationMocks.NewMockTrackingIDGenerator(ctrl)
					m.EXPECT().Generate(gomock.Any(), "FPL", 24).Return("FPL123ABC91890")

					return m
				}(),
			},
			args: args{
				reqItems: []*order_creation_interface.RequestItem{
					{
						Vendor:           &models.Vendor{Handler: vendorConst.VendorHandlerNVCore},
						IsUpstreamVendor: true,
						VendorAPIService: func() base.VendorOrderCreator {
							vendorService := integrationMocks.NewMockVendorOrderCreator(ctrl)
							return vendorService
						}(),
					},
				},
			},
			wantErr: true,
		},
		{
			name: "create new parcel successfully",
			fields: fields{
				request: &order.BaseRequest{
					RequestedTrackingID: "abc",
					From: order.Address{
						Name:          "Sender",
						AddressLine1:  "sad1",
						AddressLine2:  null.StringFrom("sad2").Ptr(),
						AddressLine3:  null.StringFrom("sad3").Ptr(),
						AddressLine4:  null.StringFrom("sad4").Ptr(),
						City:          null.StringFrom("ABC").Ptr(),
						CountryCode:   "SG",
						StateProvince: null.StringFrom("Singapore").Ptr(),
						PostCode:      null.StringFrom("12345").Ptr(),
						ContactNumber: null.StringFrom("+6512345678").Ptr(),
						ContactEmail:  null.StringFrom("<EMAIL>").Ptr(),
					},
					To: order.Address{
						Name:          "Receiver",
						AddressLine1:  "rad1",
						AddressLine2:  null.StringFrom("rad2").Ptr(),
						AddressLine3:  null.StringFrom("rad3").Ptr(),
						AddressLine4:  null.StringFrom("rad4").Ptr(),
						City:          null.StringFrom("ABC").Ptr(),
						CountryCode:   "VN",
						StateProvince: null.StringFrom("Ho Chi Minh").Ptr(),
						PostCode:      null.StringFrom("700000").Ptr(),
						ContactNumber: null.StringFrom("+***********").Ptr(),
						ContactEmail:  null.StringFrom("<EMAIL>").Ptr(),
					},
					ParcelDetails: &models.ParcelDetails{
						OriginCountry: null.StringFrom("SG").Ptr(),
						WarehouseCode: null.StringFrom("SK-01").Ptr(),
					},
					Delivery: &order.Delivery{
						StartDate: "9999-12-31",
						Timeslot: &order.Timeslot{
							StartTime: "09:00",
							EndTime:   "22:00",
							Timezone:  "Asia/Ho_Chi_Minh",
						},
						Instructions:   "dummy",
						CashOnDelivery: 11.11,
						InsuredValue:   22.22,
					},
					Pickup: &order.Pickup{
						Date: "9999-12-31",
						Timeslot: &order.Timeslot{
							StartTime: "09:00",
							EndTime:   "22:00",
							Timezone:  "Asia/Ho_Chi_Minh",
						},
						Address:      nil,
						ApproxVolume: null.Uint8From(0).Ptr(),
						Instructions: "",
					},
				},
				prepared: &order_creation_interface.OrderCreationConfig{
					OrderFlow: orderConst.FlowOnlyFirstMile,
					Service:   &models.Service{},
					Product:   &models.Product{},
					ProductVendors: repo_interface.ProductVendorWrapperSlice{
						{
							Vendor: models.Vendor{},
						},
					},
					OriginShipper: &models.Shipper{Country: null.StringFrom("SG")},
					Partner:       &models.Partner{ID: 100},
				},
				config: &clientConfig{
					IsRequiredTrackingNumber: true,
				},
				storeManager: func() order_creation_interface.DbAccessor {
					m := orderCreationMocks.NewMockDbAccessor(ctrl)
					m.EXPECT().GetExistParcel(gomock.Any(), gomock.Any()).Return(nil, nil)
					m.EXPECT().CreateParcelAndItemsAndVendorOrdersInDb(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).
						Return(nil)

					return m
				}(),
				trackingIDGenerator: func() order_creation_interface.TrackingIDGenerator {
					m := orderCreationMocks.NewMockTrackingIDGenerator(ctrl)

					return m
				}(),
			},
			args: args{
				reqItems: []*order_creation_interface.RequestItem{
					{
						Vendor:           &models.Vendor{Handler: vendorConst.VendorHandlerNVCore},
						IsUpstreamVendor: true,
						VendorAPIService: func() base.VendorOrderCreator {
							vendorService := integrationMocks.NewMockVendorOrderCreator(ctrl)
							return vendorService
						}(),
					},
				},
			},
			want: &models.Parcel{
				ServiceID:         null.UintFrom(0),
				ShipperID:         null.UintFrom(0),
				ProductID:         null.UintFrom(0),
				FromName:          null.StringFrom("Sender"),
				FromAddressLine1:  null.StringFrom("sad1"),
				FromAddressLine2:  null.StringFrom("sad2"),
				FromAddressLine3:  null.StringFrom("sad3"),
				FromAddressLine4:  null.StringFrom("sad4"),
				FromCity:          null.StringFrom("ABC"),
				FromCountryCode:   null.StringFrom("SG"),
				FromStateProvince: null.StringFrom("Singapore"),
				FromPostcode:      null.StringFrom("12345"),
				FromContactNumber: null.StringFrom("+6512345678"),
				FromContactEmail:  null.StringFrom("<EMAIL>"),
				ToName:            null.StringFrom("Receiver"),
				ToAddressLine1:    null.StringFrom("rad1"),
				ToAddressLine2:    null.StringFrom("rad2"),
				ToAddressLine3:    null.StringFrom("rad3"),
				ToAddressLine4:    null.StringFrom("rad4"),
				ToCity:            null.StringFrom("ABC"),
				ToCountryCode:     null.StringFrom("VN"),
				ToStateProvince:   null.StringFrom("Ho Chi Minh"),
				ToPostcode:        null.StringFrom("700000"),
				ToContactNumber:   null.StringFrom("+***********"),
				ToContactEmail:    null.StringFrom("<EMAIL>"),
				Source:            0,
				Type:              1,
				Metadata: null.StringFrom(string(utils.JsonMarshalIgnoreError(models.ParcelDetails{
					IsHighValue:         null.BoolFrom(false).Ptr(),
					WarehouseCode:       null.StringFrom("SK-01").Ptr(),
					RequestedTrackingID: "abc",
					OriginCountry:       null.StringFrom("SG").Ptr(),
					RoutingType:         fpl.RoutingType_FORWARD,
				}))),
				PickupInfo:   null.StringFrom(`{"pickup_date":"9999-12-31","pickup_timeslot":{"start_time":"09:00","end_time":"22:00","timezone":"Asia/Ho_Chi_Minh"},"pickup_address":null,"pickup_approx_volume":0,"pickup_instructions":""}`),
				DeliveryInfo: null.StringFrom(`{"delivery_start_date":"9999-12-31","delivery_timeslot":{"start_time":"09:00","end_time":"22:00","timezone":"Asia/Ho_Chi_Minh"},"delivery_instructions":"dummy","cash_on_delivery":11.11,"insured_value":22.22,"allow_self_collection":false}`),
				PartnerID:    null.Uint64From(100),
				TrackingID:   "abc",
			},
		},
		{
			name: "return success if parcel already exist",
			fields: fields{
				request: &order.BaseRequest{
					RequestedTrackingID: "abc",
					From: order.Address{
						Name:          "Sender",
						AddressLine1:  "sad1",
						AddressLine2:  null.StringFrom("sad2").Ptr(),
						AddressLine3:  null.StringFrom("sad3").Ptr(),
						AddressLine4:  null.StringFrom("sad4").Ptr(),
						City:          null.StringFrom("ABC").Ptr(),
						CountryCode:   "SG",
						StateProvince: null.StringFrom("Singapore").Ptr(),
						PostCode:      null.StringFrom("12345").Ptr(),
						ContactNumber: null.StringFrom("+6512345678").Ptr(),
						ContactEmail:  null.StringFrom("<EMAIL>").Ptr(),
					},
					To: order.Address{
						Name:          "Receiver",
						AddressLine1:  "rad1",
						AddressLine2:  null.StringFrom("rad2").Ptr(),
						AddressLine3:  null.StringFrom("rad3").Ptr(),
						AddressLine4:  null.StringFrom("rad4").Ptr(),
						City:          null.StringFrom("ABC").Ptr(),
						CountryCode:   "VN",
						StateProvince: null.StringFrom("Ho Chi Minh").Ptr(),
						PostCode:      null.StringFrom("700000").Ptr(),
						ContactNumber: null.StringFrom("+***********").Ptr(),
						ContactEmail:  null.StringFrom("<EMAIL>").Ptr(),
					},
					ParcelDetails: &models.ParcelDetails{
						OriginCountry: null.StringFrom("SG").Ptr(),
					},
					Delivery: &order.Delivery{
						StartDate: "9999-12-31",
						Timeslot: &order.Timeslot{
							StartTime: "09:00",
							EndTime:   "22:00",
							Timezone:  "Asia/Ho_Chi_Minh",
						},
						Instructions:   "dummy",
						CashOnDelivery: 11.11,
						InsuredValue:   22.22,
					},
					Pickup: &order.Pickup{
						Date: "9999-12-31",
						Timeslot: &order.Timeslot{
							StartTime: "09:00",
							EndTime:   "22:00",
							Timezone:  "Asia/Ho_Chi_Minh",
						},
						Address:      nil,
						ApproxVolume: null.Uint8From(0).Ptr(),
						Instructions: "",
					},
				},
				prepared: &order_creation_interface.OrderCreationConfig{
					OrderFlow: orderConst.FlowOnlyFirstMile,
					Service:   &models.Service{},
					Product:   &models.Product{},
					ProductVendors: repo_interface.ProductVendorWrapperSlice{
						{
							Vendor: models.Vendor{},
						},
					},
					OriginShipper: &models.Shipper{Country: null.StringFrom("SG")},
					Partner:       &models.Partner{ID: 100},
				},
				config: &clientConfig{
					IsRequiredTrackingNumber: true,
				},
				storeManager: func() order_creation_interface.DbAccessor {
					m := orderCreationMocks.NewMockDbAccessor(ctrl)
					m.EXPECT().GetExistParcel(gomock.Any(), gomock.Any()).Return(&models.Parcel{
						ServiceID:         null.UintFrom(0),
						ShipperID:         null.UintFrom(0),
						ProductID:         null.UintFrom(0),
						FromName:          null.StringFrom("Sender"),
						FromAddressLine1:  null.StringFrom("sad1"),
						FromAddressLine2:  null.StringFrom("sad2"),
						FromAddressLine3:  null.StringFrom("sad3"),
						FromAddressLine4:  null.StringFrom("sad4"),
						FromCity:          null.StringFrom("ABC"),
						FromCountryCode:   null.StringFrom("SG"),
						FromStateProvince: null.StringFrom("Singapore"),
						FromPostcode:      null.StringFrom("12345"),
						FromContactNumber: null.StringFrom("+6512345678"),
						FromContactEmail:  null.StringFrom("<EMAIL>"),
						ToName:            null.StringFrom("Receiver"),
						ToAddressLine1:    null.StringFrom("rad1"),
						ToAddressLine2:    null.StringFrom("rad2"),
						ToAddressLine3:    null.StringFrom("rad3"),
						ToAddressLine4:    null.StringFrom("rad4"),
						ToCity:            null.StringFrom("ABC"),
						ToCountryCode:     null.StringFrom("VN"),
						ToStateProvince:   null.StringFrom("Ho Chi Minh"),
						ToPostcode:        null.StringFrom("700000"),
						ToContactNumber:   null.StringFrom("+***********"),
						ToContactEmail:    null.StringFrom("<EMAIL>"),
						Source:            0,
						Type:              1,
						Metadata: null.StringFrom(string(utils.JsonMarshalIgnoreError(models.ParcelDetails{
							IsHighValue:         null.BoolFrom(false).Ptr(),
							RequestedTrackingID: "abc",
							OriginCountry:       null.StringFrom("SG").Ptr(),
							RoutingType:         fpl.RoutingType_FORWARD,
						}))),
						PickupInfo:   null.StringFrom(`{"pickup_date":"9999-12-31","pickup_timeslot":{"start_time":"09:00","end_time":"22:00","timezone":"Asia/Ho_Chi_Minh"},"pickup_address":null,"pickup_approx_volume":0,"pickup_instructions":""}`),
						DeliveryInfo: null.StringFrom(`{"delivery_start_date":"9999-12-31","delivery_timeslot":{"start_time":"09:00","end_time":"22:00","timezone":"Asia/Ho_Chi_Minh"},"delivery_instructions":"dummy","cash_on_delivery":11.11,"insured_value":22.22}`),
						PartnerID:    null.Uint64From(100),
						TrackingID:   "abc",
					}, nil)
					m.EXPECT().CreateParcelAndItemsAndVendorOrdersInDb(
						gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).
						Return(nil)

					return m
				}(),
				trackingIDGenerator: func() order_creation_interface.TrackingIDGenerator {
					m := orderCreationMocks.NewMockTrackingIDGenerator(ctrl)

					return m
				}(),
			},
			args: args{
				reqItems: []*order_creation_interface.RequestItem{
					{
						Vendor:           &models.Vendor{Handler: vendorConst.VendorHandlerNVCore},
						IsUpstreamVendor: true,
						VendorAPIService: func() base.VendorOrderCreator {
							vendorService := integrationMocks.NewMockVendorOrderCreator(ctrl)
							return vendorService
						}(),
					},
				},
			},
			want: &models.Parcel{
				ServiceID:         null.UintFrom(0),
				ShipperID:         null.UintFrom(0),
				ProductID:         null.UintFrom(0),
				FromName:          null.StringFrom("Sender"),
				FromAddressLine1:  null.StringFrom("sad1"),
				FromAddressLine2:  null.StringFrom("sad2"),
				FromAddressLine3:  null.StringFrom("sad3"),
				FromAddressLine4:  null.StringFrom("sad4"),
				FromCity:          null.StringFrom("ABC"),
				FromCountryCode:   null.StringFrom("SG"),
				FromStateProvince: null.StringFrom("Singapore"),
				FromPostcode:      null.StringFrom("12345"),
				FromContactNumber: null.StringFrom("+6512345678"),
				FromContactEmail:  null.StringFrom("<EMAIL>"),
				ToName:            null.StringFrom("Receiver"),
				ToAddressLine1:    null.StringFrom("rad1"),
				ToAddressLine2:    null.StringFrom("rad2"),
				ToAddressLine3:    null.StringFrom("rad3"),
				ToAddressLine4:    null.StringFrom("rad4"),
				ToCity:            null.StringFrom("ABC"),
				ToCountryCode:     null.StringFrom("VN"),
				ToStateProvince:   null.StringFrom("Ho Chi Minh"),
				ToPostcode:        null.StringFrom("700000"),
				ToContactNumber:   null.StringFrom("+***********"),
				ToContactEmail:    null.StringFrom("<EMAIL>"),
				Source:            0,
				Type:              1,
				Metadata: null.StringFrom(string(utils.JsonMarshalIgnoreError(models.ParcelDetails{
					IsHighValue:         null.BoolFrom(false).Ptr(),
					RequestedTrackingID: "abc",
					OriginCountry:       null.StringFrom("SG").Ptr(),
					RoutingType:         fpl.RoutingType_FORWARD,
				}))),
				PickupInfo:   null.StringFrom(`{"pickup_date":"9999-12-31","pickup_timeslot":{"start_time":"09:00","end_time":"22:00","timezone":"Asia/Ho_Chi_Minh"},"pickup_address":null,"pickup_approx_volume":0,"pickup_instructions":""}`),
				DeliveryInfo: null.StringFrom(`{"delivery_start_date":"9999-12-31","delivery_timeslot":{"start_time":"09:00","end_time":"22:00","timezone":"Asia/Ho_Chi_Minh"},"delivery_instructions":"dummy","cash_on_delivery":11.11,"insured_value":22.22,"allow_self_collection":false}`),
				PartnerID:    null.Uint64From(100),
				TrackingID:   "abc",
			},
		},
		{
			name: "create new parcel relabelled",
			fields: fields{
				request: &order.BaseRequest{
					RequestedTrackingID: "abc",
					RefTrackingID:       null.StringFrom("ORIGINTRACKING001").Ptr(),
					From: order.Address{
						Name:          "Sender",
						AddressLine1:  "sad1",
						AddressLine2:  null.StringFrom("sad2").Ptr(),
						AddressLine3:  null.StringFrom("sad3").Ptr(),
						AddressLine4:  null.StringFrom("sad4").Ptr(),
						City:          null.StringFrom("ABC").Ptr(),
						CountryCode:   "SG",
						StateProvince: null.StringFrom("Singapore").Ptr(),
						PostCode:      null.StringFrom("12345").Ptr(),
						ContactNumber: null.StringFrom("+6512345678").Ptr(),
						ContactEmail:  null.StringFrom("<EMAIL>").Ptr(),
					},
					To: order.Address{
						Name:          "Receiver",
						AddressLine1:  "rad1",
						AddressLine2:  null.StringFrom("rad2").Ptr(),
						AddressLine3:  null.StringFrom("rad3").Ptr(),
						AddressLine4:  null.StringFrom("rad4").Ptr(),
						City:          null.StringFrom("ABC").Ptr(),
						CountryCode:   "VN",
						StateProvince: null.StringFrom("Ho Chi Minh").Ptr(),
						PostCode:      null.StringFrom("700000").Ptr(),
						ContactNumber: null.StringFrom("+***********").Ptr(),
						ContactEmail:  null.StringFrom("<EMAIL>").Ptr(),
					},
					ParcelDetails: &models.ParcelDetails{
						ShipperSubmittedWeight:   null.Float64From(1.316).Ptr(),
						Weight:                   null.Float64From(2.15).Ptr(),
						ActualWeight:             null.Float64From(4.321).Ptr(),
						BatteryPacking:           2,
						BatteryType:              3,
						IsRelabel:                true,
						Value:                    null.Float64From(11.414).Ptr(),
						CustomsCurrency:          null.StringFrom("MYR").Ptr(),
						TaxID:                    null.StringFrom("14356").Ptr(),
						InvoiceURL:               null.StringFrom("http://abcd.xyzz").Ptr(),
						TradeTerms:               null.StringFrom("sample new trade term").Ptr(),
						CustomsDescription:       null.StringFrom("this is new description").Ptr(),
						CustomsNativeDescription: null.StringFrom("this is new native description").Ptr(),
						HSCode:                   null.UintFrom(1346).Ptr(),
						OriginCountry:            null.StringFrom("MY").Ptr(),
						Metadata:                 "{\"sample\":\"new value:\"}",
						Status:                   parcel.ParcelStatusDraft,
					},
					Delivery: &order.Delivery{
						StartDate: "9999-12-31",
						Timeslot: &order.Timeslot{
							StartTime: "09:00",
							EndTime:   "22:00",
							Timezone:  "Asia/Ho_Chi_Minh",
						},
						Instructions:   "dummy",
						CashOnDelivery: 11.11,
						InsuredValue:   22.22,
					},
					Pickup: &order.Pickup{
						Date: "9999-12-31",
						Timeslot: &order.Timeslot{
							StartTime: "09:00",
							EndTime:   "22:00",
							Timezone:  "Asia/Ho_Chi_Minh",
						},
						Address:      nil,
						ApproxVolume: null.Uint8From(0).Ptr(),
						Instructions: "",
					},
				},
				prepared: &order_creation_interface.OrderCreationConfig{
					OrderFlow: orderConst.FlowOnlyFirstMile,
					Service:   &models.Service{},
					Product:   &models.Product{},
					ProductVendors: repo_interface.ProductVendorWrapperSlice{
						{
							Vendor: models.Vendor{},
						},
					},
					OriginShipper: &models.Shipper{Country: null.StringFrom("SG")},
					Partner:       &models.Partner{ID: 100},
				},
				config: &clientConfig{
					IsRequiredTrackingNumber: true,
				},
				storeManager: func() order_creation_interface.DbAccessor {
					m := orderCreationMocks.NewMockDbAccessor(ctrl)
					m.EXPECT().GetExistParcel(gomock.Any(), gomock.Any()).Return(nil, nil)
					m.EXPECT().CreateParcelAndItemsAndVendorOrdersInDb(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).
						Return(nil)

					return m
				}(),
				trackingIDGenerator: func() order_creation_interface.TrackingIDGenerator {
					m := orderCreationMocks.NewMockTrackingIDGenerator(ctrl)

					return m
				}(),
				parcelRepo: func() repo_interface.ParcelRepository {
					r := &repositories.ParcelMock{}
					r.On("GetOne", mock.Anything, mock.Anything, mock.Anything).Return(&models.Parcel{
						Metadata: null.StringFrom(string(utils.JsonMarshalIgnoreError(models.ParcelDetails{
							IsHighValue:         null.BoolFrom(false).Ptr(),
							RequestedTrackingID: "abc",
							OriginCountry:       null.StringFrom("SG").Ptr(),
						}))),
					}, nil)
					return r
				}(),
			},
			args: args{
				reqItems: []*order_creation_interface.RequestItem{
					{
						Vendor:           &models.Vendor{Handler: vendorConst.VendorHandlerNVCore},
						IsUpstreamVendor: true,
						VendorAPIService: func() base.VendorOrderCreator {
							vendorService := integrationMocks.NewMockVendorOrderCreator(ctrl)
							return vendorService
						}(),
					},
				},
			},
			want: &models.Parcel{
				ServiceID:         null.UintFrom(0),
				ShipperID:         null.UintFrom(0),
				ProductID:         null.UintFrom(0),
				RefTrackingID:     null.StringFrom("ORIGINTRACKING001"),
				FromName:          null.StringFrom("Sender"),
				FromAddressLine1:  null.StringFrom("sad1"),
				FromAddressLine2:  null.StringFrom("sad2"),
				FromAddressLine3:  null.StringFrom("sad3"),
				FromAddressLine4:  null.StringFrom("sad4"),
				FromCity:          null.StringFrom("ABC"),
				FromCountryCode:   null.StringFrom("SG"),
				FromStateProvince: null.StringFrom("Singapore"),
				FromPostcode:      null.StringFrom("12345"),
				FromContactNumber: null.StringFrom("+6512345678"),
				FromContactEmail:  null.StringFrom("<EMAIL>"),
				ToName:            null.StringFrom("Receiver"),
				ToAddressLine1:    null.StringFrom("rad1"),
				ToAddressLine2:    null.StringFrom("rad2"),
				ToAddressLine3:    null.StringFrom("rad3"),
				ToAddressLine4:    null.StringFrom("rad4"),
				ToCity:            null.StringFrom("ABC"),
				ToCountryCode:     null.StringFrom("VN"),
				ToStateProvince:   null.StringFrom("Ho Chi Minh"),
				ToPostcode:        null.StringFrom("700000"),
				ToContactNumber:   null.StringFrom("+***********"),
				ToContactEmail:    null.StringFrom("<EMAIL>"),
				Source:            0,
				Type:              1,
				Metadata: null.StringFrom(string(utils.JsonMarshalIgnoreError(models.ParcelDetails{
					ShipperSubmittedWeight:   null.Float64From(1.316).Ptr(),
					Weight:                   null.Float64From(2.15).Ptr(),
					ActualWeight:             null.Float64From(4.321).Ptr(),
					BatteryPacking:           2,
					BatteryType:              3,
					IsRelabel:                true,
					IsHighValue:              null.BoolFrom(false).Ptr(),
					Value:                    null.Float64From(11.414).Ptr(),
					CustomsCurrency:          null.StringFrom("MYR").Ptr(),
					TaxID:                    null.StringFrom("14356").Ptr(),
					InvoiceURL:               null.StringFrom("http://abcd.xyzz").Ptr(),
					TradeTerms:               null.StringFrom("sample new trade term").Ptr(),
					CustomsDescription:       null.StringFrom("this is new description").Ptr(),
					CustomsNativeDescription: null.StringFrom("this is new native description").Ptr(),
					HSCode:                   null.UintFrom(1346).Ptr(),
					RequestedTrackingID:      "abc",
					OriginCountry:            null.StringFrom("MY").Ptr(),
					Metadata:                 "{\"sample\":\"new value:\"}",
					RoutingType:              fpl.RoutingType_RELABEL,
				}))),
				PickupInfo:   null.StringFrom(`{"pickup_date":"9999-12-31","pickup_timeslot":{"start_time":"09:00","end_time":"22:00","timezone":"Asia/Ho_Chi_Minh"},"pickup_address":null,"pickup_approx_volume":0,"pickup_instructions":""}`),
				DeliveryInfo: null.StringFrom(`{"delivery_start_date":"9999-12-31","delivery_timeslot":{"start_time":"09:00","end_time":"22:00","timezone":"Asia/Ho_Chi_Minh"},"delivery_instructions":"dummy","cash_on_delivery":11.11,"insured_value":22.22,"allow_self_collection":false}`),
				PartnerID:    null.Uint64From(100),
				TrackingID:   "abc",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &orderCreator{
				storeManager:        tt.fields.storeManager,
				shipperGetter:       tt.fields.shipperGetter,
				trackingIDGenerator: tt.fields.trackingIDGenerator,
				parcelRepo:          tt.fields.parcelRepo,
			}
			got, err := o.CreateParcelAndItemsAndVendorOrdersInDb(testCtx, tt.args.reqItems, tt.fields.request, tt.fields.prepared, tt.fields.config, nil)
			if (err != nil) != tt.wantErr {
				t.Errorf("orderClient.CreateParcelAndItemsAndVendorOrdersInDb() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("got  %+v, \n-----------------------want %+v", got, tt.want)
			}
		})
	}
}

func Test_clientCoordinator_performOrderRequests(t *testing.T) {
	type args struct {
		reqItems []*order_creation_interface.RequestItem
		parcel   *models.Parcel
	}

	type fields struct {
		config  *clientConfig
		request *order.BaseRequest
	}

	newOrderResponseMock := func(originOrder *nvcore.OrderResponse) base.OCResponse {
		resp := integrationMocks.NewMockOCResponse(gomock.NewController(t))
		resp.EXPECT().GetHeader().Return("").AnyTimes()
		resp.EXPECT().GetBody().Return("").AnyTimes()
		resp.EXPECT().GetHTTPCode().Return(200).AnyTimes()
		resp.EXPECT().GetVendorTrackingID().Return("not_empty_string").AnyTimes()
		resp.EXPECT().GetErrorMsg().Return("").AnyTimes()

		return resp
	}

	ctrl := gomock.NewController(t)

	tests := []struct {
		name                                           string
		fields                                         fields
		args                                           args
		mockUpdateVendorOrderAndCreateVendorRequestErr error
		mockPersistUpdatedParcelErr                    error
		wantErr                                        bool
	}{
		{
			name: "do nothing if request items are empty",
			fields: fields{
				request: &order.BaseRequest{},
			},
			args: args{
				parcel: &models.Parcel{},
			},
		},
		{
			name: "do nothing if request items has no order-creatable item",
			fields: fields{
				request: &order.BaseRequest{},
			},
			args: args{
				reqItems: []*order_creation_interface.RequestItem{
					{},
				},
				parcel: &models.Parcel{},
			},
		},
		{
			name: "set request and response properly and call store manager but got error",
			fields: fields{
				config:  &clientConfig{},
				request: &order.BaseRequest{},
			},
			args: args{
				reqItems: []*order_creation_interface.RequestItem{
					{
						IsUpstreamVendor: true,
						Vendor:           &models.Vendor{ID: 1, Handler: vendorConst.VendorHandlerNVCore},
						VendorOrder:      &models.VendorOrder{ID: 1},
						OrderableShipper: &models.Shipper{ID: 1},
						VendorAPIService: func() base.VendorOrderCreator {
							vendorService := integrationMocks.NewMockVendorOrderCreator(ctrl)
							vendorService.EXPECT().Create(gomock.Any(), gomock.Any()).Return(
								newOrderResponseMock(&nvcore.OrderResponse{}),
								base.OrderRequestGettableMock(),
								errors.New("ayzo"),
							).AnyTimes()
							return vendorService
						}(),
					},
				},
				parcel: &models.Parcel{},
			},
			wantErr: true,
		},
		{
			name: "set request and response properly and call store manager",
			fields: fields{
				config:  &clientConfig{},
				request: &order.BaseRequest{},
			},
			args: args{
				reqItems: []*order_creation_interface.RequestItem{
					{
						IsUpstreamVendor: true,
						Vendor:           &models.Vendor{ID: 1, Handler: vendorConst.VendorHandlerNVCore},
						VendorOrder:      &models.VendorOrder{ID: 1},
						OrderableShipper: &models.Shipper{ID: 1},
						VendorAPIService: func() base.VendorOrderCreator {
							vendorService := integrationMocks.NewMockVendorOrderCreator(ctrl)
							vendorService.EXPECT().Create(gomock.Any(), gomock.Any()).Return(
								newOrderResponseMock(&nvcore.OrderResponse{}),
								base.OrderRequestGettableMock(),
								nil,
							).AnyTimes()
							return vendorService
						}(),
					},
				},
				parcel: &models.Parcel{},
			},
			mockUpdateVendorOrderAndCreateVendorRequestErr: errors.New("couldn't update parcel/vendor order/vendor request"),
			wantErr: true,
		},
		{
			name: "failed to persist parcel",
			fields: fields{
				config:  &clientConfig{},
				request: &order.BaseRequest{},
			},
			args: args{
				reqItems: []*order_creation_interface.RequestItem{
					0: {
						IsUpstreamVendor: true,
						ProductVendor:    &models.ProductVendor{Stage: null.StringFrom("FM")},
						Vendor:           &models.Vendor{ID: 1, Handler: vendorConst.VendorHandlerNVCore},
						VendorOrder:      &models.VendorOrder{ID: 1},
						OrderableShipper: &models.Shipper{ID: 1},
						VendorAPIService: func() base.VendorOrderCreator {
							vendorService := integrationMocks.NewMockVendorOrderCreator(ctrl)
							vendorService.EXPECT().Create(gomock.Any(), gomock.Any()).Return(
								newOrderResponseMock(&nvcore.OrderResponse{}),
								base.OrderRequestGettableMock(),
								nil,
							).AnyTimes()
							return vendorService
						}(),
					},
					1: {
						ProductVendor:    &models.ProductVendor{Stage: null.StringFrom("LM")},
						Vendor:           &models.Vendor{ID: 2, Handler: vendorConst.VendorHandlerNVCore},
						VendorOrder:      &models.VendorOrder{ID: 2},
						OrderableShipper: &models.Shipper{ID: 2},
						VendorAPIService: func() base.VendorOrderCreator {
							vendorService := integrationMocks.NewMockVendorOrderCreator(ctrl)
							vendorService.EXPECT().Create(gomock.Any(), gomock.Any()).Return(
								newOrderResponseMock(&nvcore.OrderResponse{}),
								base.OrderRequestGettableMock(),
								nil,
							).AnyTimes()
							return vendorService
						}(),
					},
				},
				parcel: &models.Parcel{},
			},
			mockUpdateVendorOrderAndCreateVendorRequestErr: nil,
			mockPersistUpdatedParcelErr:                    errors.New("internal server error"),
			wantErr:                                        true,
		},
		{
			name: "FM request is response with a tracking ID, the next request should use that as a payload requested tracking ID",
			fields: fields{
				config: &clientConfig{},
				request: &order.BaseRequest{
					Delivery: &order.Delivery{},
				},
			},
			args: args{
				reqItems: []*order_creation_interface.RequestItem{
					0: {
						IsUpstreamVendor: true,
						ProductVendor:    &models.ProductVendor{Stage: null.StringFrom("FM")},
						Vendor:           &models.Vendor{ID: 1, Handler: vendorConst.VendorHandlerNVCore},
						VendorOrder:      &models.VendorOrder{ID: 1},
						OrderableShipper: &models.Shipper{ID: 1},
						VendorAPIService: func() base.VendorOrderCreator {
							vendorService := integrationMocks.NewMockVendorOrderCreator(ctrl)
							vendorService.EXPECT().Create(gomock.Any(), gomock.Any()).Return(
								newOrderResponseMock(&nvcore.OrderResponse{}),
								base.OrderRequestGettableMock(),
								nil,
							).AnyTimes()
							return vendorService
						}(),
					},
					1: {
						ProductVendor:    &models.ProductVendor{Stage: null.StringFrom("LM")},
						Vendor:           &models.Vendor{ID: 2, Handler: vendorConst.VendorHandlerNVCore},
						VendorOrder:      &models.VendorOrder{ID: 2},
						OrderableShipper: &models.Shipper{ID: 2},
						VendorAPIService: func() base.VendorOrderCreator {
							vendorService := integrationMocks.NewMockVendorOrderCreator(ctrl)
							vendorService.EXPECT().Create(gomock.Any(), gomock.Any()).Return(
								newOrderResponseMock(&nvcore.OrderResponse{}),
								base.OrderRequestGettableMock(),
								nil,
							).AnyTimes()
							return vendorService
						}(),
					},
				},
				parcel: &models.Parcel{},
			},
			mockUpdateVendorOrderAndCreateVendorRequestErr: nil,
			mockPersistUpdatedParcelErr:                    nil,
			wantErr:                                        false,
		},
	}

	var ctx = reflect.TypeOf((*context.Context)(nil)).Elem()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			storeManager := orderCreationMocks.NewMockDbAccessor(ctrl)
			for _, ri := range tt.args.reqItems {
				storeManager.EXPECT().UpdateVendorOrderAndCreateVendorRequest(
					gomock.AssignableToTypeOf(ctx), ri.VendorOrder, gomock.Any(),
				).Return(tt.mockUpdateVendorOrderAndCreateVendorRequestErr)
			}
			storeManager.EXPECT().PersistUpdatedParcel(
				gomock.AssignableToTypeOf(ctx), tt.args.parcel, nil,
			).Return(tt.mockPersistUpdatedParcelErr)

			defer repositories.InitInMemoryDBWithTables().Close()
			w := &orderCreator{
				storeManager:               storeManager,
				parcelCreationEventCreator: async_task.NewFPLParcelCreationCreator(),
			}
			_, err := w.performOrderRequests(context.TODO(), tt.args.parcel, tt.args.reqItems, tt.fields.request, &order_creation_interface.OrderCreationConfig{
				Partner: &models.Partner{},
			}, nil)
			if (err != nil) != tt.wantErr {
				t.Errorf("clientCoordinator.performOrderRequests() error = %v, wantErr %v", err, tt.wantErr)
			}
			if err == nil {

				res, err := models.AsyncTaskTabs().AllG(context.Background())
				require.Nil(t, err)
				assert.Equal(t, 1, len(res))
			}
		})
	}
}

func Test_orderClient_performOrderRequest(t *testing.T) {
	t.Parallel()
	type fields struct {
		storeManager order_creation_interface.DbAccessor
	}
	type args struct {
		ctx     context.Context
		reqItem *order_creation_interface.RequestItem
		parcel  *models.Parcel
	}

	var nilResponder *nvcore.OrderResponse
	duplicatedErr := fplerror.ErrBadRequest
	duplicatedErr.SetCode(nvcore.CoreOCDuplicatedErrorMagicCode)

	ctrl := gomock.NewController(t)

	tests := []struct {
		name   string
		fields fields
		args   args

		duplicated bool
		wantErr    error
	}{
		{
			name: "duplicated tracking id and cannot retrieve such order",
			args: args{
				reqItem: &order_creation_interface.RequestItem{
					VendorAPIService: func() base.VendorOrderCreator {
						vendorService := integrationMocks.NewMockVendorOrderCreator(ctrl)
						vendorService.EXPECT().Create(gomock.Any(), gomock.Any()).Return(
							nilResponder,
							base.OrderRequestGettableMock(),
							duplicatedErr.NewWithoutStack("Tracking ID (FPLOCKNJTH0103089071) has been used, please use a different tracking ID and try again"),
						).AnyTimes()

						vendorService.EXPECT().GetByRequestedTrackingID(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
						return vendorService
					}(),
					Vendor: &models.Vendor{
						Handler: vendorConst.VendorHandlerNVCore,
					},
					VendorOrder:      &models.VendorOrder{},
					OrderableShipper: &models.Shipper{},
					Request:          &order.BaseRequest{},
				},
			},
			fields: fields{
				storeManager: func() order_creation_interface.DbAccessor {
					storeManager := orderCreationMocks.NewMockDbAccessor(ctrl)
					storeManager.EXPECT().UpdateVendorOrderAndCreateVendorRequest(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
					return storeManager
				}(),
			},
			duplicated: true,
			wantErr:    duplicatedErr.NewWithoutStack("Tracking ID (FPLOCKNJTH0103089071) has been used, please use a different tracking ID and try again"),
		},
		{
			name: "duplicated tracking id and can retrieve such order",
			args: args{
				reqItem: &order_creation_interface.RequestItem{
					VendorAPIService: func() base.VendorOrderCreator {
						vendorService := integrationMocks.NewMockVendorOrderCreator(ctrl)
						vendorService.EXPECT().Create(gomock.Any(), gomock.Any()).Return(
							nilResponder,
							base.OrderRequestGettableMock(),
							duplicatedErr.NewWithoutStack("Tracking ID (FPLOCKNJTH0103089071) has been used, please use a different tracking ID and try again"),
						).AnyTimes()

						vendorService.EXPECT().GetByRequestedTrackingID(gomock.Any(), gomock.Any()).Return(&nvcore.OrderResponse{}, nil).AnyTimes()
						return vendorService
					}(),
					Vendor: &models.Vendor{
						Handler: vendorConst.VendorHandlerNVCore,
					},
					VendorOrder:      &models.VendorOrder{},
					OrderableShipper: &models.Shipper{},
					Request:          &order.BaseRequest{},
				},
				parcel: &models.Parcel{},
			},
			fields: fields{
				storeManager: func() order_creation_interface.DbAccessor {
					storeManager := orderCreationMocks.NewMockDbAccessor(ctrl)
					storeManager.EXPECT().UpdateVendorOrderAndCreateVendorRequest(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
					return storeManager
				}(),
			},
			duplicated: true,
			wantErr:    nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &orderCreator{
				storeManager: tt.fields.storeManager,
			}

			duplicated, err := o.performOrderRequest(tt.args.ctx, tt.args.reqItem, tt.args.parcel, nil)
			assert.Equal(t, tt.duplicated, duplicated)
			assert.Equal(t, tt.wantErr, err)
		})
	}
}

func Test_isDuplicatedError(t *testing.T) {
	t.Parallel()
	type args struct {
		err     error
		reqItem *order_creation_interface.RequestItem
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "error is not tplerr",
			args: args{
				reqItem: &order_creation_interface.RequestItem{
					Vendor: &models.Vendor{
						Handler: vendorConst.VendorHandlerNVCore,
					},
				},
				err: errors.New("internal server error"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isDuplicatedError(tt.args.err, tt.args.reqItem); got != tt.want {
				t.Errorf("isDuplicatedError() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getShipperTaxNumber(t *testing.T) {
	t.Parallel()
	type args struct {
		taxName string
		taxes   map[string]string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "tax name is not existed",
			args: args{
				taxes:   make(map[string]string),
				taxName: "lvg",
			},
			want: "",
		},
		{
			name: "tax name is not existed",
			args: args{
				taxes:   map[string]string{"lvg": "  1234  "},
				taxName: "lvg",
			},
			want: "1234",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getShipperTaxNumber(tt.args.taxName, tt.args.taxes); got != tt.want {
				t.Errorf("getShipperTaxNumber() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_orderClient_fillShipperTaxNumber(t *testing.T) {
	t.Parallel()
	type args struct {
		taxName          string
		shipperTaxNumber string
		request          *order.BaseRequest
		shipper          *models.Shipper
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "happy case",
			args: args{
				request: &order.BaseRequest{
					Items: order.ParcelItemSlice{
						nil,
						{},
						{
							GstRegistrationNumber: new(string),
							Taxes: map[order.TaxName]order.TaxInfo{
								"gst": {
									Number: new(string),
								},
							},
						},
					},
				},
				shipper:          &models.Shipper{},
				taxName:          "gst",
				shipperTaxNumber: "1234",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &orderCreator{}
			o.fillShipperTaxNumber(context.TODO(), tt.args.taxName, tt.args.shipperTaxNumber, tt.args.request, tt.args.shipper)
		})
	}
}

func Test_orderClient_autoFillTaxes(t *testing.T) {
	t.Parallel()
	type args struct {
		request *order.BaseRequest
		shipper *models.Shipper
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "nil data",
		},
		{
			name: "shipper tax numbers is not valid",
			args: args{
				shipper: &models.Shipper{},
				request: &order.BaseRequest{},
			},
		},
		{
			name: "shipper tax numbers is not json format",
			args: args{
				shipper: &models.Shipper{
					TaxNumbers: null.StringFrom("abc"),
				},
				request: &order.BaseRequest{},
			},
		},
		{
			name: "shipper tax number is empty",
			args: args{
				shipper: &models.Shipper{
					TaxNumbers: null.StringFrom("{}"),
				},
				request: &order.BaseRequest{
					To: order.Address{
						CountryCode: utils.CountryCodeSG,
					},
				},
			},
		},
		{
			name: "happy case",
			args: args{
				shipper: &models.Shipper{
					TaxNumbers: null.StringFrom(`{"gst":"1234"}`),
				},
				request: &order.BaseRequest{
					To: order.Address{
						CountryCode: utils.CountryCodeSG,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := &orderCreator{}
			o.autoFillTaxes(context.TODO(), tt.args.request, tt.args.shipper)
		})
	}
}

func Test_getParcelDetailMerged(t *testing.T) {
	t.Parallel()
	type args struct {
		originParcelDetail  *models.ParcelDetails
		requestParcelDetail *models.ParcelDetails
	}
	tests := []struct {
		name string
		args args
		want *models.ParcelDetails
	}{
		{
			name: "origin detail empty",
			args: args{
				originParcelDetail: &models.ParcelDetails{
					Status: parcel.ParcelStatusSuccess,
				},
				requestParcelDetail: &models.ParcelDetails{
					ShipperSubmittedWeight:   null.Float64From(1.2).Ptr(),
					Weight:                   null.Float64From(1.1).Ptr(),
					ActualWeight:             null.Float64From(3.42).Ptr(),
					BatteryPacking:           1,
					BatteryType:              2,
					IsRelabel:                false,
					Value:                    null.Float64From(13.424).Ptr(),
					CustomsCurrency:          null.StringFrom("VND").Ptr(),
					TaxID:                    null.StringFrom("12345").Ptr(),
					InvoiceURL:               null.StringFrom("http://abc.xyz").Ptr(),
					TradeTerms:               null.StringFrom("sample trade term").Ptr(),
					CustomsDescription:       null.StringFrom("this is description").Ptr(),
					CustomsNativeDescription: null.StringFrom("this is native description").Ptr(),
					HSCode:                   null.UintFrom(1234).Ptr(),
					OriginCountry:            null.StringFrom("VN").Ptr(),
					Metadata:                 "{\"sample\":\"value:\"}",
					Status:                   parcel.ParcelStatusDraft,
				},
			},
			want: &models.ParcelDetails{
				ShipperSubmittedWeight:   null.Float64From(1.2).Ptr(),
				Weight:                   null.Float64From(1.1).Ptr(),
				ActualWeight:             null.Float64From(3.42).Ptr(),
				BatteryPacking:           1,
				BatteryType:              2,
				IsRelabel:                true,
				Value:                    null.Float64From(13.424).Ptr(),
				CustomsCurrency:          null.StringFrom("VND").Ptr(),
				TaxID:                    null.StringFrom("12345").Ptr(),
				InvoiceURL:               null.StringFrom("http://abc.xyz").Ptr(),
				TradeTerms:               null.StringFrom("sample trade term").Ptr(),
				CustomsDescription:       null.StringFrom("this is description").Ptr(),
				CustomsNativeDescription: null.StringFrom("this is native description").Ptr(),
				HSCode:                   null.UintFrom(1234).Ptr(),
				OriginCountry:            null.StringFrom("VN").Ptr(),
				Metadata:                 "{\"sample\":\"value:\"}",
				Status:                   parcel.ParcelStatusDraft,
			},
		},
		{
			name: "request detail empty",
			args: args{
				requestParcelDetail: &models.ParcelDetails{
					Status: parcel.ParcelStatusDraft,
				},
				originParcelDetail: &models.ParcelDetails{
					ShipperSubmittedWeight:   null.Float64From(1.2).Ptr(),
					Weight:                   null.Float64From(1.1).Ptr(),
					ActualWeight:             null.Float64From(3.42).Ptr(),
					BatteryPacking:           1,
					BatteryType:              2,
					IsRelabel:                false,
					Value:                    null.Float64From(13.424).Ptr(),
					CustomsCurrency:          null.StringFrom("VND").Ptr(),
					TaxID:                    null.StringFrom("12345").Ptr(),
					InvoiceURL:               null.StringFrom("http://abc.xyz").Ptr(),
					TradeTerms:               null.StringFrom("sample trade term").Ptr(),
					CustomsDescription:       null.StringFrom("this is description").Ptr(),
					CustomsNativeDescription: null.StringFrom("this is native description").Ptr(),
					HSCode:                   null.UintFrom(1234).Ptr(),
					OriginCountry:            null.StringFrom("VN").Ptr(),
					Metadata:                 "{\"sample\":\"value:\"}",
					Status:                   parcel.ParcelStatusSuccess,
				},
			},
			want: &models.ParcelDetails{
				ShipperSubmittedWeight:   null.Float64From(1.2).Ptr(),
				Weight:                   null.Float64From(1.1).Ptr(),
				ActualWeight:             null.Float64From(3.42).Ptr(),
				BatteryPacking:           1,
				BatteryType:              2,
				IsRelabel:                true,
				Value:                    null.Float64From(13.424).Ptr(),
				CustomsCurrency:          null.StringFrom("VND").Ptr(),
				TaxID:                    null.StringFrom("12345").Ptr(),
				InvoiceURL:               null.StringFrom("http://abc.xyz").Ptr(),
				TradeTerms:               null.StringFrom("sample trade term").Ptr(),
				CustomsDescription:       null.StringFrom("this is description").Ptr(),
				CustomsNativeDescription: null.StringFrom("this is native description").Ptr(),
				HSCode:                   null.UintFrom(1234).Ptr(),
				OriginCountry:            null.StringFrom("VN").Ptr(),
				Metadata:                 "{\"sample\":\"value:\"}",
				Status:                   parcel.ParcelStatusDraft,
			},
		},
		{
			name: "request overwrite origin empty",
			args: args{
				requestParcelDetail: &models.ParcelDetails{
					ShipperSubmittedWeight:   null.Float64From(1.316).Ptr(),
					Weight:                   null.Float64From(2.15).Ptr(),
					ActualWeight:             null.Float64From(4.321).Ptr(),
					BatteryPacking:           2,
					BatteryType:              3,
					IsRelabel:                true,
					Value:                    null.Float64From(11.414).Ptr(),
					CustomsCurrency:          null.StringFrom("MYR").Ptr(),
					TaxID:                    null.StringFrom("14356").Ptr(),
					InvoiceURL:               null.StringFrom("http://abcd.xyzz").Ptr(),
					TradeTerms:               null.StringFrom("sample new trade term").Ptr(),
					CustomsDescription:       null.StringFrom("this is new description").Ptr(),
					CustomsNativeDescription: null.StringFrom("this is new native description").Ptr(),
					HSCode:                   null.UintFrom(1346).Ptr(),
					OriginCountry:            null.StringFrom("MY").Ptr(),
					Metadata:                 "{\"sample\":\"new value:\"}",
					Status:                   parcel.ParcelStatusDraft,
				},
				originParcelDetail: &models.ParcelDetails{
					ShipperSubmittedWeight:   null.Float64From(1.2).Ptr(),
					Weight:                   null.Float64From(1.1).Ptr(),
					ActualWeight:             null.Float64From(3.42).Ptr(),
					BatteryPacking:           1,
					BatteryType:              2,
					IsRelabel:                false,
					Value:                    null.Float64From(13.424).Ptr(),
					CustomsCurrency:          null.StringFrom("VND").Ptr(),
					TaxID:                    null.StringFrom("12345").Ptr(),
					InvoiceURL:               null.StringFrom("http://abc.xyz").Ptr(),
					TradeTerms:               null.StringFrom("sample trade term").Ptr(),
					CustomsDescription:       null.StringFrom("this is description").Ptr(),
					CustomsNativeDescription: null.StringFrom("this is native description").Ptr(),
					HSCode:                   null.UintFrom(1234).Ptr(),
					OriginCountry:            null.StringFrom("VN").Ptr(),
					Metadata:                 "{\"sample\":\"value:\"}",
					Status:                   parcel.ParcelStatusSuccess,
				},
			},
			want: &models.ParcelDetails{
				ShipperSubmittedWeight:   null.Float64From(1.316).Ptr(),
				Weight:                   null.Float64From(2.15).Ptr(),
				ActualWeight:             null.Float64From(4.321).Ptr(),
				BatteryPacking:           2,
				BatteryType:              3,
				IsRelabel:                true,
				Value:                    null.Float64From(11.414).Ptr(),
				CustomsCurrency:          null.StringFrom("MYR").Ptr(),
				TaxID:                    null.StringFrom("14356").Ptr(),
				InvoiceURL:               null.StringFrom("http://abcd.xyzz").Ptr(),
				TradeTerms:               null.StringFrom("sample new trade term").Ptr(),
				CustomsDescription:       null.StringFrom("this is new description").Ptr(),
				CustomsNativeDescription: null.StringFrom("this is new native description").Ptr(),
				HSCode:                   null.UintFrom(1346).Ptr(),
				OriginCountry:            null.StringFrom("MY").Ptr(),
				Metadata:                 "{\"sample\":\"new value:\"}",
				Status:                   parcel.ParcelStatusDraft,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := mergeParcelDetail(tt.args.originParcelDetail, tt.args.requestParcelDetail)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("mergeParcelDetail() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_orderClient_isHighValueParcel(t *testing.T) {
	t.Parallel()
	type fields struct {
		request       *order.BaseRequest
		prepared      *order_creation_interface.OrderCreationConfig
		config        *clientConfig
		storeManager  order_creation_interface.DbAccessor
		shipperGetter order_creation_interface.ShipperFinder
	}

	var (
		smallFloatNumber = 1.0
		bigFloatNumber   = 100000.0
		quantity         = 2
		toMyAddress      = order.Address{
			Name:         "",
			AddressLine1: "Malaysia",
			CountryCode:  "MY",
		}
	)

	tests := []struct {
		name    string
		fields  fields
		want    bool
		wantErr bool
	}{
		{
			name: "parcel is really high value in case items touch high value",
			fields: fields{
				request: &order.BaseRequest{
					To: toMyAddress,
					ParcelDetails: &models.ParcelDetails{
						Value: null.Float64From(smallFloatNumber).Ptr(),
					},
					Items: []*order.ParcelItem{
						{
							UnitValue: &bigFloatNumber,
							Quantity:  &quantity,
						},
					},
				},
			},
			want: true,
		},
		{
			name: "parcel is really high value in case parcel touches high value",
			fields: fields{
				request: &order.BaseRequest{
					To: toMyAddress,
					ParcelDetails: &models.ParcelDetails{
						Value: null.Float64From(bigFloatNumber).Ptr(),
					},
					Items: []*order.ParcelItem{
						{
							UnitValue: &smallFloatNumber,
							Quantity:  &quantity,
						},
					},
				},
			},
			want: true,
		},
		{
			name: "parcel is not high value",
			fields: fields{
				request: &order.BaseRequest{
					To: toMyAddress,
					ParcelDetails: &models.ParcelDetails{
						Value: null.Float64From(smallFloatNumber).Ptr(),
					},
					Items: []*order.ParcelItem{
						{
							UnitValue: &smallFloatNumber,
							Quantity:  &quantity,
						},
					},
				},
			},
			want: false,
		},
		{
			name: "parcel is not high value in case destination country is not found in the high value list",
			fields: fields{
				request: &order.BaseRequest{
					To: order.Address{
						CountryCode: "TW",
					},
					ParcelDetails: &models.ParcelDetails{
						Value: null.Float64From(bigFloatNumber).Ptr(),
					},
					Items: []*order.ParcelItem{
						{
							UnitValue: &bigFloatNumber,
							Quantity:  &quantity,
						},
					},
				},
			},
			want: false,
		},
		{
			name: "parcel is high value in case items field is null and parcel value is bigger than de minims value",
			fields: fields{
				request: &order.BaseRequest{
					To: toMyAddress,
					ParcelDetails: &models.ParcelDetails{
						Value: null.Float64From(bigFloatNumber).Ptr(),
					},
					Items: nil,
				},
			},
			want: true,
		},
		{
			name: "parcel is not high value in case to country code is null",
			fields: fields{
				request: &order.BaseRequest{
					To: order.Address{},
					ParcelDetails: &models.ParcelDetails{
						Value: null.Float64From(bigFloatNumber).Ptr(),
					},
					Items: nil,
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := IsHighValueParcel(tt.fields.request.ParcelDetails.Value, tt.fields.request.Items, null.StringFrom(tt.fields.request.To.CountryCode))
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("isHighValueParcel() = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func Test_fillTaxesToParcelItem(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		item          *models.ParcelItem
		shipper       *models.Shipper
		toCountryCode string
		want          *models.ParcelItem
	}{
		{
			name: "happy path - SG GST tax",
			item: &models.ParcelItem{
				Metadata: "{}",
			},
			shipper: &models.Shipper{
				TaxNumbers: null.StringFrom(`{"gst": "GST123456"}`),
			},
			toCountryCode: "SG",
			want: &models.ParcelItem{
				Metadata: `{"taxes":{"gst":{"number":"GST123456"}},"gst_registration_number":"GST123456"}`,
			},
		},
		{
			name: "invalid tax numbers json",
			item: &models.ParcelItem{
				Metadata: "{}",
			},
			shipper: &models.Shipper{
				TaxNumbers: null.StringFrom(`invalid json`),
			},
			toCountryCode: "SG",
			want: &models.ParcelItem{
				Metadata: "{}",
			},
		},
		{
			name: "empty tax numbers",
			item: &models.ParcelItem{
				Metadata: "{}",
			},
			shipper: &models.Shipper{
				TaxNumbers: null.String{},
			},
			toCountryCode: "SG",
			want: &models.ParcelItem{
				Metadata: "{}",
			},
		},
		{
			name: "country without tax configuration",
			item: &models.ParcelItem{
				Metadata: "{}",
			},
			shipper: &models.Shipper{
				TaxNumbers: null.StringFrom(`{"gst": "GST123456"}`),
			},
			toCountryCode: "XX",
			want: &models.ParcelItem{
				Metadata: "{}",
			},
		},
		{
			name: "existing metadata with taxes",
			item: &models.ParcelItem{
				Metadata: `{"taxes":{"OTHER":{"number":"ABC123"}}}`,
			},
			shipper: &models.Shipper{
				TaxNumbers: null.StringFrom(`{"gst": "GST123456"}`),
			},
			toCountryCode: "SG",
			want: &models.ParcelItem{
				Metadata: `{"taxes":{"gst":{"number":"GST123456"},"OTHER":{"number":"ABC123"}},"gst_registration_number":"GST123456"}`,
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			fillTaxesToParcelItem(tt.item, tt.shipper, tt.toCountryCode)

			expectedMD := &repo_interface.ParcelItemMetadata{}
			_ = json.Unmarshal([]byte(tt.want.Metadata), expectedMD)

			actualMD := &repo_interface.ParcelItemMetadata{}
			_ = json.Unmarshal([]byte(tt.item.Metadata), actualMD)

			assert.Equal(t, expectedMD.Taxes, actualMD.Taxes)
		})
	}
}

func Test_populateParcelWithBagInfo(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		bag            *models.Parcel
		parcel         *models.Parcel
		expectedParcel *models.Parcel
	}{
		{
			name: "regular parcel - non B2BBundle",
			bag: &models.Parcel{
				ID:             1,
				SourceOrderID:  null.StringFrom("SO123"),
				ServiceID:      null.UintFrom(100),
				ProductID:      null.UintFrom(200),
				OriginVendorID: 300,
				PartnerID:      null.Uint64From(400),
				ShipperID:      null.UintFrom(500),
				Type:           parcel.Parcel,
			},
			parcel: &models.Parcel{
				Metadata: null.StringFrom(`{"some":"data"}`),
			},
			expectedParcel: &models.Parcel{
				ParentID:       null.UintFrom(1),
				SourceOrderID:  null.StringFrom("SO123"),
				ServiceID:      null.UintFrom(100),
				ProductID:      null.UintFrom(200),
				OriginVendorID: 300,
				PartnerID:      null.Uint64From(400),
				ShipperID:      null.UintFrom(500),
				Metadata:       null.StringFrom(`{"some":"data"}`),
			},
		},
		{
			name: "B2BBundle parcel",
			bag: &models.Parcel{
				ID:             1,
				SourceOrderID:  null.StringFrom("SO123"),
				ServiceID:      null.UintFrom(100),
				ProductID:      null.UintFrom(200),
				OriginVendorID: 300,
				PartnerID:      null.Uint64From(400),
				ShipperID:      null.UintFrom(500),
				Type:           parcel.B2BBundle,
				Metadata:       null.StringFrom(`{"b2b_bundle":{"piece_tracking_ids":["123"],"requested_piece_tracking_ids":["456"]},"shipper_submitted_dimensions":{"length":10},"quantity":5,"status":"CREATED"}`),
				PickupInfo:     null.StringFrom(`{"pickup":"info"}`),
				DeliveryInfo:   null.StringFrom(`{"delivery":"info"}`),
			},
			parcel: &models.Parcel{
				Metadata: null.StringFrom(`{"some":"data"}`),
			},
			expectedParcel: &models.Parcel{
				ParentID:       null.UintFrom(1),
				SourceOrderID:  null.StringFrom("SO123"),
				ServiceID:      null.UintFrom(100),
				ProductID:      null.UintFrom(200),
				OriginVendorID: 300,
				PartnerID:      null.Uint64From(400),
				ShipperID:      null.UintFrom(500),
				Metadata:       null.StringFrom(`{"some":"data","b2b_bundle":{"piece_tracking_ids":null,"requested_piece_tracking_ids":null},"shipper_submitted_dimensions":{"length":10},"quantity":5,"status":"CREATED"}`),
				PickupInfo:     null.StringFrom(`{"pickup":"info"}`),
				DeliveryInfo:   null.StringFrom(`{"delivery":"info"}`),
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			populateParcelWithBagInfo(tt.bag, tt.parcel)
			expectedMD := models.ParcelDetails{}
			_ = json.Unmarshal([]byte(tt.expectedParcel.Metadata.String), &expectedMD)

			actualMD := models.ParcelDetails{}
			_ = json.Unmarshal([]byte(tt.parcel.Metadata.String), &actualMD)

			assert.Equal(t, expectedMD, actualMD)
			tt.expectedParcel.Metadata = null.StringFrom("")
			tt.parcel.Metadata = null.StringFrom("")
			assert.Equal(t, tt.expectedParcel, tt.parcel)
		})
	}
}

func Test_validatePickupRequest(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name     string
		req      *order.BaseRequest
		prepared *order_creation_interface.OrderCreationConfig
		wantErr  bool
		errMsg   string
	}{
		{
			name: "nil pickup - should pass",
			req: &order.BaseRequest{
				From: order.Address{CountryCode: "SG"},
				To:   order.Address{CountryCode: "MY"},
			},
			prepared: &order_creation_interface.OrderCreationConfig{
				Service: &models.Service{},
			},
			wantErr: false,
		},
		{
			name: "MMCC B2C service - should pass",
			req: &order.BaseRequest{
				Pickup: &order.Pickup{},
			},
			prepared: &order_creation_interface.OrderCreationConfig{
				Service: &models.Service{
					Type: serviceCfg.TypeMMCCB2C.NullUint8(),
				},
			},
			wantErr: false,
		},
		{
			name: "MMCC B2B service - should pass",
			req: &order.BaseRequest{
				Pickup: &order.Pickup{},
			},
			prepared: &order_creation_interface.OrderCreationConfig{
				Service: &models.Service{
					Type: serviceCfg.TypeMMCCB2B.NullUint8(),
				},
			},
			wantErr: false,
		},
		{
			name: "unsupported flow - should fail",
			req: &order.BaseRequest{
				Pickup: &order.Pickup{},
			},
			prepared: &order_creation_interface.OrderCreationConfig{
				Service:   &models.Service{},
				OrderFlow: "unsupported_flow",
			},
			wantErr: true,
			errMsg:  "Pickup request is not supported for this order",
		},
		{
			name: "domestic flow - different countries - should fail",
			req: &order.BaseRequest{
				Pickup: &order.Pickup{
					Address: &order.Address{CountryCode: "SG"},
				},
				To: order.Address{CountryCode: "MY"},
			},
			prepared: &order_creation_interface.OrderCreationConfig{
				Service:   &models.Service{},
				OrderFlow: orderConst.FlowDomestic,
			},
			wantErr: true,
			errMsg:  "Destination country must be same as pick up country when service type is domestic",
		},
		{
			name: "domestic flow - same countries - should pass",
			req: &order.BaseRequest{
				Pickup: &order.Pickup{
					Address: &order.Address{CountryCode: "SG"},
				},
				To: order.Address{CountryCode: "SG"},
			},
			prepared: &order_creation_interface.OrderCreationConfig{
				Service:   &models.Service{},
				OrderFlow: orderConst.FlowDomestic,
			},
			wantErr: false,
		},
		{
			name: "end to end flow - different origin countries - should fail",
			req: &order.BaseRequest{
				Pickup: &order.Pickup{
					Address: &order.Address{CountryCode: "SG"},
				},
				From: order.Address{CountryCode: "MY"},
			},
			prepared: &order_creation_interface.OrderCreationConfig{
				Service:   &models.Service{},
				OrderFlow: orderConst.FlowEndToEnd,
			},
			wantErr: true,
			errMsg:  "Origin country must be same as pick up country when service type is not domestic",
		},
		{
			name: "end to end flow - same origin countries - should pass",
			req: &order.BaseRequest{
				Pickup: &order.Pickup{
					Address: &order.Address{CountryCode: "SG"},
				},
				From: order.Address{CountryCode: "SG"},
			},
			prepared: &order_creation_interface.OrderCreationConfig{
				Service:   &models.Service{},
				OrderFlow: orderConst.FlowEndToEnd,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			err := validatePickupRequest(tt.req, tt.prepared)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func Test_customSheinRequest(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		input    *order.BaseRequest
		expected *order.BaseRequest
	}{
		{
			name: "SG address should get SG return address",
			input: &order.BaseRequest{
				To: order.Address{
					CountryCode: "SG",
				},
			},
			expected: &order.BaseRequest{
				To: order.Address{
					CountryCode: "SG",
				},
				Return: &SGAddress,
			},
		},
		{
			name: "MY West address should get West return address",
			input: &order.BaseRequest{
				To: order.Address{
					CountryCode:   "MY",
					StateProvince: null.StringFrom("Selangor").Ptr(),
				},
			},
			expected: &order.BaseRequest{
				To: order.Address{
					CountryCode:   "MY",
					StateProvince: null.StringFrom("Selangor").Ptr(),
				},
				Return: CustomSheinMYReturnAddress["WEST"],
			},
		},
		{
			name: "should truncate item descriptions longer than 255 characters",
			input: &order.BaseRequest{
				Items: []*order.ParcelItem{
					{
						Description: func() *string {
							s := string(make([]byte, 300))
							for i := 0; i < 300; i++ {
								s = s[:i] + "a" + s[i+1:]
							}
							return &s
						}(),
					},
				},
			},
			expected: &order.BaseRequest{
				Items: []*order.ParcelItem{
					{
						Description: func() *string {
							s := string(make([]byte, 255))
							for i := 0; i < 255; i++ {
								s = s[:i] + "a" + s[i+1:]
							}
							return &s
						}(),
					},
				},
			},
		},
		{
			name: "should truncate parcel customs description longer than 255 characters",
			input: &order.BaseRequest{
				ParcelDetails: &models.ParcelDetails{
					CustomsDescription: func() *string {
						s := string(make([]byte, 300))
						for i := 0; i < 300; i++ {
							s = s[:i] + "b" + s[i+1:]
						}
						return &s
					}(),
				},
			},
			expected: &order.BaseRequest{
				ParcelDetails: &models.ParcelDetails{
					CustomsDescription: func() *string {
						s := string(make([]byte, 255))
						for i := 0; i < 255; i++ {
							s = s[:i] + "b" + s[i+1:]
						}
						return &s
					}(),
				},
			},
		},
		{
			name: "should handle nil descriptions",
			input: &order.BaseRequest{
				Items: []*order.ParcelItem{
					{
						Description: nil,
					},
				},
				ParcelDetails: &models.ParcelDetails{
					CustomsDescription: nil,
				},
			},
			expected: &order.BaseRequest{
				Items: []*order.ParcelItem{
					{
						Description: nil,
					},
				},
				ParcelDetails: &models.ParcelDetails{
					CustomsDescription: nil,
				},
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			customSheinRequest(tt.input)
			assert.Equal(t, tt.expected, tt.input)
		})
	}
}

func TestOrderCreator_CreateMMCCOrder(t *testing.T) {
	data := prepareMMCCSetupData()
	tests := map[string]struct {
		setup    func(t *testing.T) (*orderCreator, *order_creation_interface.MMCCOCPayload, *models.Partner)
		validate func(t *testing.T, bag *models.Parcel, err error)
	}{
		"success": {
			setup: func(t *testing.T) (*orderCreator, *order_creation_interface.MMCCOCPayload, *models.Partner) {
				ctrl := gomock.NewController(t)

				bagRequest := &order.BaseRequest{
					RequestedTrackingID: "BAG123",
					SourceOrderID:       lo.ToPtr("SRC123"),
				}

				mmccParcels := []*order_creation_interface.MMCCParcel{
					{
						Parcel: &models.Parcel{
							TrackingID: "PARCEL123",
							ShipperID:  null.UintFrom(1),
						},
						ParcelItems: models.ParcelItemSlice{
							{
								ID: 1,
							},
						},
					},
				}

				creator, creatorM, closeFn := setupOrderCreator(ctrl, t, data)
				defer closeFn()

				creatorM.parcelRepo.EXPECT().
					BulkCreateIgnoreTxn(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil)

				creatorM.parcelRepo.EXPECT().
					GetListWithCtxTxn(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(models.ParcelSlice{mmccParcels[0].Parcel}, nil)

				creatorM.shipperRepo.EXPECT().
					GetOne(gomock.Any(), gomock.Any()).
					Return(data.shippers.DefaultShipper, nil)

				creatorM.parcelItemRepo.EXPECT().
					BulkCreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil)

				payload := &order_creation_interface.MMCCOCPayload{
					BagRequest:  bagRequest,
					MMCCParcels: mmccParcels,
				}

				return creator, payload, data.partner
			},
			validate: func(t *testing.T, bag *models.Parcel, err error) {
				assert.NoError(t, err)
				assert.NotNil(t, bag)
			},
		},
		"bulk create parcels error": {
			setup: func(t *testing.T) (*orderCreator, *order_creation_interface.MMCCOCPayload, *models.Partner) {
				ctrl := gomock.NewController(t)

				bagRequest := &order.BaseRequest{
					RequestedTrackingID: "BAG123",
					SourceOrderID:       lo.ToPtr("SRC123"),
				}

				mmccParcels := []*order_creation_interface.MMCCParcel{
					{
						Parcel: &models.Parcel{
							TrackingID: "PARCEL123",
							ShipperID:  null.UintFrom(1),
						},
						ParcelItems: models.ParcelItemSlice{
							{
								ID: 1,
							},
						},
					},
				}

				creator, creatorM, closeFn := setupOrderCreator(ctrl, t, data)
				defer closeFn()

				creatorM.parcelRepo.EXPECT().
					BulkCreateIgnoreTxn(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(errors.New("bulk create error"))

				payload := &order_creation_interface.MMCCOCPayload{
					BagRequest:  bagRequest,
					MMCCParcels: mmccParcels,
				}

				return creator, payload, data.partner
			},
			validate: func(t *testing.T, bag *models.Parcel, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "bulk create parcels error")
			},
		},
		"failed to get parcels": {
			setup: func(t *testing.T) (*orderCreator, *order_creation_interface.MMCCOCPayload, *models.Partner) {
				ctrl := gomock.NewController(t)

				bagRequest := &order.BaseRequest{
					RequestedTrackingID: "BAG123",
					SourceOrderID:       lo.ToPtr("SRC123"),
				}

				mmccParcels := []*order_creation_interface.MMCCParcel{
					{
						Parcel: &models.Parcel{
							TrackingID: "PARCEL123",
							ShipperID:  null.UintFrom(1),
						},
						ParcelItems: models.ParcelItemSlice{
							{
								ID: 1,
							},
						},
					},
				}

				creator, creatorM, closeFn := setupOrderCreator(ctrl, t, data)
				defer closeFn()

				creatorM.parcelRepo.EXPECT().
					BulkCreateIgnoreTxn(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil)

				creatorM.parcelRepo.EXPECT().
					GetListWithCtxTxn(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil, errors.New("get parcels error"))

				payload := &order_creation_interface.MMCCOCPayload{
					BagRequest:  bagRequest,
					MMCCParcels: mmccParcels,
				}

				return creator, payload, data.partner
			},
			validate: func(t *testing.T, bag *models.Parcel, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "get parcels error")
			},
		},
		"failed to get shipper": {
			setup: func(t *testing.T) (*orderCreator, *order_creation_interface.MMCCOCPayload, *models.Partner) {
				ctrl := gomock.NewController(t)

				bagRequest := &order.BaseRequest{
					RequestedTrackingID: "BAG123",
					SourceOrderID:       lo.ToPtr("SRC123"),
				}

				mmccParcels := []*order_creation_interface.MMCCParcel{
					{
						Parcel: &models.Parcel{
							TrackingID: "PARCEL123",
							ShipperID:  null.UintFrom(1),
						},
						ParcelItems: models.ParcelItemSlice{
							{
								ID: 1,
							},
						},
					},
				}

				creator, creatorM, closeFn := setupOrderCreator(ctrl, t, data)
				defer closeFn()

				creatorM.parcelRepo.EXPECT().
					BulkCreateIgnoreTxn(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil)

				creatorM.parcelRepo.EXPECT().
					GetListWithCtxTxn(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(models.ParcelSlice{mmccParcels[0].Parcel}, nil)

				creatorM.shipperRepo.EXPECT().
					GetOne(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("get shipper error"))

				payload := &order_creation_interface.MMCCOCPayload{
					BagRequest:  bagRequest,
					MMCCParcels: mmccParcels,
				}

				return creator, payload, data.partner
			},
			validate: func(t *testing.T, bag *models.Parcel, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "get shipper error")
			},
		},
		"failed to bulk create parcel items": {
			setup: func(t *testing.T) (*orderCreator, *order_creation_interface.MMCCOCPayload, *models.Partner) {
				ctrl := gomock.NewController(t)

				bagRequest := &order.BaseRequest{
					RequestedTrackingID: "BAG123",
					SourceOrderID:       lo.ToPtr("SRC123"),
				}

				mmccParcels := []*order_creation_interface.MMCCParcel{
					{
						Parcel: &models.Parcel{
							TrackingID: "PARCEL123",
							ShipperID:  null.UintFrom(1),
						},
						ParcelItems: models.ParcelItemSlice{
							{
								ID: 1,
							},
						},
					},
				}

				creator, creatorM, closeFn := setupOrderCreator(ctrl, t, data)
				defer closeFn()

				creatorM.parcelRepo.EXPECT().
					BulkCreateIgnoreTxn(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil)

				creatorM.parcelRepo.EXPECT().
					GetListWithCtxTxn(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					Return(models.ParcelSlice{mmccParcels[0].Parcel}, nil)

				creatorM.shipperRepo.EXPECT().
					GetOne(gomock.Any(), gomock.Any()).
					Return(data.shippers.DefaultShipper, nil)

				creatorM.parcelItemRepo.EXPECT().
					BulkCreateWithTxn(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(errors.New("bulk create parcel items error"))

				payload := &order_creation_interface.MMCCOCPayload{
					BagRequest:  bagRequest,
					MMCCParcels: mmccParcels,
				}

				return creator, payload, data.partner
			},
			validate: func(t *testing.T, bag *models.Parcel, err error) {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "bulk create parcel items error")
			},
		},
	}

	for name, tc := range tests {
		tc := tc
		t.Run(name, func(t *testing.T) {
			t.Parallel()
			creator, payload, partner := tc.setup(t)
			mockTxn := repoMocks.NewMockContextTransactor(gomock.NewController(t))
			bag, err := creator.CreateMMCCOrder(context.Background(), payload, partner, mockTxn)
			tc.validate(t, bag, err)
		})
	}
}

func TestOrderCreator_CreateCoreB2BBundle(t *testing.T) {
	nvCoreRespes := map[string]*nvcore.OrderResponse{
		"sg": {
			TrackingNumber: "BUNDLE_123",
			BundleInformation: &nvcore.BundleInformationResponse{
				BundleInformation: &nvcore.BundleInformation{
					TotalQuantity:                 2,
					RequestedPieceTrackingNumbers: []string{"PIECE_1", "PIECE_2"},
				},
				Pieces: []nvcore.PieceResponse{
					{
						TrackingNumber: "PIECE_1",
					},
					{
						TrackingNumber: "PIECE_2",
					},
				},
			},
		}}
	data := prepareCoreB2BBundleSetupData(nvCoreRespes)
	tests := map[string]struct {
		req     *order.BaseRequest
		partner *models.Partner
		data    *creatorData

		expectedResult *order_creation_interface.CoreB2BBundleOCResult
		expectedErr    error
	}{
		"success": {
			req: &order.BaseRequest{
				RequestedTrackingID: "BUNDLE_123",
				SourceOrderID:       lo.ToPtr("SRC123"),
				ParcelDetails: &models.ParcelDetails{
					Quantity: lo.ToPtr(uint(2)),
					B2BBundle: &models.B2BBundleDetails{
						DocumentsRequired:         []string{"RDO", "GRN"},
						RequestedPieceTrackingIDs: []string{"PIECE_1", "PIECE_2"},
						HSCode:                    lo.ToPtr("1,2"),
					},
				},
				From: order.Address{
					AddressLine1: "AL1",
					CountryCode:  "SG",
				},
				To: order.Address{
					AddressLine1: "AL1",
					CountryCode:  "VN",
				},
			},
			partner: data.partner,
			data:    data,

			expectedResult: &order_creation_interface.CoreB2BBundleOCResult{
				TrackingID: "BUNDLE_123",
				PieceTrackingIDs: []string{
					"PIECE_1",
					"PIECE_2",
				},
			},
			expectedErr: nil,
		},
	}

	for name, tc := range tests {
		tc := tc
		t.Run(name, func(t *testing.T) {
			creator, creatorMock, closeFn := setupOrderCreator(gomock.NewController(t), t, data)
			defer closeFn()

			creatorMock.vendorRequestRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
			result, err := creator.CreateCoreB2BBundle(context.Background(), tc.req, tc.partner)
			assert.Equal(t, tc.expectedResult, result)
			assert.Equal(t, tc.expectedErr, err)
		})
	}
}

func TestOrderCreator_CreateE2EOrder(t *testing.T) {
	tests := map[string]struct {
		testData *creatorData
		req      *order.BaseRequest
		validate func(t *testing.T, parcel *models.Parcel, err error)
	}{
		"create E2E international order successfully": {
			testData: prepareE2ESetupData(map[string]*nvcore.OrderResponse{
				"sg": {
					ParcelJob: nvcore.ParcelJobResponse{
						ParcelJob: nvcore.ParcelJob{
							DeliveryStartDate: "2024-12-12",
						},
					},
					TrackingNumber: "ORDER_123",
				},
				"vn": {
					ParcelJob: nvcore.ParcelJobResponse{
						ParcelJob: nvcore.ParcelJob{
							DeliveryStartDate: "2024-12-12",
						},
					},
					TrackingNumber: "ORDER_123",
				},
			}),
			req: &order.BaseRequest{
				Source:              order.SourceRegularAPI,
				RequestedTrackingID: "ORDER_123",
				SourceOrderID:       lo.ToPtr("SRC123"),
				From: order.Address{
					Name:         "Name test",
					AddressLine1: "AL1",
					CountryCode:  "SG",
				},
				To: order.Address{
					Name:         "Name test",
					AddressLine1: "AL1",
					CountryCode:  "VN",
				},
			},
			validate: func(t *testing.T, parcel *models.Parcel, err error) {
				assert.NoError(t, err)
				assert.NotNil(t, parcel)
			},
		},
		"[unified OC] create E2E international order successfully": {
			testData: prepareE2ESetupData(map[string]*nvcore.OrderResponse{
				"sg": {
					ParcelJob: nvcore.ParcelJobResponse{
						ParcelJob: nvcore.ParcelJob{
							DeliveryStartDate: "2024-12-12",
						},
					},
					TrackingNumber: "ORDER_123",
				},
				"vn": {
					ParcelJob: nvcore.ParcelJobResponse{
						ParcelJob: nvcore.ParcelJob{
							DeliveryStartDate: "2024-12-12",
						},
					},
					TrackingNumber: "ORDER_123",
				},
			}),
			req: &order.BaseRequest{
				Source:              order.SourceCoreConsumerV2,
				RequestedTrackingID: "ORDER_123",
				SourceOrderID:       lo.ToPtr("SRC123"),
				From: order.Address{
					Name:         "Name test",
					AddressLine1: "AL1",
					CountryCode:  "SG",
				},
				To: order.Address{
					Name:         "Name test",
					AddressLine1: "AL1",
					CountryCode:  "VN",
				},
			},
			validate: func(t *testing.T, parcel *models.Parcel, err error) {
				assert.NoError(t, err)
				assert.NotNil(t, parcel)
			},
		},
		"create E2E corporate international order successfully": {
			testData: prepareE2ESetupData(map[string]*nvcore.OrderResponse{
				"sg": {
					ParcelJob: nvcore.ParcelJobResponse{
						ParcelJob: nvcore.ParcelJob{
							DeliveryStartDate: "2024-12-12",
						},
					},
					TrackingNumber: "ORDER_123",
				},
				"vn": {
					ParcelJob: nvcore.ParcelJobResponse{
						ParcelJob: nvcore.ParcelJob{
							DeliveryStartDate: "2024-12-12",
						},
					},
					TrackingNumber: "ORDER_123",
				},
			}),
			req: &order.BaseRequest{
				Type:                lo.ToPtr(parcel.CorporateInternational.String()),
				Source:              order.SourceRegularAPI,
				RequestedTrackingID: "ORDER_123",
				SourceOrderID:       lo.ToPtr("SRC123"),
				From: order.Address{
					Name:         "Name test",
					AddressLine1: "AL1",
					CountryCode:  "SG",
				},
				To: order.Address{
					Name:         "Name test",
					AddressLine1: "AL1",
					CountryCode:  "VN",
				},
			},
			validate: func(t *testing.T, parcel *models.Parcel, err error) {
				assert.NoError(t, err)
				assert.NotNil(t, parcel)
			},
		},
		"create E2E international order return successfully": {
			testData: prepareE2ESetupData(map[string]*nvcore.OrderResponse{
				"sg": {
					ParcelJob: nvcore.ParcelJobResponse{
						ParcelJob: nvcore.ParcelJob{
							DeliveryStartDate: "2024-12-12",
						},
					},
					TrackingNumber: "ORDER_123",
				},
				"vn": {
					ParcelJob: nvcore.ParcelJobResponse{
						ParcelJob: nvcore.ParcelJob{
							DeliveryStartDate: "2024-12-12",
						},
					},
					TrackingNumber: "ORDER_123",
				},
			}),
			req: &order.BaseRequest{
				Type:                lo.ToPtr(parcel.InternationalReturn.String()),
				Source:              order.SourceRegularAPI,
				RequestedTrackingID: "ORDER_123",
				SourceOrderID:       lo.ToPtr("SRC123"),
				RefTrackingID:       lo.ToPtr("REF123"),
				From: order.Address{
					Name:         "Name test",
					AddressLine1: "AL1",
					CountryCode:  "SG",
				},
				To: order.Address{
					Name:         "Name test",
					AddressLine1: "AL1",
					CountryCode:  "VN",
				},
			},
			validate: func(t *testing.T, parcel *models.Parcel, err error) {
				assert.NoError(t, err)
				assert.NotNil(t, parcel)
			},
		},
	}

	for name, tc := range tests {
		tc := tc
		t.Run(name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			creator, _, closeFn := setupOrderCreator(ctrl, t, tc.testData)
			defer closeFn()
			p, err := creator.CreateE2EOrder(context.Background(), tc.req, tc.testData.partner)
			tc.validate(t, p, err)
		})
	}
}
