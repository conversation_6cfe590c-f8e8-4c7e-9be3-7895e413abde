//go:build integration
// +build integration

package it_test

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"testing"

	lop "github.com/samber/lo/parallel"
	"github.com/volatiletech/null/v8"

	"git.ninjavan.co/3pl/configs"
	parcelConst "git.ninjavan.co/3pl/configs/parcel"
	"git.ninjavan.co/3pl/envs"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/testutils"
)

func (s *ITTiktokOrderCreator) TestITTiktokOrderCreator_CreateMMCCBag() {
	const (
		bigBagID = "BAGTIKTOK123456789"
	)

	request := &order.TiktokMMCCPreAlert{
		BigBagNo: bigBagID,
		SortCode: "CNMY-A-S-BKI-AF-2",
		PickupInfo: order.TiktokPickupInfo{
			PickupFirstName: "John",
			PickupLastName:  "Doe",
			PickupPhone:     "**********",
			PickupZip:       "510801",
			Address: order.TiktokPickupAddress{
				AddressL0:   "CN",
				AddressL1:   "广东省",
				AddressL2:   "广州市",
				AddressL3:   "空港花都",
				AddressL4:   "P栋311",
				Details:     "金港北三路6号",
				DetailsMore: "龙地广州空港物流园",
			},
		},
		BigBagLength:        "1200",
		BigBagWidth:         "1400",
		BigBagHeight:        "100",
		BigBagWeight:        "500000",
		BigBagWeightUnit:    "kg",
		BigBagDimensionUnit: "cm",
		PackageQuantity:     2,
	}

	verify := func(parcel *models.Parcel) error {
		if !testutils.CompareNonEmptyFields(parcel, &models.Parcel{
			ServiceID:     null.UintFrom(81904),
			ProductID:     null.UintFrom(141019),
			ShipperID:     null.UintFrom(900501),
			PartnerID:     null.Uint64From(envs.Instance.Tiktok.PartnerId),
			SourceOrderID: null.StringFrom(bigBagID),
			RefTrackingID: null.StringFrom(bigBagID),
			Type:          parcelConst.BagB2CV2,
		}) {
			return fmt.Errorf("Expected bag to be created with source order ID %s, but got %+v", bigBagID, parcel)
		}

		var metadata repo_interface.ParcelMetadata
		if err := json.Unmarshal([]byte(parcel.Metadata.String), &metadata); err != nil {
			return fmt.Errorf("failed to unmarshal parcel metadata: %w", err)
		}

		// Verify parcel details in metadata
		if metadata.Quantity == nil || *metadata.Quantity != uint(2) {
			return fmt.Errorf("Expected 2 parcels inside created bag, but got %d", metadata.Quantity)
		}

		if metadata.ShipperSubmittedWeight == nil || *metadata.ShipperSubmittedWeight != 500000 {
			return fmt.Errorf("Expected shipper submitted weight to be 500000, but got %v", metadata.ShipperSubmittedWeight)
		}

		if metadata.ShipperSubmittedWeightUnit == nil || *metadata.ShipperSubmittedWeightUnit != "kg" {
			return fmt.Errorf("Expected shipper submitted weight unit to be kg, but got %v", metadata.ShipperSubmittedWeightUnit)
		}

		if metadata.ShipperSubmittedDimensions == nil {
			return fmt.Errorf("Expected shipper submitted dimensions to be set")
		}

		dimensions := metadata.ShipperSubmittedDimensions
		if dimensions.Length == nil || *dimensions.Length != 1200 {
			return fmt.Errorf("Expected length to be 1200, but got %v", dimensions.Length)
		}
		if dimensions.Width == nil || *dimensions.Width != 1400 {
			return fmt.Errorf("Expected width to be 1400, but got %v", dimensions.Width)
		}
		if dimensions.Height == nil || *dimensions.Height != 100 {
			return fmt.Errorf("Expected height to be 100, but got %v", dimensions.Height)
		}
		if dimensions.Unit == nil || *dimensions.Unit != "cm" {
			return fmt.Errorf("Expected dimension unit to be cm, but got %v", dimensions.Unit)
		}

		return nil
	}

	partner, err := repositories.NewPartnerRepository().
		GetOne(context.TODO(), models.PartnerWhere.ID.EQ(envs.Instance.Tiktok.PartnerId))
	if err != nil {
		s.T().Error(err)
		return
	}

	s.T().Run("Create mmcc bag successfully", func(t *testing.T) {
		_, err := s.usecase.CreateMMCCBag(context.Background(), partner, request)
		if err != nil {
			t.Error(err)
			return
		}

		bag, err := repositories.NewParcelRepository().GetOne(context.Background(),
			models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
			models.ParcelWhere.Type.EQ(parcelConst.BagB2CV2),
		)
		if err != nil {
			t.Error(err)
			return
		}

		if err := verify(bag); err != nil {
			t.Error(err)
		}
	})
}

func (s *ITTiktokOrderCreator) TestITTiktokOrderCreator_CreateAsyncMMCCParcels() {
	const (
		bigBagID = "BAGTIKTOK123456789"
	)

	// First create a bag to contain the parcels
	bagRequest := &order.TiktokMMCCPreAlert{
		BigBagNo: bigBagID,
		SortCode: "CNMY-A-S-BKI-AF-2",
		PickupInfo: order.TiktokPickupInfo{
			PickupFirstName: "John",
			PickupLastName:  "Doe",
			PickupPhone:     "**********",
			PickupZip:       "510801",
			Address: order.TiktokPickupAddress{
				AddressL0:   "CN",
				AddressL1:   "广东省",
				AddressL2:   "广州市",
				AddressL3:   "空港花都",
				AddressL4:   "P栋311",
				Details:     "金港北三路6号",
				DetailsMore: "龙地广州空港物流园",
			},
		},
		BigBagLength:        "1200",
		BigBagWidth:         "1400",
		BigBagHeight:        "100",
		BigBagWeight:        "500000",
		BigBagWeightUnit:    "kg",
		BigBagDimensionUnit: "cm",
		PackageQuantity:     2,
	}

	// Create parcels request
	parcelRequest := &order.TiktokMMCCCreateRequest{
		PackageList: []*order.TiktokMMCCPackage{
			{
				BigBagNo:        bigBagID,
				TrackingNo:      "PARCEL123456789",
				ProviderOrderID: "ORDER123456789",
				Remark:          "Test parcel",
				Package: &order.TiktokPackageDimension{
					Weight: null.Uint64From(1000).Ptr(),
					Length: null.Uint64From(30).Ptr(),
					Width:  null.Uint64From(20).Ptr(),
					Height: null.Uint64From(10).Ptr(),
				},
				Value: &order.TiktokValue{
					TotalGoodsValue: "100.00",
					Currency:        "USD",
					IsCOD:           null.Int64From(1).Ptr(),
					CODValue:        "10.00",
					GoodsValue:      "90.00",
					IsInsurance:     null.Int64From(0).Ptr(),
					InsuranceValue:  "0.00",
				},
				SenderInfo: &order.TiktokMMCCSenderInfo{
					FirstName: "John",
					LastName:  "Doe",
					Phone:     "**********",
					Postcode:  "510801",
					Address: &order.TiktokMMCCSenderInfoAddress{
						AddressL0:   "CN",
						AddressL1:   "广东省",
						AddressL2:   "广州市",
						AddressL3:   "空港花都",
						AddressL4:   "P栋311",
						Details:     "金港北三路6号",
						DetailsMore: "龙地广州空港物流园",
					},
				},
				ShippingInfo: &order.TiktokMMCCShippingInfo{
					FirstName: "Jane",
					LastName:  "Smith",
					Phone:     "0987654321",
					Postcode:  "40300",
					Address: &order.TiktokMMCCShippingInfoAddress{
						AddressL0:   "MY",
						AddressL1:   "Selangor",
						AddressL2:   "Shah Alam",
						AddressL3:   "Seksyen 22",
						AddressL4:   "Lot 1-8",
						Details:     "Persiaran Jubli Perak",
						DetailsMore: "Seksyen 22",
					},
				},
				Items: []*order.TiktokItem{
					{
						ProductName:   "Test Product",
						ProductNameCN: "测试产品",
						Qty:           1,
						UnitPrice:     "100.00",
						UnitCode:      "USD",
						UnitWeight:    1000,
						ProductURL:    "https://test.com/product",
						SKUId:         "SKU123",
						Category:      "Electronics",
						Spec:          "Test Spec",
					},
				},
			},
		},
	}

	verify := func(bag *models.Parcel, trackingNos []string) error {
		// Verify bag exists
		if bag == nil {
			return fmt.Errorf("Expected bag to be created")
		}

		// Verify async tasks were created
		identityKeyStr := strconv.Itoa(int(bag.ID))
		for _, trackingNo := range trackingNos {
			asyncTask, err := repositories.NewAsyncTask().GetTask(context.Background(),
				uint8(configs.AsyncTaskTypeCreateMMCCParcel),
				trackingNo)
			if err != nil {
				return fmt.Errorf("failed to get async task by tracking_no: %s, but got %w", trackingNo, err)
			}
			if asyncTask.TaskData == null.StringFrom("") {
				return fmt.Errorf("Expected async task to be created with identity key %s, but got empty", identityKeyStr)
			}
		}
		return nil
	}

	partner, err := repositories.NewPartnerRepository().
		GetOne(context.TODO(), models.PartnerWhere.ID.EQ(envs.Instance.Tiktok.PartnerId))
	if err != nil {
		s.T().Error(err)
		return
	}

	s.T().Run("Create async MMCC parcels successfully", func(t *testing.T) {
		// First create the bag
		bag, err := s.usecase.CreateMMCCBag(context.Background(), partner, bagRequest)
		if err != nil {
			t.Error(err)
			return
		}

		// Then create the parcels
		_, err = s.usecase.CreateAsyncMMCCParcels(context.Background(), *parcelRequest)
		if err != nil {
			t.Error(err)
			return
		}

		trackingNos := lop.Map(parcelRequest.PackageList, func(pkg *order.TiktokMMCCPackage, index int) string {
			return pkg.TrackingNo
		})
		if err := verify(bag, trackingNos); err != nil {
			t.Error(err)
		}
	})
}
