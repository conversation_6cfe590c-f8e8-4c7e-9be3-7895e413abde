//go:build integration
// +build integration

package it_test

import (
	"os"
	"testing"

	"github.com/stretchr/testify/suite"

	"git.ninjavan.co/3pl/handlers/order_creation"
	"git.ninjavan.co/3pl/handlers/order_creation/order_creation_interface"
	"git.ninjavan.co/3pl/testutils"
)

type ITOrderCreator struct {
	suite.Suite
	usecase order_creation_interface.OrderCreator
}

func TestITITOrderCreator(t *testing.T) {
	suite.Run(t, new(ITOrderCreator))
}

func (s *ITOrderCreator) SetupSuite() {
	s.usecase = order_creation.NewOrderCreator()
}

type ITB2BBundleCreator struct {
	suite.Suite
	usecase order_creation_interface.B2BBundleCreator
}

func TestITB2BBundleCreator(t *testing.T) {
	suite.Run(t, new(ITB2BBundleCreator))
}

func (s *ITB2BBundleCreator) SetupSuite() {
	s.usecase = order_creation.NewB2bBundleCreator()
}

type ITLazadaMMOrderCreator struct {
	suite.Suite
	creator order_creation.LazadaMMOrderCreator
}

func TestITLazadaMMOrderCreator(t *testing.T) {
	suite.Run(t, new(ITLazadaMMOrderCreator))
}

func (s *ITLazadaMMOrderCreator) SetupSuite() {
	s.creator = order_creation.NewLazadaMMOrderCreator()
}

type ITTiktokOrderCreator struct {
	suite.Suite
	usecase order_creation_interface.TiktokCreator
}

func TestITTiktokCreator(t *testing.T) {
	suite.Run(t, new(ITTiktokOrderCreator))
}

func (s *ITTiktokOrderCreator) SetupSuite() {
	s.usecase = order_creation.NewTiktokCreator()
}

func TestMain(m *testing.M) {
	suite := testutils.BaseIntegration{}
	suite.SetupSuite()
	code := m.Run()
	suite.TearDownSuite() // if we don't terminate containers then we can reuse them again for faster executing time
	os.Exit(code)
}
