//go:build integration
// +build integration

package it_test

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"

	parcelConst "git.ninjavan.co/3pl/configs/parcel"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/testutils"
)

func (s *ITLazadaMMOrderCreator) TestITLazadaMMOrderCreator_CreateMMCCBag() {
	const (
		partnerID                    = 88426
		bigBagIDCreatedBeforeParcels = "BAGMM177091771273"
		bigBagIDCreatedAfterParcels  = "BAGMM177091771274"
	)

	type testCase struct {
		name    string
		request *order.LazadaMMCreateBagRequest
		verify  func(*models.Parcel) error
	}

	tests := []testCase{
		{
			name: "Create mmcc bag with 2 parcels before creating parcels inside",
			request: &order.LazadaMMCreateBagRequest{
				BigBagID:    bigBagIDCreatedBeforeParcels,
				LaneCode:    "SZXABKI-FLASH-STD-02",
				GrossWeight: "500000",
				LabelWeight: "500000",
				WeightUnit:  "",
				ParcelQty:   "2",
				ParcelList: []*order.LazadaMMParcelID{
					{
						LogisticsOrderCode: "SHIPPERREF123451",
						TrackingNumber:     "PARCEL1234567891",
					},
					{
						LogisticsOrderCode: "SHIPPERREF123452",
						TrackingNumber:     "PARCEL1234567892",
					},
				},
				Sender: order.LazadaMMSender{
					UserID: "USER123123",
				},
				BigbagMaterial: order.LazadaMMBigbagMaterial{},
				Length:         "1200",
				Width:          "1400",
				Height:         "100",
			},
			verify: func(parcel *models.Parcel) error {
				if !testutils.CompareNonEmptyFields(parcel, &models.Parcel{
					ServiceID:     null.UintFrom(81904),
					ProductID:     null.UintFrom(141019),
					ShipperID:     null.UintFrom(7012943),
					PartnerID:     null.Uint64From(partnerID),
					SourceOrderID: null.StringFrom(bigBagIDCreatedBeforeParcels),
					RefTrackingID: null.StringFrom(bigBagIDCreatedBeforeParcels),
					Type:          parcelConst.BagB2CV2,
				}) {
					return fmt.Errorf("Expected parcel to be created with source order ID %s, but got %+v", bigBagIDCreatedBeforeParcels, parcel)
				}

				var metadata order.ExternalParcelDetails
				_ = json.Unmarshal([]byte(parcel.Metadata.String), &metadata)
				if metadata.Quantity == nil || *metadata.Quantity != uint(2) {
					return fmt.Errorf("Expected 2 parcels inside created bag, but got %d", len(metadata.B2BBundle.PieceTrackingIDs))
				}
				return nil
			},
		},
		{
			name: "Create bag after creating parcels inside",
			request: &order.LazadaMMCreateBagRequest{
				BigBagID:    bigBagIDCreatedAfterParcels,
				LaneCode:    "SZXAKCH-FLASH-STD-02",
				GrossWeight: "750000",
				LabelWeight: "750000",
				WeightUnit:  "",
				ParcelQty:   "1",
				ParcelList: []*order.LazadaMMParcelID{
					{
						LogisticsOrderCode: "PLID-SG88B2",
						TrackingNumber:     "PTID-5FNGN5",
					},
				},
				Sender: order.LazadaMMSender{
					UserID: "USER123123",
				},
				BigbagMaterial: order.LazadaMMBigbagMaterial{},
				Length:         "1200",
				Width:          "1400",
				Height:         "100",
			},
			verify: func(parcel *models.Parcel) error {
				if !testutils.CompareNonEmptyFields(parcel, &models.Parcel{
					ServiceID:     null.UintFrom(81905),
					ProductID:     null.UintFrom(141019),
					ShipperID:     null.UintFrom(7012943),
					PartnerID:     null.Uint64From(partnerID),
					SourceOrderID: null.StringFrom(bigBagIDCreatedAfterParcels),
					RefTrackingID: null.StringFrom(bigBagIDCreatedAfterParcels),
					Type:          parcelConst.BagB2CV2,
				}) {
					return fmt.Errorf("Expected parcel to be created with source order ID %s, but got %+v", bigBagIDCreatedAfterParcels, parcel)
				}

				var metadata order.ExternalParcelDetails
				_ = json.Unmarshal([]byte(parcel.Metadata.String), &metadata)
				if metadata.Quantity == nil || *metadata.Quantity != uint(1) {
					return fmt.Errorf("Expected 1 parcel inside created bag, but got %d", metadata.Quantity)
				}

				mmccParcels, err := repositories.NewParcelRepository().GetMMCCParcelsByRefTIDsAndPartnerID(context.Background(), []string{"PLID-SG88B2"}, partnerID)
				if err != nil {
					s.T().Error(err)
					return nil
				}

				// verify parcels inside bag
				if len(mmccParcels) != 1 {
					return fmt.Errorf("Expected 1 parcel inside bag, but got %d", len(mmccParcels))
				}

				// verify mmcc parcels are linked with bag
				if !testutils.CompareNonEmptyFields(mmccParcels[0], &models.Parcel{
					ServiceID:     null.UintFrom(81905),
					ProductID:     null.UintFrom(141019),
					ShipperID:     null.UintFrom(7012943),
					PartnerID:     null.Uint64From(partnerID),
					SourceOrderID: null.StringFrom(bigBagIDCreatedAfterParcels),
					RefTrackingID: null.StringFrom("PLID-SG88B2"),
					ParentID:      null.UintFrom(parcel.ID),
					Type:          parcelConst.MMCCParcel,
				}) {
					return fmt.Errorf("Expected parcel to be created with source order ID %s, but got %+v", bigBagIDCreatedAfterParcels, mmccParcels[0])
				}

				return nil
			},
		},
	}

	partner, err := repositories.NewPartnerRepository().
		GetOne(context.TODO(), models.PartnerWhere.ID.EQ(partnerID))
	if err != nil {
		s.T().Error(err)
		return
	}

	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			_, err := s.creator.CreateMMCCBag(context.Background(), partner, tt.request)
			if err != nil {
				t.Error(err)
				return
			}

			bag, err := repositories.NewParcelRepository().GetOne(context.Background(),
				models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(tt.request.BigBagID)),
				models.ParcelWhere.Type.EQ(parcelConst.BagB2CV2),
			)
			if err != nil {
				t.Error(err)
				return
			}

			if err := tt.verify(bag); err != nil {
				t.Error(err)
			}
		})
	}
}

func (s *ITLazadaMMOrderCreator) TestITLazadaMMOrderCreator_UpdateMMCCBag() {
	const (
		partnerID                      = 88426
		bigBagID                       = "SZXACGK41-02-BA2110231444-3603SB"
		bigBagIDParcelAssignedShipment = "SZXACGK41-02-BA2110231-45223SB"
	)

	type testCase struct {
		name    string
		request *order.LazadaMMUpdateRequest
		verify  func(mmccParcelTrackingIDs []string, bigBagID string) error
	}

	getParcelsByTrackingIDs := func(trackingIDs []string) ([]*models.Parcel, error) {
		return repositories.NewParcelRepository().GetListWithCtx(
			context.Background(),
			models.ParcelWhere.TrackingID.IN(trackingIDs),
		)
	}

	verifyParcelsDeleted := func(trackingIDs []string, bagID string) error {
		parcels, err := getParcelsByTrackingIDs(trackingIDs)
		if err != nil {
			return fmt.Errorf("failed to get parcels: %w", err)
		}
		if len(parcels) > 0 {
			return fmt.Errorf("expected all parcels inside bag %s to be deleted, but found %d parcels", bagID, len(parcels))
		}
		return nil
	}

	verifyParcelsUnlinked := func(trackingIDs []string, bagID string) error {
		parcels, err := getParcelsByTrackingIDs(trackingIDs)
		if err != nil {
			return fmt.Errorf("failed to get parcels: %w", err)
		}
		if len(parcels) == 0 {
			return fmt.Errorf("expected parcels to exist but found none")
		}

		for _, parcel := range parcels {
			if parcel.SourceOrderID.Valid || parcel.ParentID.Valid {
				return fmt.Errorf("expected parcel %s to be unlinked from bag %s, but found sourceOrderID=%v, parentID=%v",
					parcel.TrackingID, bagID, parcel.SourceOrderID, parcel.ParentID)
			}
		}
		return nil
	}

	tests := []testCase{
		{
			name: "Delete parcels from bag when parcels are not assigned to shipment",
			request: &order.LazadaMMUpdateRequest{
				BigBagID: bigBagID,
				ParcelList: []*order.LazadaMMParcelID{
					{
						LogisticsOrderCode: "LP00474638845204",
						TrackingNumber:     "NLIDXB1004373325",
					},
				},
				UpdateType: "DELETE",
			},
			verify: verifyParcelsDeleted,
		},
		{
			name: "Unlink parcels from bag when parcels are assigned to shipment",
			request: &order.LazadaMMUpdateRequest{
				BigBagID: bigBagIDParcelAssignedShipment,
				ParcelList: []*order.LazadaMMParcelID{
					{
						LogisticsOrderCode: "LP0047472371263LOM",
						TrackingNumber:     "NLSGXB1076MKL73325",
					},
				},
				UpdateType: "DELETE",
			},
			verify: verifyParcelsUnlinked,
		},
	}

	partner, err := repositories.NewPartnerRepository().
		GetOne(context.TODO(), models.PartnerWhere.ID.EQ(partnerID))
	if err != nil {
		s.T().Fatalf("failed to get partner: %v", err)
	}

	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			refTIDs := lo.Map(tt.request.ParcelList, func(p *order.LazadaMMParcelID, _ int) string {
				return p.LogisticsOrderCode
			})

			mmccParcels, err := repositories.NewParcelRepository().GetMMCCParcelsByRefTIDsAndPartnerID(
				context.Background(),
				refTIDs,
				partner.ID,
			)
			if err != nil {
				t.Fatalf("failed to get MMCC parcels: %v", err)
			}

			trackingIDs := lo.Map(mmccParcels, func(p *models.Parcel, _ int) string {
				return p.TrackingID
			})

			if err := s.creator.UpdateMMCCBag(context.Background(), partner, tt.request); err != nil {
				t.Fatalf("failed to update MMCC bag: %v", err)
			}

			if err := tt.verify(trackingIDs, tt.request.BigBagID); err != nil {
				t.Error(err)
			}
		})
	}
}

func (s *ITLazadaMMOrderCreator) TestITLazadaMMOrderCreator_CreateMMCCParcel() {
	const (
		partnerID = 88426
		bigBagID1 = "SZ02-BA208111-KL45299"
		bigBagID2 = "SZ02-LA108091-HJ4520991"
	)

	// Common test data
	baseAddress := order.Address{
		Name:          "David",
		AddressLine1:  "金港北三路6号",
		AddressLine2:  null.StringFrom("龙地广州空港物流园").Ptr(),
		AddressLine3:  null.StringFrom("P栋311").Ptr(),
		AddressLine4:  null.StringFrom("空港花都").Ptr(),
		City:          null.StringFrom("广州市").Ptr(),
		StateProvince: null.StringFrom("广东省").Ptr(),
		CountryCode:   "CN",
		PostCode:      null.StringFrom("510801").Ptr(),
	}

	toAddress := order.Address{
		Name:          "Jem",
		AddressLine1:  "1/8, Persiaran Jubli Perak",
		AddressLine2:  null.StringFrom("Seksyen 22").Ptr(),
		City:          null.StringFrom("Shah Alam").Ptr(),
		StateProvince: null.StringFrom("Selangor Darul Ehsan").Ptr(),
		CountryCode:   "MY",
		PostCode:      null.StringFrom("40300").Ptr(),
		ContactNumber: null.StringFrom("0123456789").Ptr(),
	}

	parcelItem := &order.ParcelItem{
		Description:               null.StringFrom("Laptop").Ptr(),
		Quantity:                  null.IntFrom(1).Ptr(),
		UnitValue:                 null.Float64From(1000.0).Ptr(),
		UnitWeight:                null.Float64From(1.5).Ptr(),
		HSCode:                    null.UintFrom(847130).Ptr(),
		OriginCountry:             null.StringFrom("MY").Ptr(),
		InvoiceURL:                null.StringFrom("https://invoice_url").Ptr(),
		GoodsCurrency:             null.StringFrom("MYR").Ptr(),
		IsGstIncludedInGoodsValue: false,
		GstRegistrationNumber:     null.StringFrom("1234567890").Ptr(),
		SKUId:                     null.StringFrom("SKU123").Ptr(),
		Url:                       null.StringFrom("https://product_url").Ptr(),
	}

	createBaseRequest := func(bigBagID, refTrackingID, shipperRefNo string, dimensions *models.Dimensions) *order.BaseRequest {
		return &order.BaseRequest{
			Source:        0,
			SourceOrderID: null.StringFrom(bigBagID).Ptr(),
			RefTrackingID: null.StringFrom(refTrackingID).Ptr(),
			From:          baseAddress,
			To:            toAddress,
			Pickup:        nil,
			Delivery:      nil,
			ParcelDetails: &models.ParcelDetails{
				ShipperSubmittedWeight:     null.Float64From(1.5).Ptr(),
				ShipperSubmittedWeightUnit: null.StringFrom("KG").Ptr(),
				Value:                      null.Float64From(100.0).Ptr(),
				ShipperSubmittedDimensions: dimensions,
				ShipperRefNo:               shipperRefNo,
			},
			Items:                 []*order.ParcelItem{parcelItem},
			DocumentIDs:           nil,
			IsMMCCB2C:             false,
			OriginGlobalShipperID: nil,
			Corporate:             nil,
			CustomsDetail:         "",
			RequestId:             shipperRefNo,
			PartnerUniqueKey:      null.StringFrom(fmt.Sprintf("%s_%s", refTrackingID, shipperRefNo)).Ptr(),
		}
	}

	verifyParcel := func(parcel *models.Parcel, expectedSourceOrderID string, expectedRefTrackingID string, expectedParentID *uint) error {
		expectedParcel := &models.Parcel{
			SourceOrderID:     null.StringFrom(expectedSourceOrderID),
			RefTrackingID:     null.StringFrom(expectedRefTrackingID),
			Type:              parcelConst.MMCCParcel,
			PartnerID:         null.Uint64From(partnerID),
			FromName:          null.StringFrom(baseAddress.Name),
			FromAddressLine1:  null.StringFrom(baseAddress.AddressLine1),
			FromAddressLine2:  null.StringFromPtr(baseAddress.AddressLine2),
			FromAddressLine3:  null.StringFromPtr(baseAddress.AddressLine3),
			FromAddressLine4:  null.StringFromPtr(baseAddress.AddressLine4),
			FromCity:          null.StringFromPtr(baseAddress.City),
			FromStateProvince: null.StringFromPtr(baseAddress.StateProvince),
			FromCountryCode:   null.StringFrom(baseAddress.CountryCode),
			FromPostcode:      null.StringFromPtr(baseAddress.PostCode),
			ToName:            null.StringFrom(toAddress.Name),
			ToAddressLine1:    null.StringFrom(toAddress.AddressLine1),
			ToAddressLine2:    null.StringFromPtr(toAddress.AddressLine2),
			ToCity:            null.StringFromPtr(toAddress.City),
			ToStateProvince:   null.StringFromPtr(toAddress.StateProvince),
			ToCountryCode:     null.StringFrom(toAddress.CountryCode),
			ToPostcode:        null.StringFromPtr(toAddress.PostCode),
			ToContactNumber:   null.StringFromPtr(toAddress.ContactNumber),
		}

		if expectedParentID != nil {
			expectedParcel.ParentID = null.UintFrom(*expectedParentID)
		}

		if !testutils.CompareNonEmptyFields(parcel, expectedParcel) {
			return fmt.Errorf("expected parcel to be created with fields %+v, but got %+v", expectedParcel, parcel)
		}
		return nil
	}

	verifyParcelItems := func(parcelID uint) error {
		items, err := repositories.NewParcelItemRepository().GetListWithCtx(
			context.Background(),
			models.ParcelItemWhere.ParcelID.EQ(uint64(parcelID)),
		)
		if err != nil {
			return fmt.Errorf("failed to get parcel items: %w", err)
		}
		if len(items) != 1 {
			return fmt.Errorf("expected 1 item to be created, but got %d", len(items))
		}
		return nil
	}

	type testCase struct {
		name string
		args struct {
			bigBagID string
			request  *order.BaseRequest
		}
		verify func(bigBagID string, parcelRefTrackingID *string) error
	}

	tests := []testCase{
		{
			name: "parcels created before creating bag",
			args: struct {
				bigBagID string
				request  *order.BaseRequest
			}{
				bigBagID: bigBagID1,
				request: createBaseRequest(
					bigBagID1,
					"REF1238761FM",
					"SHIPPER1273LP0971",
					&models.Dimensions{
						Length: null.Float64From(10.0).Ptr(),
						Width:  null.Float64From(20.0).Ptr(),
						Height: null.Float64From(30.0).Ptr(),
						Unit:   null.StringFrom("cm").Ptr(),
					},
				),
			},
			verify: func(bigBagID string, parcelRefTrackingID *string) error {
				createdParcel, err := repositories.NewParcelRepository().GetOne(context.Background(),
					models.ParcelWhere.RefTrackingID.EQ(null.StringFromPtr(parcelRefTrackingID)),
					models.ParcelWhere.PartnerID.EQ(null.Uint64From(partnerID)),
				)
				if err != nil {
					return fmt.Errorf("failed to get created parcel: %w", err)
				}

				if err := verifyParcel(createdParcel, "", *parcelRefTrackingID, nil); err != nil {
					return err
				}

				return verifyParcelItems(createdParcel.ID)
			},
		},
		{
			name: "parcels created after creating bag",
			args: struct {
				bigBagID string
				request  *order.BaseRequest
			}{
				bigBagID: bigBagID2,
				request: createBaseRequest(
					bigBagID2,
					"REF1238-JK61661",
					"SHIPPER18760",
					&models.Dimensions{
						Length: null.Float64From(909.3).Ptr(),
						Width:  null.Float64From(100.0).Ptr(),
						Height: null.Float64From(2000.1).Ptr(),
						Unit:   null.StringFrom("cm").Ptr(),
					},
				),
			},
			verify: func(bigBagID string, parcelRefTrackingID *string) error {
				createdParcel, err := repositories.NewParcelRepository().GetOne(context.Background(),
					models.ParcelWhere.RefTrackingID.EQ(null.StringFromPtr(parcelRefTrackingID)),
					models.ParcelWhere.PartnerID.EQ(null.Uint64From(partnerID)),
				)
				if err != nil {
					return fmt.Errorf("failed to get created parcel: %w", err)
				}

				bag, err := repositories.NewParcelRepository().GetOne(context.Background(),
					models.ParcelWhere.SourceOrderID.EQ(null.StringFrom(bigBagID)),
					models.ParcelWhere.PartnerID.EQ(null.Uint64From(partnerID)),
					models.ParcelWhere.Type.EQ(parcelConst.BagB2CV2),
				)
				if err != nil {
					return fmt.Errorf("failed to get bag: %w", err)
				}

				if err := verifyParcel(createdParcel, bigBagID, *parcelRefTrackingID, &bag.ID); err != nil {
					return err
				}

				return verifyParcelItems(createdParcel.ID)
			},
		},
	}

	partner, err := repositories.NewPartnerRepository().
		GetOne(context.TODO(), models.PartnerWhere.ID.EQ(partnerID))
	if err != nil {
		s.T().Fatalf("failed to get partner: %v", err)
	}

	for _, tt := range tests {
		s.T().Run(tt.name, func(t *testing.T) {
			if err := s.creator.CreateMMCCParcel(context.Background(), partner, tt.args.bigBagID, tt.args.request); err != nil {
				t.Fatalf("failed to create MMCC parcel: %v", err)
			}

			if err := tt.verify(tt.args.bigBagID, tt.args.request.RefTrackingID); err != nil {
				t.Error(err)
			}
		})
	}
}
