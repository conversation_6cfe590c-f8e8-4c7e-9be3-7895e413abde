// Code generated by MockGen. DO NOT EDIT.
// Source: ./middleware/order_request.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockorderRequestID is a mock of orderRequestID interface.
type MockorderRequestID struct {
	ctrl     *gomock.Controller
	recorder *MockorderRequestIDMockRecorder
}

// MockorderRequestIDMockRecorder is the mock recorder for MockorderRequestID.
type MockorderRequestIDMockRecorder struct {
	mock *MockorderRequestID
}

// NewMockorderRequestID creates a new mock instance.
func NewMockorderRequestID(ctrl *gomock.Controller) *MockorderRequestID {
	mock := &MockorderRequestID{ctrl: ctrl}
	mock.recorder = &MockorderRequestIDMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockorderRequestID) EXPECT() *MockorderRequestIDMockRecorder {
	return m.recorder
}

// getRequestedID mocks base method.
func (m *MockorderRequestID) getRequestedID(c *gin.Context) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "getRequestedID", c)
	ret0, _ := ret[0].(string)
	return ret0
}

// getRequestedID indicates an expected call of getRequestedID.
func (mr *MockorderRequestIDMockRecorder) getRequestedID(c interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "getRequestedID", reflect.TypeOf((*MockorderRequestID)(nil).getRequestedID), c)
}

// getRequestedTrackingID mocks base method.
func (m *MockorderRequestID) getRequestedTrackingID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "getRequestedTrackingID")
	ret0, _ := ret[0].(string)
	return ret0
}

// getRequestedTrackingID indicates an expected call of getRequestedTrackingID.
func (mr *MockorderRequestIDMockRecorder) getRequestedTrackingID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "getRequestedTrackingID", reflect.TypeOf((*MockorderRequestID)(nil).getRequestedTrackingID))
}

// MockShopeeRequestBase is a mock of ShopeeRequestBase interface.
type MockShopeeRequestBase struct {
	ctrl     *gomock.Controller
	recorder *MockShopeeRequestBaseMockRecorder
}

// MockShopeeRequestBaseMockRecorder is the mock recorder for MockShopeeRequestBase.
type MockShopeeRequestBaseMockRecorder struct {
	mock *MockShopeeRequestBase
}

// NewMockShopeeRequestBase creates a new mock instance.
func NewMockShopeeRequestBase(ctrl *gomock.Controller) *MockShopeeRequestBase {
	mock := &MockShopeeRequestBase{ctrl: ctrl}
	mock.recorder = &MockShopeeRequestBaseMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockShopeeRequestBase) EXPECT() *MockShopeeRequestBaseMockRecorder {
	return m.recorder
}

// getRefTrackingID mocks base method.
func (m *MockShopeeRequestBase) getRefTrackingID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "getRefTrackingID")
	ret0, _ := ret[0].(string)
	return ret0
}

// getRefTrackingID indicates an expected call of getRefTrackingID.
func (mr *MockShopeeRequestBaseMockRecorder) getRefTrackingID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "getRefTrackingID", reflect.TypeOf((*MockShopeeRequestBase)(nil).getRefTrackingID))
}

// getRequestID mocks base method.
func (m *MockShopeeRequestBase) getRequestID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "getRequestID")
	ret0, _ := ret[0].(string)
	return ret0
}

// getRequestID indicates an expected call of getRequestID.
func (mr *MockShopeeRequestBaseMockRecorder) getRequestID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "getRequestID", reflect.TypeOf((*MockShopeeRequestBase)(nil).getRequestID))
}
