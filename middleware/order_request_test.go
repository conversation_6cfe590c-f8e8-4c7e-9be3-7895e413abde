package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"

	"git.ninjavan.co/3pl/configs"
	middlewareCfg "git.ninjavan.co/3pl/configs/middleware"
	orderConst "git.ninjavan.co/3pl/configs/orders"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	mockRepo "git.ninjavan.co/3pl/repositories/mocks"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/utils"
)

func TestCatchOrderRequest(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name          string
		requestSource orderConst.RequestSource
		c             *gin.Context
		want          *models.OrderRequest
	}{
		{
			name: "invalid payload",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request, _ = http.NewRequest("POST", "/some-where", strings.NewReader("abc"))
				return c
			}(),
			want: nil,
		},
		{
			name: "invalid partner and shouldn't panic",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request, _ = http.NewRequest("POST", "/some-where", strings.NewReader("{}"))
				c.Set("partner", "ABC")
				return c
			}(),
			want: nil,
		},
		{
			name: "handle external endpoint which contains tracking_id successfully",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request, _ = http.NewRequest("POST", "/some-where", strings.NewReader(`{"requested_tracking_id":"abc"}`))
				c.Request.Header.Add("Content-Type", "application/json")
				c.Set("partner", &models.Partner{ID: 1})
				return c
			}(),
			requestSource: orderConst.FromExternalEndpoint,
			want: &models.OrderRequest{
				PartnerID:           1,
				RequestedTrackingID: null.StringFrom("abc"),
				RequestBody:         null.StringFrom(`{"requested_tracking_id":"abc"}`),
				RequestHeader:       null.StringFrom(`{"Content-Type":["application/json"]}`),
				Status:              uint8(configs.OrderRequestProcessing),
			},
		},
		{
			name: "handle internal endpoint which contains tracking_id successfully",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request, _ = http.NewRequest("POST", "/some-where", strings.NewReader(`{"requested_tracking_id":"abc"}`))
				c.Request.Header.Add("Content-Type", "application/json")
				c.Set("partner", &models.Partner{ID: 1})
				return c
			}(),
			requestSource: orderConst.FromInternalEndpoint,
			want: &models.OrderRequest{
				PartnerID:           1,
				RequestedTrackingID: null.StringFrom("abc"),
				RequestBody:         null.StringFrom(`{"requested_tracking_id":"abc"}`),
				RequestHeader:       null.StringFrom(`{"Content-Type":["application/json"]}`),
				Status:              uint8(configs.OrderRequestProcessing),
			},
		},
		{
			name: "handle wish endpoint which contains tracking_id successfully",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request, _ = http.NewRequest("POST", "/some-where", strings.NewReader(`{"tracking_id":"abc"}`))
				c.Request.Header.Add("Content-Type", "application/json")
				c.Set("partner", &models.Partner{ID: 1})
				return c
			}(),
			requestSource: orderConst.FromWishEndpoint,
			want: &models.OrderRequest{
				PartnerID:           1,
				RequestedTrackingID: null.StringFrom("abc"),
				RequestBody:         null.StringFrom(`{"tracking_id":"abc"}`),
				RequestHeader:       null.StringFrom(`{"Content-Type":["application/json"]}`),
				Status:              uint8(configs.OrderRequestProcessing),
			},
		},
		{
			name: "handle external endpoint which not contains tracking_id successfully",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request, _ = http.NewRequest("POST", "/some-where", strings.NewReader(`{"abc":123}`))
				c.Request.Header.Add("Content-Type", "application/json")
				c.Set("partner", &models.Partner{ID: 1})
				return c
			}(),
			requestSource: orderConst.FromExternalEndpoint,
			want: &models.OrderRequest{
				PartnerID:     1,
				RequestBody:   null.StringFrom(`{"abc":123}`),
				RequestHeader: null.StringFrom(`{"Content-Type":["application/json"]}`),
				Status:        uint8(configs.OrderRequestProcessing),
			},
		},
		{
			name: "handle internal endpoint which not contains tracking_id successfully",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request, _ = http.NewRequest("POST", "/some-where", strings.NewReader(`{"abc":123}`))
				c.Request.Header.Add("Content-Type", "application/json")
				c.Set("partner", &models.Partner{ID: 1})
				return c
			}(),
			requestSource: orderConst.FromInternalEndpoint,
			want: &models.OrderRequest{
				PartnerID:     1,
				RequestBody:   null.StringFrom(`{"abc":123}`),
				RequestHeader: null.StringFrom(`{"Content-Type":["application/json"]}`),
				Status:        uint8(configs.OrderRequestProcessing),
			},
		},
		{
			name: "handle wish endpoint which not contains tracking_id successfully",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request, _ = http.NewRequest("POST", "/some-where", strings.NewReader(`{"abc":123}`))
				c.Request.Header.Add("Content-Type", "application/json")
				c.Set("partner", &models.Partner{ID: 1})
				return c
			}(),
			requestSource: orderConst.FromWishEndpoint,
			want: &models.OrderRequest{
				PartnerID:     1,
				RequestBody:   null.StringFrom(`{"abc":123}`),
				RequestHeader: null.StringFrom(`{"Content-Type":["application/json"]}`),
				Status:        uint8(configs.OrderRequestProcessing),
			},
		},
		{
			name: "tiktok oc, failed to bind form",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request, _ = http.NewRequest("POST", "/some-where", strings.NewReader(`{}`))
				c.Request.Header.Add("Content-Type", "text/plain; boundary=")
				return c
			}(),
			requestSource: orderConst.FromTiktokEndpoint,
			want:          nil,
		},
		{
			name: "tiktok oc, failed to unmarshal param_json",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				formValues := url.Values{}
				formValues.Add("param_json", `{`)
				c.Request, _ = http.NewRequest("POST", "/some-where", strings.NewReader(formValues.Encode()))
				c.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")
				return c
			}(),
			requestSource: orderConst.FromTiktokEndpoint,
			want:          nil,
		},
		{
			name: "tiktok oc, package_list is empty",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				formValues := url.Values{}
				formValues.Add("param_json", `{"package_list": []}`)
				c.Request, _ = http.NewRequest("POST", "/some-where", strings.NewReader(formValues.Encode()))
				c.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")
				return c
			}(),
			requestSource: orderConst.FromTiktokEndpoint,
			want:          nil,
		},
		{
			name: "tiktok oc, catch oc request successfully",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				formValues := url.Values{}
				formValues.Add("param_json", `{"package_list": [{}]}`)
				c.Request, _ = http.NewRequest("POST", "/some-where", strings.NewReader(formValues.Encode()))
				c.Request.Header.Add("Content-Type", "application/x-www-form-urlencoded")
				c.Set("partner", &models.Partner{ID: 1})
				return c
			}(),
			requestSource: orderConst.FromTiktokEndpoint,
			want: &models.OrderRequest{
				PartnerID:           1,
				RequestedTrackingID: null.StringFrom(""),
				RequestBody:         null.StringFrom(`{"provider_order_id":"","big_bag_no":"","tracking_no":"","shipping_method_code":"","time_zone":"","operate_time":"","delivery_info":null,"eori_number":"","shipping_tax_id":"","remark":"","goods_type":null,"special_category":null,"value":null,"package":null,"items":null,"previous_carrier_info":null,"sender_info":null,"shipping_info":null,"return_type":null,"return_info":null}`),
				RequestHeader:       null.StringFrom(`{"Content-Type":["application/x-www-form-urlencoded"]}`),
				Status:              uint8(configs.OrderRequestProcessing),
			},
		},
	}

	gin.SetMode(gin.TestMode)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			CatchOrderRequest(tt.requestSource)(tt.c)

			ctxValue, found := tt.c.Get(orderRequestCtxKey)
			or, _ := ctxValue.(*models.OrderRequest)
			if found && !reflect.DeepEqual(or.PartnerID, tt.want.PartnerID) &&
				!reflect.DeepEqual(or.RequestedTrackingID, tt.want.RequestedTrackingID) &&
				!reflect.DeepEqual(or.RequestBody, tt.want.RequestBody) &&
				!reflect.DeepEqual(or.RequestHeader, tt.want.RequestHeader) &&
				!reflect.DeepEqual(or.Status, tt.want.Status) {
				fmt.Printf("%+v\n", tt.want)
				fmt.Printf("%+v\n", ctxValue)
				t.Errorf("CatchRequest() = %v, want %v", ctxValue, tt.want)
			}
		})
	}
}

func TestSaveOrderRequest(t *testing.T) {
	tests := []struct {
		name       string
		c          *gin.Context
		mockDnFunc func(mock sqlmock.Sqlmock)
		wantStatus uint8
	}{
		{
			name: "can't found order request in the context",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request, _ = http.NewRequest("", "", nil)

				return c
			}(),
		},
		{
			name: "found order request key but the data type is unexpected",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request, _ = http.NewRequest("", "", nil)

				c.Set(orderRequestCtxKey, &models.Parcel{})
				return c
			}(),
		},
		{
			name: "found valid order request, but the response contains error",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request, _ = http.NewRequest("", "", nil)

				c.Set(respWriterCtxKey, nil)
				c.Set(orderRequestCtxKey, &models.OrderRequest{})
				_ = c.Error(errors.New("an error just happened"))
				return c
			}(),
			mockDnFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("INSERT INTO `order_requests` (.+)").
					WillReturnResult(sqlmock.NewResult(1, 1))
				mock.ExpectQuery("SELECT (.+) FROM `order_requests` WHERE (.+)").
					WillReturnRows(sqlmock.NewRows([]string{"id", "created_at", "updated_at"}).AddRow(1, time.Now(), time.Now()))
			},
			wantStatus: uint8(configs.OrderRequestErrBadRequest),
		},
		{
			name: "found valid order request, and response contains no problem",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request, _ = http.NewRequest("", "", nil)

				c.Set(respWriterCtxKey, &ResponseBodyWriter{Body: &bytes.Buffer{}})
				SetParcelContext(c, &models.Parcel{ID: 1})
				c.Set(orderRequestCtxKey, &models.OrderRequest{})
				return c
			}(),
			mockDnFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("INSERT INTO `order_requests` (.+)").
					WillReturnResult(sqlmock.NewResult(1, 1))
				mock.ExpectQuery("SELECT (.+) FROM `order_requests` WHERE (.+)").
					WillReturnRows(sqlmock.NewRows([]string{"id", "created_at", "updated_at"}).AddRow(1, time.Now(), time.Now()))
			},
			wantStatus: uint8(configs.OrderRequestSuccess),
		},
		{
			name: "got db error",
			c: func() *gin.Context {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request, _ = http.NewRequest("", "", nil)

				c.Set(orderRequestCtxKey, &models.OrderRequest{})
				return c
			}(),
			mockDnFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec("INSERT INTO `order_requests` (.+)").
					WillReturnError(errors.New("db error"))
			},
		},
	}

	gin.SetMode(gin.TestMode)
	db, mock := utils.StartMockDB(false)
	defer func() {
		_ = db.Close()
	}()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockDnFunc != nil {
				tt.mockDnFunc(mock)
			}

			SaveOrderRequest()(tt.c)
			got, _ := tt.c.Get(orderRequestCtxKey)
			orderRequest, _ := got.(*models.OrderRequest)
			if tt.wantStatus != 0 && tt.wantStatus != orderRequest.Status {
				t.Errorf("SaveOrderRequest() = %v, wantStatus %v", orderRequest.Status, tt.wantStatus)
			}

			if tt.mockDnFunc != nil {
				assert.Nil(t, mock.ExpectationsWereMet())
			}
		})
	}
}

func TestSaveLazadaCCOrderRequest(t *testing.T) {
	// Save original repository function to restore after test
	origOrderRequestRepo := newOrderRequestRepo
	defer func() {
		newOrderRequestRepo = origOrderRequestRepo
	}()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name           string
		requestBody    interface{}
		headers        map[string]string
		expectedReqID  string
		expectedTID    string
		partnerID      uint64
		parcelID       uint
		statusCode     int
		shouldBindFail bool
		mockRepoFunc   func(t *testing.T) repo_interface.OrderRequestRepository
	}{
		{
			name: "successful request with order code",
			requestBody: order.LazadaCreateParcelReq{
				OrderCode: "ORDER123",
			},
			headers: map[string]string{
				"x-acs-req-uuid": "req-uuid-123",
				"Content-Type":   "application/json",
			},
			expectedReqID: "req-uuid-123",
			expectedTID:   "ORDER123",
			partnerID:     1000,
			statusCode:    http.StatusOK,
			mockRepoFunc: func(t *testing.T) repo_interface.OrderRequestRepository {
				mockRepo := mockRepo.NewMockOrderRequestRepository(ctrl)
				mockRepo.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, req *models.OrderRequest) error {
						if req.RequestID != "req-uuid-123" ||
							req.RequestedTrackingID.String != "ORDER123" ||
							req.PartnerID != 1000 {
							return fmt.Errorf("unexpected request: %+v", req)
						}
						return nil
					})
				return mockRepo
			},
		},
		{
			name: "request with no x-acs-req-uuid header",
			requestBody: order.LazadaCreateParcelReq{
				OrderCode: "ORDER456",
			},
			headers: map[string]string{
				"Content-Type": "application/json",
			},
			expectedTID: "ORDER456",
			partnerID:   1000,
			statusCode:  http.StatusOK,
			mockRepoFunc: func(t *testing.T) repo_interface.OrderRequestRepository {
				mockRepo := mockRepo.NewMockOrderRequestRepository(ctrl)
				mockRepo.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, req *models.OrderRequest) error {
						// UUID is generated, so we can't check exact value
						if req.RequestedTrackingID.String != "ORDER456" ||
							req.PartnerID != 1000 {
							return fmt.Errorf("unexpected request: %+v", req)
						}
						return nil
					})
				return mockRepo
			},
		},
		{
			name: "request with parcel context",
			requestBody: order.LazadaCreateParcelReq{
				OrderCode: "ORDER789",
			},
			headers: map[string]string{
				"x-acs-req-uuid": "req-uuid-789",
				"Content-Type":   "application/json",
			},
			expectedReqID: "req-uuid-789",
			expectedTID:   "ORDER789",
			partnerID:     1000,
			parcelID:      123,
			statusCode:    http.StatusOK,
			mockRepoFunc: func(t *testing.T) repo_interface.OrderRequestRepository {
				mockRepo := mockRepo.NewMockOrderRequestRepository(ctrl)
				mockRepo.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, req *models.OrderRequest) error {
						if req.RequestID != "req-uuid-789" ||
							req.RequestedTrackingID.String != "ORDER789" ||
							req.PartnerID != 1000 ||
							req.ParcelID.Uint != 123 {
							return fmt.Errorf("unexpected request: %+v", req)
						}
						return nil
					})
				return mockRepo
			},
		},
		{
			name:           "invalid request binding",
			requestBody:    "invalid json",
			headers:        map[string]string{"Content-Type": "application/json"},
			partnerID:      1000,
			statusCode:     http.StatusOK,
			shouldBindFail: true,
			mockRepoFunc: func(t *testing.T) repo_interface.OrderRequestRepository {
				mockRepo := mockRepo.NewMockOrderRequestRepository(ctrl)
				mockRepo.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(nil)
				return mockRepo
			},
		},
		{
			name:        "error response",
			requestBody: order.LazadaCreateParcelReq{OrderCode: "ERROR"},
			headers: map[string]string{
				"x-acs-req-uuid": "req-uuid-error",
				"Content-Type":   "application/json",
			},
			expectedReqID: "req-uuid-error",
			expectedTID:   "ERROR",
			partnerID:     1000,
			statusCode:    http.StatusBadRequest,
			mockRepoFunc: func(t *testing.T) repo_interface.OrderRequestRepository {
				mockRepo := mockRepo.NewMockOrderRequestRepository(ctrl)
				mockRepo.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, req *models.OrderRequest) error {
						if req.RequestID != "req-uuid-error" ||
							req.RequestedTrackingID.String != "ERROR" ||
							req.PartnerID != 1000 ||
							req.Status != configs.NewOrderRequestStatus(http.StatusBadRequest).Uint8() {
							return fmt.Errorf("unexpected request: %+v", req)
						}
						return nil
					})
				return mockRepo
			},
		},
		{
			name:        "database error",
			requestBody: order.LazadaCreateParcelReq{OrderCode: "DB_ERROR"},
			headers: map[string]string{
				"x-acs-req-uuid": "req-uuid-db-error",
				"Content-Type":   "application/json",
			},
			expectedReqID: "req-uuid-db-error",
			expectedTID:   "DB_ERROR",
			partnerID:     1000,
			statusCode:    http.StatusOK,
			mockRepoFunc: func(t *testing.T) repo_interface.OrderRequestRepository {
				mockRepo := mockRepo.NewMockOrderRequestRepository(ctrl)
				mockRepo.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(errors.New("database error"))
				return mockRepo
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {

			// Override the repository function
			newOrderRequestRepo = func() repo_interface.OrderRequestRepository {
				return tt.mockRepoFunc(t)
			}

			// Setup
			gin.SetMode(gin.TestMode)
			w := httptest.NewRecorder()
			c, r := gin.CreateTestContext(w)

			// Create request
			var reqBody []byte
			var err error
			if tt.shouldBindFail {
				reqBody = []byte(tt.requestBody.(string))
			} else {
				reqBody, err = json.Marshal(tt.requestBody)
				if err != nil {
					t.Fatalf("Failed to marshal request body: %v", err)
				}
			}

			req := httptest.NewRequest(http.MethodPost, "/lazada/cc/orders", bytes.NewBuffer(reqBody))
			for k, v := range tt.headers {
				req.Header.Set(k, v)
			}

			// Setup context
			c.Request = req

			// Set parcel context if needed
			if tt.parcelID > 0 {
				SetParcelContext(c, &models.Parcel{ID: tt.parcelID})
			}

			// Setup response status
			r.POST("/lazada/cc/orders", func(c *gin.Context) {
				partner := &models.Partner{ID: tt.partnerID}
				c.Set(middlewareCfg.PartnerKey, partner)
				c.Status(tt.statusCode)
			}, SaveLazadaCCOrderRequest())

			// Execute
			r.ServeHTTP(w, req)

			// Assert
			assert.Equal(t, tt.statusCode, w.Code)
		})
	}
}

func TestSaveLazadaCainiaoMMOrderRequest(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name           string
		path           string
		logisticsData  interface{}
		expectedReqID  string
		expectedTID    string
		shouldBindFail bool
	}{
		{
			name: "bag create request",
			path: "/bag-create",
			logisticsData: lazadaCainiaoMMBagRequest{
				BigBagID: "BAG123",
			},
			expectedReqID: "BAG123",
			expectedTID:   "BAG123",
		},
		{
			name: "bag update request",
			path: "/bag-update",
			logisticsData: lazadaCainiaoMMBagRequest{
				BigBagID: "BAG456",
			},
			expectedReqID: "BAG456",
			expectedTID:   "BAG456",
		},
		{
			name: "parcel create request",
			path: "/parcel-create",
			logisticsData: lazadaCainiaoMMParcelRequest{
				LogisticsOrderCode: "TN789",
				Parcel: struct {
					BigBagID string `json:"bigBagID"`
				}{
					BigBagID: "BAG789",
				},
			},
			expectedReqID: "TN789",
			expectedTID:   "BAG789",
		},
		{
			name:           "invalid request binding",
			path:           "/bag-create",
			shouldBindFail: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			db, mock, _ := sqlmock.New()
			boil.SetDB(db)
			mock.ExpectBegin()
			mock.ExpectCommit()

			// Setup
			gin.SetMode(gin.TestMode)
			w := httptest.NewRecorder()
			c, r := gin.CreateTestContext(w)

			// Create request
			var reqBody order.LazadaRequest
			if !tt.shouldBindFail {
				logisticsJSON, _ := json.Marshal(tt.logisticsData)
				reqBody = order.LazadaRequest{
					LogisticsInterface: string(logisticsJSON),
				}
			}

			jsonData, _ := json.Marshal(reqBody)
			req := httptest.NewRequest(http.MethodPost, tt.path, bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")

			// Setup context
			c.Request = req
			partner := &models.Partner{ID: 1}
			c.Set(middlewareCfg.PartnerKey, partner)

			// Execute
			r.POST(tt.path, SaveLazadaCainiaoMMOrderRequest())
			r.ServeHTTP(w, req)

			// Assert
			assert.Equal(t, http.StatusOK, w.Code)
		})
	}
}

func TestSaveLazadaMMOrderRequest(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name           string
		path           string
		requestBody    interface{}
		expectedReqID  string
		expectedTID    string
		shouldBindFail bool
	}{
		{
			name: "bag create request",
			path: "/bag-create",
			requestBody: lazadaMMBagRequest{
				BigBagID:     "BAG123",
				IdempotentId: "REQ123",
			},
			expectedReqID: "REQ123",
			expectedTID:   "BAG123",
		},
		{
			name: "parcel create request",
			path: "/parcel-create",
			requestBody: lazadaMMParcelRequest{
				OrderCode:    "ORDER456",
				IdempotentId: "REQ456",
			},
			expectedReqID: "REQ456",
			expectedTID:   "ORDER456",
		},
		{
			name:           "invalid request binding",
			path:           "/bag-create",
			shouldBindFail: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			db, _, _ := sqlmock.New()
			boil.SetDB(db)

			// Setup
			gin.SetMode(gin.TestMode)
			w := httptest.NewRecorder()
			c, r := gin.CreateTestContext(w)

			// Create request
			var reqBody []byte
			if !tt.shouldBindFail {
				reqBody, _ = json.Marshal(tt.requestBody)
			} else {
				reqBody = []byte("invalid json")
			}

			req := httptest.NewRequest(http.MethodPost, tt.path, bytes.NewBuffer(reqBody))
			req.Header.Set("Content-Type", "application/json")

			// Setup context
			c.Request = req
			partner := &models.Partner{ID: 1}
			c.Set(middlewareCfg.PartnerKey, partner)

			// Execute
			r.POST(tt.path, SaveLazadaMMOrderRequest())
			r.ServeHTTP(w, req)

			// Assert
			assert.Equal(t, http.StatusOK, w.Code)
		})
	}
}
