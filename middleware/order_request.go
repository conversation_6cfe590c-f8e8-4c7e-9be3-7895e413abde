package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/golang-jwt/jwt/v5"
	"github.com/hashicorp/go-uuid"
	"github.com/rs/zerolog/log"
	"github.com/volatiletech/null/v8"

	commons "bitbucket.ninjavan.co/commons/go"
	commonCache "bitbucket.ninjavan.co/commons/redis-commons---go/v2/cache"

	"git.ninjavan.co/3pl/cache"
	"git.ninjavan.co/3pl/configs"
	middlewareCfg "git.ninjavan.co/3pl/configs/middleware"
	orderConst "git.ninjavan.co/3pl/configs/orders"
	"git.ninjavan.co/3pl/envs"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/repositories/repo_interface"
	"git.ninjavan.co/3pl/utils"
	"git.ninjavan.co/3pl/utils/ctxutils"
	"git.ninjavan.co/3pl/utils/loggerutils"
	"git.ninjavan.co/3pl/utils/normalize"
)

const (
	orderRequestCtxKey                   = "order_request"
	respWriterCtxKey                     = "resp_writer"
	parcelCtxKey                         = "parcel"
	requestedTrackingIDKey               = "requested_tracking_id"
	cacheRateLimitRequestedTrackingIDKey = "4pl:cache_limit_requested_tracking_id:%d:%s"
	failedToSaveOrderRequestErrMsg       = "can't insert order request"
)

func SetParcelContext(c *gin.Context, parcel *models.Parcel) {
	c.Set(parcelCtxKey, parcel)
}

var (
	newOrderRequestRepo = func() repo_interface.OrderRequestRepository {
		return repositories.NewOrderRequestRepository()
	}
)

// CatchOrderRequest takes request from gin context
// then check if the request is matched to any struct of order requests
// then initialize a context value for them
func CatchOrderRequest(requestSource orderConst.RequestSource) gin.HandlerFunc {
	return tracingWrapper(func(c *gin.Context) {
		var (
			err     error
			bodyMap map[string]interface{}
		)

		switch requestSource {
		case orderConst.FromTiktokEndpoint:
			r := order.TiktokRequest{}
			err = c.ShouldBindWith(&r, binding.Form)
			if err != nil {
				c.Next()
				return
			}
			var ocRequest order.TiktokCreateRequest
			err = json.Unmarshal([]byte(r.ParamJSON), &ocRequest)
			if err != nil {
				c.Next()
				return
			}
			if len(ocRequest) == 0 {
				c.Next()
				return
			}
			b, _ := json.Marshal(*ocRequest[0])
			c.Set(gin.BodyBytesKey, b)
			err = c.ShouldBindBodyWith(&bodyMap, binding.JSON)
		case orderConst.FromTiktokMMCCPreAlert:
			r := order.TiktokRequest{}
			err = c.ShouldBindWith(&r, binding.Form)
			if err != nil {
				c.Next()
				return
			}
			var req order.TiktokMMCCPreAlert
			err = json.Unmarshal([]byte(r.ParamJSON), &req)
			if err != nil {
				c.Next()
				return
			}
			c.Set(gin.BodyBytesKey, []byte(r.ParamJSON))
			err = c.ShouldBindBodyWith(&bodyMap, binding.JSON)

		case orderConst.FromTiktokMMCCEndpoint:
			r := order.TiktokRequest{}
			err = c.ShouldBindWith(&r, binding.Form)
			if err != nil {
				c.Next()
				return
			}

			var ocRequest order.TiktokMMCCCreateRequest
			err = json.Unmarshal([]byte(r.ParamJSON), &ocRequest)
			if err != nil {
				c.Next()
				return
			}

			if len(ocRequest.PackageList) == 0 {
				c.Next()
				return
			}

			b, _ := json.Marshal(ocRequest)
			c.Set(gin.BodyBytesKey, b)
			err = c.ShouldBindBodyWith(&bodyMap, binding.JSON)
			bodyMap["big_bag_no"] = ocRequest.PackageList[0].BigBagNo

			fallthrough

		default:
			err = c.ShouldBindBodyWith(&bodyMap, binding.JSON)
		}

		if err != nil {
			c.Next()
			return
		}

		p, _ := c.Get(middlewareCfg.PartnerKey)
		partner, _ := p.(*models.Partner)

		var tidKey string
		switch requestSource {
		case orderConst.FromInternalEndpoint, orderConst.FromExternalEndpoint, orderConst.FromEPIEndpoint:
			tidKey = "requested_tracking_id"
		case orderConst.FromWishEndpoint:
			tidKey = "tracking_id"
		case orderConst.FromTiktokEndpoint:
			tidKey = "tracking_no"
		case orderConst.FromTiktokMMCCPreAlert, orderConst.FromTiktokMMCCEndpoint:
			tidKey = "big_bag_no"
		default:
			c.Next()
			return
		}

		var requestedTrackingID null.String
		val, found := bodyMap[tidKey]
		if found {
			nVal, ok := val.(string)
			if ok {
				requestedTrackingID = null.StringFrom(nVal)
				if requestedTrackingID.String != "" {
					c.Set(requestedTrackingIDKey, requestedTrackingID.String)
				}
			}
		}

		var body []byte
		if cb, ok := c.Get(gin.BodyBytesKey); ok {
			if cbb, ok := cb.([]byte); ok {
				body = cbb
			}
		}

		bodyStr := string(body)
		_ = normalize.Normalize(&bodyStr)
		c.Set(gin.BodyBytesKey, []byte(bodyStr))

		requestHeader, _ := json.Marshal(c.Request.Header)

		orderRequest := &models.OrderRequest{
			RequestID:           c.GetString("_reqID"),
			RequestedTrackingID: requestedTrackingID,
			RequestBody:         null.StringFrom(bodyStr),
			RequestHeader:       null.StringFrom(string(requestHeader)),
			PartnerID:           partner.ID,
			Status:              uint8(configs.OrderRequestProcessing),
			CreatedAt:           time.Now().UTC(),
		}

		c.Set(orderRequestCtxKey, orderRequest)
		blw := &ResponseBodyWriter{Body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Set(respWriterCtxKey, blw)
		c.Writer = blw
		c.Next()
	}, "CatchOrderRequest")
}

// SaveOrderRequest takes order request from gin context
// and the parcel, latest error of any order request api response
// then update order requests
func SaveOrderRequest() gin.HandlerFunc {
	return tracingWrapper(func(c *gin.Context) {
		logger := loggerutils.WithContextRequestId(c.Request.Context(), commons.Logger)
		o, found := c.Get(orderRequestCtxKey)
		if !found {
			logger.Log().
				Interface("ctx_order_request", o).
				Msg("can't assert order request")
			return
		}

		orderRequest, ok := o.(*models.OrderRequest)
		if !ok {
			logger.Log().
				Interface("ctx_order_request", o).
				Msg("can't assert order request")
			return
		}

		parcel, _ := c.Value(parcelCtxKey).(*models.Parcel)

		orderRequest.Status = uint8(configs.OrderRequestSuccess)
		if respErr := c.Errors.Last(); respErr != nil {
			orderRequest.Status = uint8(configs.OrderRequestErrBadRequest)
		} else if parcel != nil {
			orderRequest.ParcelID = null.UintFrom(parcel.ID)
		}

		rawRw, _ := c.Get(respWriterCtxKey)
		rw, _ := rawRw.(*ResponseBodyWriter)
		if rw != nil {
			_, orderRequest.ResponseBody = getResponseDetails(c, rw)
		} else {
			logger.Error().Msg("can't parse response Body writer")
		}

		err := repositories.NewOrderRequestRepository().Create(c, orderRequest)
		if err != nil {
			logger.Log().
				Err(err).
				Interface("order_request", orderRequest).
				Msg(failedToSaveOrderRequestErrMsg)
		}
	}, "SaveOrderRequest")
}

type lazadaLogisticsRequest struct {
	ExpressNumber string `json:"expressNumber"`
	UniqueCode    string `json:"uniqueCode"`
}

func SaveLazadaOrderRequest() gin.HandlerFunc {
	return tracingWrapper(func(c *gin.Context) {
		r := &order.LazadaRequest{}
		if err := c.ShouldBindWith(r, binding.Form); err != nil {
			c.Next()
			return
		}
		req := &lazadaLogisticsRequest{}
		if err := json.Unmarshal([]byte(r.LogisticsInterface), req); err != nil {
			c.Next()
			return
		}

		writer := &ResponseBodyWriter{Body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = writer

		c.Next()

		_ = normalize.Normalize(&r.LogisticsInterface)
		p, _ := c.Get(middlewareCfg.PartnerKey)
		partner, _ := p.(*models.Partner)
		orderRequest := &models.OrderRequest{
			RequestID:           req.UniqueCode,
			RequestedTrackingID: null.StringFrom(req.ExpressNumber),
			RequestBody:         null.StringFrom(r.LogisticsInterface),
			RequestHeader:       null.StringFrom(string(utils.JsonMarshalIgnoreError(c.Request.Header))),
			PartnerID:           partner.ID,
			Status:              configs.NewOrderRequestStatus(writer.StatusCode).Uint8(),
			ResponseBody:        writer.Body.String(),
		}
		parcel, _ := c.Value(parcelCtxKey).(*models.Parcel)
		if parcel != nil {
			orderRequest.ParcelID = null.UintFrom(parcel.ID)
		}

		err := repositories.NewOrderRequestRepository().Create(c, orderRequest)
		if err != nil {
			log.Ctx(c.Request.Context()).Err(err).
				Interface("order_request", orderRequest).
				Msg(failedToSaveOrderRequestErrMsg)
		}
	}, "SaveLazadaOrderRequest")
}

type orderRequestID interface {
	getRequestedTrackingID() string
	getRequestedID(c *gin.Context) string
}

type lazadaCainiaoMMBagRequest struct {
	BigBagID string `json:"bigBagID"`
}

func (r *lazadaCainiaoMMBagRequest) getRequestedTrackingID() string {
	if r == nil {
		return ""
	}
	return r.BigBagID
}

func (r *lazadaCainiaoMMBagRequest) getRequestedID(_ *gin.Context) string {
	return r.getRequestedTrackingID()
}

type lazadaCainiaoMMParcelRequest struct {
	LogisticsOrderCode string `json:"logisticsOrderCode"`
	Parcel             struct {
		BigBagID string `json:"bigBagID"`
	} `json:"parcel"`
}

func (r *lazadaCainiaoMMParcelRequest) getRequestedTrackingID() string {
	if r == nil || r.Parcel.BigBagID == "" {
		return ""
	}
	return r.Parcel.BigBagID
}

func (r *lazadaCainiaoMMParcelRequest) getRequestedID(_ *gin.Context) string {
	if r == nil {
		return ""
	}
	return r.LogisticsOrderCode
}

func SaveLazadaCainiaoMMOrderRequest() gin.HandlerFunc {
	return tracingWrapper(func(c *gin.Context) {
		r := &order.LazadaRequest{}
		if err := c.ShouldBindWith(r, binding.Form); err != nil {
			c.Next()
			return
		}

		var id orderRequestID
		var err error
		switch {
		case strings.Contains(c.Request.URL.Path, "bag-create") || strings.Contains(c.Request.URL.Path, "bag-update"):
			id = &lazadaCainiaoMMBagRequest{}
			err = json.Unmarshal([]byte(r.LogisticsInterface), &id)
		case strings.Contains(c.Request.URL.Path, "parcel-create"):
			id = &lazadaCainiaoMMParcelRequest{}
			err = json.Unmarshal([]byte(r.LogisticsInterface), &id)
		}

		if err != nil {
			log.Ctx(c.Request.Context()).Err(err).Msg("failed to parse request")
			c.Next()
			return
		}

		writer := &ResponseBodyWriter{Body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = writer

		c.Next()

		_ = normalize.Normalize(&r.LogisticsInterface)
		p, _ := c.Get(middlewareCfg.PartnerKey)
		partner, _ := p.(*models.Partner)
		var partnerID uint64
		if partner != nil {
			partnerID = partner.ID
		}

		orderRequest := &models.OrderRequest{
			RequestID:           id.getRequestedID(c),
			RequestedTrackingID: null.StringFrom(id.getRequestedTrackingID()),
			RequestBody:         null.StringFrom(r.LogisticsInterface),
			RequestHeader:       null.StringFrom(string(utils.JsonMarshalIgnoreError(c.Request.Header))),
			PartnerID:           partnerID,
			Status:              configs.NewOrderRequestStatus(writer.StatusCode).Uint8(),
			ResponseBody:        writer.Body.String(),
		}
		parcel, _ := c.Value(parcelCtxKey).(*models.Parcel)
		if parcel != nil {
			orderRequest.ParcelID = null.UintFrom(parcel.ID)
		}

		err = repositories.NewOrderRequestRepository().Create(c, orderRequest)
		if err != nil {
			log.Ctx(c.Request.Context()).Err(err).
				Interface("order_request", orderRequest).
				Msg(failedToSaveOrderRequestErrMsg)
		}
	}, "SaveLazadaCainiaoMMOrderRequest")
}

func SaveLazadaCCOrderRequest() gin.HandlerFunc {
	return tracingWrapper(func(c *gin.Context) {
		p, _ := c.Get(middlewareCfg.PartnerKey)
		partner, _ := p.(*models.Partner)
		var partnerID uint64
		if partner != nil {
			partnerID = partner.ID
		}

		writer := &ResponseBodyWriter{Body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = writer
		c.Next()

		reqId := c.Request.Header.Get("x-acs-req-uuid")
		if reqId == "" {
			reqId, _ = uuid.GenerateUUID()
		}

		reqTid, payload := extractLazadaCCRequestPayload(c)
		orderRequest := &models.OrderRequest{
			RequestID:           reqId,
			RequestedTrackingID: null.StringFrom(reqTid),
			RequestBody:         null.StringFrom(string(payload)),
			RequestHeader:       null.StringFrom(string(utils.JsonMarshalIgnoreError(c.Request.Header))),
			PartnerID:           partnerID,
			Status:              configs.NewOrderRequestStatus(writer.StatusCode).Uint8(),
			ResponseBody:        writer.Body.String(),
		}
		parcel, _ := c.Value(parcelCtxKey).(*models.Parcel)
		if parcel != nil {
			orderRequest.ParcelID = null.UintFrom(parcel.ID)
		}

		err := newOrderRequestRepo().Create(c, orderRequest)
		if err != nil {
			log.Ctx(c.Request.Context()).Err(err).
				Interface("order_request", orderRequest).
				Msg(failedToSaveOrderRequestErrMsg)
		}
	}, "SaveLazadaCCOrderRequest")
}

// extractLazadaCCRequestPayload extracts tracking ID and payload from the request
func extractLazadaCCRequestPayload(c *gin.Context) (string, []byte) {
	var (
		reqTID  string
		payload []byte
	)

	if cb, ok := c.Get(gin.BodyBytesKey); ok {
		if cbb, ok := cb.([]byte); ok {
			payload = cbb
		}
	}

	r := order.LazadaCreateParcelReq{}
	if err := c.ShouldBindBodyWith(&r, binding.JSON); err != nil {
		json.Unmarshal(payload, &r)
	}

	reqTID = r.OrderCode

	return reqTID, payload
}

type lazadaMMBagRequest struct {
	BigBagID     string `json:"bigBagID"`
	IdempotentId string `json:"idempotentId"`
}

func (r *lazadaMMBagRequest) getRequestedTrackingID() string {
	return r.BigBagID
}

func (r *lazadaMMBagRequest) getRequestedID(c *gin.Context) string {
	reqId := c.Request.Header.Get("x-acs-req-uuid")
	if reqId == "" {
		reqId = r.IdempotentId
	}
	return reqId
}

type lazadaMMParcelRequest struct {
	OrderCode    string `json:"orderCode"`
	IdempotentId string `json:"idempotentId"`
}

func (r *lazadaMMParcelRequest) getRequestedTrackingID() string {
	return r.OrderCode
}

func (r *lazadaMMParcelRequest) getRequestedID(c *gin.Context) string {
	reqId := c.Request.Header.Get("x-acs-req-uuid")
	if reqId == "" {
		reqId = r.IdempotentId
	}
	return reqId
}

func SaveLazadaMMOrderRequest() gin.HandlerFunc {
	return func(c *gin.Context) {
		var id orderRequestID
		switch {
		case strings.Contains(c.Request.URL.Path, "bag-create"):
			id = &lazadaMMBagRequest{}
			_ = c.ShouldBindBodyWith(&id, binding.JSON)
		case strings.Contains(c.Request.URL.Path, "parcel-create"):
			id = &lazadaMMParcelRequest{}
			_ = c.ShouldBindBodyWith(&id, binding.JSON)
		}
		saveOrderRequest(id)(c)
	}
}

func saveOrderRequest(id orderRequestID) gin.HandlerFunc {
	return tracingWrapper(func(c *gin.Context) {
		writer := &ResponseBodyWriter{Body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = writer

		var reqBody map[string]any
		b := binding.Default(c.Request.Method, c.ContentType())
		var err error
		switch {
		case b == binding.JSON || b == binding.XML || b == binding.ProtoBuf || b == binding.MsgPack || b == binding.YAML || b == binding.TOML:
			err = c.ShouldBindBodyWith(&reqBody, binding.JSON)
		default:
			err = c.ShouldBind(&reqBody)
		}

		if err != nil {
			log.Ctx(c.Request.Context()).Err(err).Msg("failed to extract request body")
			c.Next()
			return
		}

		c.Next()

		p, _ := c.Get(middlewareCfg.PartnerKey)
		partner, _ := p.(*models.Partner)
		var partnerID uint64
		if partner != nil {
			partnerID = partner.ID
		}

		reqID := id.getRequestedID(c)
		if reqID == "" {
			reqID = ctxutils.GetRequestIdFromContext(c.Request.Context())
		}
		reqBodyBytes, _ := json.Marshal(reqBody)
		or := &models.OrderRequest{
			RequestID:           reqID,
			RequestedTrackingID: null.StringFrom(id.getRequestedTrackingID()),
			RequestBody:         null.StringFrom(string(reqBodyBytes)),
			RequestHeader:       null.StringFrom(string(utils.JsonMarshalIgnoreError(c.Request.Header))),
			PartnerID:           partnerID,
			Status:              configs.NewOrderRequestStatus(writer.StatusCode).Uint8(),
			ResponseBody:        writer.Body.String(),
		}
		parcel, _ := c.Value(parcelCtxKey).(*models.Parcel)
		if parcel != nil {
			or.ParcelID = null.UintFrom(parcel.ID)
		}

		err = repositories.NewOrderRequestRepository().Create(c, or)
		if err != nil {
			log.Ctx(c.Request.Context()).Err(err).
				Interface("order_request", or).
				Msg(failedToSaveOrderRequestErrMsg)
		}
	}, "SaveOrderRequest")
}

type ShopeeRequestBase interface {
	getRequestID() string
	getRefTrackingID() string
}

type shopeeOrderDataRequest struct {
	Order struct {
		UniqueID    string `json:"unique_id"`
		IlhShopeeNo string `json:"ilh_shopee_no"`
		CartonNo    string `json:"carton_no"`
		CarrierTn   string `json:"carrier_tn"`
	} `json:"order"`
}

func (r *shopeeOrderDataRequest) getRequestID() string {
	return r.Order.UniqueID
}

func (r *shopeeOrderDataRequest) getRefTrackingID() string {
	if r.Order.CartonNo != "" {
		return r.Order.CartonNo
	}
	return r.Order.CarrierTn
}

type shopeeCancelOrderDataRequest struct {
	UniqueID  string `json:"unique_id"`
	CarrierTn string `json:"carrier_tn"`
}

func (r *shopeeCancelOrderDataRequest) getRequestID() string {
	return r.UniqueID
}

func (r *shopeeCancelOrderDataRequest) getRefTrackingID() string {
	return r.CarrierTn
}

func SaveShopeeOrderRequest(requestType configs.RequestType) gin.HandlerFunc {
	orderRequestRepo := repositories.NewOrderRequestRepository()
	return tracingWrapper(func(c *gin.Context) {
		req := &order.ShopeeRequest{}
		if err := c.ShouldBindBodyWith(req, binding.JSON); err != nil {
			log.Ctx(c.Request.Context()).Err(err).Msg("failed to parse shopee request")
			c.Next()
			return
		}
		jwtParser := jwt.NewParser()
		var parts []string
		var err error
		var requestID string
		var refTracking string
		if requestType == configs.BagCancel {
			var reqData order.ShopeeDataRequest[shopeeCancelOrderDataRequest]
			_, parts, err = jwtParser.ParseUnverified(req.Jwt, &reqData)
			requestID = reqData.Data.getRequestID()
			refTracking = reqData.Data.getRefTrackingID()
		} else {
			var reqData order.ShopeeDataRequest[shopeeOrderDataRequest]
			_, parts, err = jwtParser.ParseUnverified(req.Jwt, &reqData)
			requestID = reqData.Data.getRequestID()
			refTracking = reqData.Data.getRefTrackingID()
		}

		if err != nil {
			log.Ctx(c.Request.Context()).Err(err).Msg("failed to parse JWT")
			c.Next()
			return
		}

		writer := &ResponseBodyWriter{Body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = writer

		c.Next()

		p, _ := c.Get(middlewareCfg.PartnerKey)
		partner, _ := p.(*models.Partner)
		payload, _ := jwtParser.DecodeSegment(parts[1])
		orderRequest := &models.OrderRequest{
			RequestID:           requestID,
			RequestedTrackingID: null.StringFrom(refTracking),
			RequestBody:         null.StringFrom(string(payload)),
			RequestHeader:       null.StringFrom(string(utils.JsonMarshalIgnoreError(c.Request.Header))),
			PartnerID:           partner.ID,
			Status:              configs.NewOrderRequestStatus(writer.StatusCode).Uint8(),
			ResponseBody:        writer.Body.String(),
		}
		parcel, _ := c.Value(parcelCtxKey).(*models.Parcel)
		if parcel != nil {
			orderRequest.ParcelID = null.UintFrom(parcel.ID)
		}
		err = orderRequestRepo.Create(c, orderRequest)
		if err != nil {
			log.Ctx(c.Request.Context()).Err(err).
				Interface("order_request", orderRequest).
				Msg("failed to insert order request")
		}
	}, "SendShopeeOrderRequest")
}

func RateLimitRequestedTrackingID() gin.HandlerFunc {
	cacheHandler := cache.NewHandler()

	return tracingWrapper(func(c *gin.Context) {
		v, _ := c.Get(middlewareCfg.PartnerKey)
		partner, _ := v.(*models.Partner)
		requestedTrackingID, _ := c.Get(requestedTrackingIDKey)

		// do not handle for
		if requestedTrackingID == nil {
			c.Next()
			return
		}

		requestedTrackingIDKey := fmt.Sprintf(cacheRateLimitRequestedTrackingIDKey, partner.ID, requestedTrackingID)

		err := cacheHandler.Add(c, requestedTrackingIDKey, 1, time.Duration(envs.Instance.RateLimitedRequestedTrackingIdInSecond)*time.Second)

		if err == commonCache.ErrNotStored {
			c.AbortWithStatus(http.StatusTooManyRequests)
			return
		}
	}, "RateLimitRequestedTrackingID")
}

type ResponseBodyWriter struct {
	gin.ResponseWriter
	Body       *bytes.Buffer
	StatusCode int
}

func (w *ResponseBodyWriter) Write(s []byte) (int, error) {
	w.Body.Write(s)
	return w.ResponseWriter.Write(s)
}

func (w *ResponseBodyWriter) WriteString(s string) (int, error) {
	w.Body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}

func (w *ResponseBodyWriter) WriteHeader(code int) {
	w.StatusCode = code
	w.ResponseWriter.WriteHeader(code)
}
