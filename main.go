package main

import (
	"context"
	"flag"
	"net/http"
	"strings"
	"time"

	"github.com/foolin/goview"
	"github.com/foolin/goview/supports/ginview"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
	"github.com/samber/lo"
	"github.com/volatiletech/sqlboiler/v4/boil"

	commonsCfg "bitbucket.ninjavan.co/cg/base-commons---go/config"
	"bitbucket.ninjavan.co/cg/datadog-agent---go/nvgin"
	"bitbucket.ninjavan.co/cg/datadog-agent---go/nvprofiler"
	"bitbucket.ninjavan.co/cg/datadog-agent---go/nvsql"
	"bitbucket.ninjavan.co/cg/datadog-agent---go/nvtracer"
	"bitbucket.ninjavan.co/cg/db-commons---go/sql"
	"bitbucket.ninjavan.co/cg/kafka-commons---go/v3/consumer"
	"bitbucket.ninjavan.co/cg/server-commons---go/middleware"
	deprecatedMiddleware "bitbucket.ninjavan.co/commons/go/middleware"
	"bitbucket.ninjavan.co/commons/go/webapp"

	"git.ninjavan.co/3pl/cache"
	"git.ninjavan.co/3pl/configs"
	internalconsumer "git.ninjavan.co/3pl/consumers"
	consumerUtils "git.ninjavan.co/3pl/consumers/utils"
	"git.ninjavan.co/3pl/consumers/utils/utils_interface"
	"git.ninjavan.co/3pl/controllers"
	"git.ninjavan.co/3pl/envs"
	tplMiddleware "git.ninjavan.co/3pl/middleware"
	"git.ninjavan.co/3pl/schedulers"
	"git.ninjavan.co/3pl/services"
	"git.ninjavan.co/3pl/tasks"
	"git.ninjavan.co/3pl/utils"
	"git.ninjavan.co/3pl/utils/monitor"
	_ "git.ninjavan.co/3pl/validators"
)

func main() {
	nvtracer.Start()
	defer nvtracer.Stop()
	nvprofiler.Start()
	defer nvprofiler.Stop()

	go monitor.StartExporter()

	commonsConfig := commonsCfg.New()

	// Init DB
	cache.Init(commonsConfig)

	dbEnv := sql.Config(commonsConfig)
	database := nvsql.Open(
		dbEnv.DBUser, dbEnv.DBPassword, dbEnv.DBHost,
		dbEnv.DBName, dbEnv.DBTLSVersion,
		dbEnv.DBPort, dbEnv.DBMaxConnections,
	)
	boil.DebugMode = envs.Instance.Runtime.DbDebug
	boil.SetDB(database)

	tasks.Register()

	flag.Parse()
	zerolog.DefaultContextLogger = &utils.Logger

	services.SetupHttpClients(commonsConfig)

	//nvProducer := event_publisher.SetupProducer(commonsConfig)
	//go event_publisher.ReportFunc()(nvProducer)
	//
	//var nvConsumer *consumer.NVConsumer
	//if envs.Instance.Runtime.ConsumerEnable {
	//	nvConsumer = setupConsumers(commonsConfig)
	//}
	//s, err := startCronJobs(commonsConfig)
	//if err != nil {
	//	utils.Logger.Panic().Msg("failed to start cronjobs")
	//	return
	//}

	r := webEngine()
	webApp := webapp.New(&http.Server{
		Addr:              ":" + envs.Instance.Runtime.WebAppPort,
		Handler:           r,
		ReadHeaderTimeout: 10 * time.Second,
		WriteTimeout:      65 * time.Second,
	})

	defer func() {
		// wait for consumer/cronjob goroutines to finish before closing producers/DB
		time.Sleep(time.Duration(envs.Instance.Runtime.CleanUpWaitTimeInSeconds) * time.Second)

		utils.Logger.Info().Msg("Closing kafka producer")
		//err := nvProducer.Close()
		//if err != nil {
		//	utils.Logger.Err(err).Msgf("Error closing producer: %v", err)
		//}
		sql.Close(database)
	}()

	//if nvConsumer != nil {
	//	defer nvConsumer.Close()
	//}
	//defer cleanupCronJob(s)

	webApp.Serve()
}

func cleanupCronJob(s schedulers.QuartzScheduler) {
	if !envs.Instance.Runtime.EnableCronJobs {
		return
	}
	utils.Logger.Info().Msg("caught-terminating-signal-cleaning-up-cron-job")

	s.Stop()

	timeout := time.Duration(envs.Instance.Runtime.CronJobCleanUpTimeoutInSeconds) * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	err := schedulers.ResetRunningCronJobs(ctx)
	if err != nil {
		utils.Logger.Err(err).Msg("failed-to-reset-running-cron-jobs")
		return
	}

	select {
	case <-ctx.Done():
		utils.Logger.Err(err).Msg("timeout-when-clean-up")
	default:
		utils.Logger.Info().Msg("clean-up-cron-job-successfully")
	}
}

func webEngine() *gin.Engine {
	r := gin.New()
	r.Use(nvgin.New())

	// Initialize frontend render
	r.HTMLRender = ginview.New(goview.Config{
		Root:      "views/frontend",
		Extension: ".html",
		Master:    "layouts/master",
		Partials:  []string{},
		Funcs:     utils.ViewFuncMap(),
		// Default: if env is not local, should not cache the template
		DisableCache: configs.IsLocal(),
	})

	// Initialize middlewares
	r.Use(
		middleware.Header(),
		middleware.Logger(),
		deprecatedMiddleware.ParseParams(), // TODO migrate, careful of _reqID key
		tplMiddleware.HandleCORS(configs.IsLocal()),
		tplMiddleware.ResponseErrors(),
		middleware.Recovery(),
		middleware.Datadog(commonsCfg.GetConfig()),
	)

	controllers.SetupRoutes(r)

	return r
}

func startCronJobs(commonsConfig commonsCfg.Config) (schedulers.QuartzScheduler, error) {
	if !envs.Instance.Runtime.EnableCronJobs {
		return nil, nil
	}

	s, err := schedulers.NewScheduler(commonsConfig, cache.GetClient())
	if err != nil {
		utils.Logger.Error().Msgf("Create scheduler failed due to: %s", err.Error())
		return nil, err
	}

	if err = schedulers.StartScheduleJobs(s, schedulers.GetJobDefs()); err != nil {
		utils.Logger.Error().Msgf("Start schedule jobs failed due to: %s", err.Error())
		return nil, err
	}

	return s, nil
}

func setupConsumers(cfg commonsCfg.Config) *consumer.NVConsumer {
	topology := consumer.NewConsumerMap()

	addProcessor(topology,
		envs.Instance.KafkaTopics.ShipperCreationEventsProtoTopic,
		internalconsumer.NewShipperCreationEventConsumer(),
	)
	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.OrderCreateProtoTopic,
		envs.Instance.KafkaTopics.OrderCreateProtoRetryTopic,
		internalconsumer.NewOrderCreateConsumer(envs.Instance.KafkaTopics.OrderCreateProtoRetryTopic),
	)
	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.OrderProcessedEventTopic,
		envs.Instance.KafkaTopics.FplOrderProcessedRetryTopic,
		internalconsumer.NewOrderProcessedEventConsumer(envs.Instance.KafkaTopics.FplOrderProcessedRetryTopic),
	)
	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.OrderStatusProtoTopic,
		envs.Instance.KafkaTopics.OrderStatusProtoRetryTopic,
		internalconsumer.NewOrderStatusConsumer(envs.Instance.KafkaTopics.OrderStatusProtoRetryTopic),
	)
	addProcessor(topology,
		envs.Instance.KafkaTopics.OrderEventsProtoTopic,
		internalconsumer.NewOrderEventConsumer(),
	)
	addProcessor(topology,
		envs.Instance.KafkaTopics.ParcelLostDamagedTopic,
		internalconsumer.NewLostDamagedEventConsumer(),
	)
	addProcessor(topology,
		envs.Instance.KafkaTopics.AuthShipperCredentialUpdateEventTopic,
		internalconsumer.NewShipperCredentialsUpdatedConsumer(),
	)
	addProcessor(topology,
		envs.Instance.KafkaTopics.ParcelInboundedAtHubTopic,
		internalconsumer.NewParcelHubEventConsumer(),
	)
	addProcessor(topology,
		envs.Instance.KafkaTopics.ParcelSweptAtHubTopic,
		internalconsumer.NewParcelHubEventConsumer(),
	)
	addProcessor(topology,
		envs.Instance.KafkaTopics.ShipperSettingsUpdatedEventsTopic,
		internalconsumer.NewShipperSettingsUpdatedEventConsumer(),
	)
	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.FplBulkEventsUpsertedTopic,
		envs.Instance.KafkaTopics.FplBulkEventsUpsertedHandlingRetryTopic,
		internalconsumer.NewFPLBulkEventsUpsertedConsumer(envs.Instance.KafkaTopics.FplBulkEventsUpsertedHandlingRetryTopic),
	)
	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.FplMmccEventsTopic,
		envs.Instance.KafkaTopics.FplMmccEventsRetryTopic,
		internalconsumer.NewFplMMCCEventConsumer(envs.Instance.KafkaTopics.FplMmccEventsRetryTopic),
	)
	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.FplQueryCExtractionTopic,
		envs.Instance.KafkaTopics.FplQueryCExtractionRetryTopic,
		internalconsumer.NewFPLQueryCExtractionConsumer(envs.Instance.KafkaTopics.FplQueryCExtractionRetryTopic),
	)
	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.FplShipmentParcelEventTopic,
		envs.Instance.KafkaTopics.FplShipmentParcelEventRetryTopic,
		internalconsumer.NewFplUpdateShipmentParcelEventConsumer(envs.Instance.KafkaTopics.FplShipmentParcelEventRetryTopic),
	)
	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.NvCrossDockEventTopic,
		envs.Instance.KafkaTopics.FplCrossDockEventRetryTopic,
		internalconsumer.NewCrossDockEventConsumer(envs.Instance.KafkaTopics.FplCrossDockEventRetryTopic),
	)
	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.OrderEventsWithProofTopic,
		envs.Instance.KafkaTopics.FplOrderEventsWithProofRetryTopic,
		internalconsumer.NewOrderEventWithProofConsumer(envs.Instance.KafkaTopics.FplOrderEventsWithProofRetryTopic),
	)
	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.FplBulkUpsertBagEventsTopic,
		envs.Instance.KafkaTopics.FplBulkUpsertBagEventsRetryTopic,
		internalconsumer.NewFplBulkUpsertBagEvents(envs.Instance.KafkaTopics.FplBulkUpsertBagEventsRetryTopic),
	)
	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.WebhookPushResponseTopic,
		envs.Instance.KafkaTopics.WebhookPushResponseRetryTopic,
		internalconsumer.NewWebhookPushConsumer(envs.Instance.KafkaTopics.WebhookPushResponseRetryTopic),
	)

	// b2b bundle related topics
	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.MpsEvent,
		envs.Instance.KafkaTopics.RetryMpsEvent,
		internalconsumer.NewMpsEventConsumer(),
	)
	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.MpsOrderProcessedEvent,
		envs.Instance.KafkaTopics.RetryMpsOrderProcessedEvent,
		internalconsumer.NewMpsOrderProcessedEventConsumer(),
	)

	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.FplAsyncUpdateShipmentEventTopic,
		envs.Instance.KafkaTopics.FplAsyncUpdateShipmentEventRetryTopic,
		internalconsumer.NewFplUpdateShipmentEventConsumer(envs.Instance.KafkaTopics.FplAsyncUpdateShipmentEventRetryTopic),
	)
	if envs.Instance.EnableReconcileParcelEvents {
		addProcessorWithRetry(topology,
			envs.Instance.KafkaTopics.FplReconcileEventTopic,
			envs.Instance.KafkaTopics.FplReconcileEventRetryTopic,
			internalconsumer.NewFPLReconcileParcelEventsConsumer(envs.Instance.KafkaTopics.FplReconcileEventRetryTopic),
		)
	}
	addProcessorWithRetry(topology,
		envs.Instance.KafkaTopics.FplAutoTranslateDescriptionTopic,
		envs.Instance.KafkaTopics.FplAutoTranslateDescriptionRetryTopic,
		internalconsumer.NewTranslateDescriptionConsumer(envs.Instance.KafkaTopics.FplAutoTranslateDescriptionRetryTopic),
	)
	if envs.Instance.EnableReconcileWebhook {
		addProcessor(topology,
			envs.Instance.KafkaTopics.WebhookCreatedTopic,
			internalconsumer.NewWebhookCreatedConsumer(),
		)
	}

	c, err := consumer.New(context.Background(), cfg, topology)
	if err != nil {
		utils.Logger.Panic().Err(err).Msgf("Error creating consumer with topics: %v", topology.GetTopics())
	}
	return c
}

func addProcessor(topology consumer.ConsumerMap, topic string, processor utils_interface.Processor) {
	if envs.Instance.LocalDevConfig.EnabledKafkaTopics != "all" {
		topics := strings.Split(envs.Instance.LocalDevConfig.EnabledKafkaTopics, ",")
		if !lo.Contains(topics, topic) {
			return
		}
	}
	if envs.Instance.LocalDevConfig.IgnoredKafkaTopics != "" {
		topics := strings.Split(envs.Instance.LocalDevConfig.IgnoredKafkaTopics, ",")
		if lo.Contains(topics, topic) {
			return
		}
	}
	topology.AddTopic(
		topic,
		consumerUtils.NewPanicHandlingWrapper(processor),
	)
}

func addProcessorWithRetry(topology consumer.ConsumerMap, topic, retryTopic string, processor utils_interface.Processor) {
	addProcessor(topology, topic, processor)
	addProcessor(topology, retryTopic, processor)
}
