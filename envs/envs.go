package envs

import (
	"time"

	"github.com/rs/zerolog/log"

	"bitbucket.ninjavan.co/cg/base-commons---go/config"
	"bitbucket.ninjavan.co/cg/base-commons---go/env"
)

var Instance ServiceEnv

func init() {
	cfg := config.New()
	// The environment variables will be populated into the exampleEnv variable
	if err := env.Init(cfg, &Instance); err != nil {
		log.Err(err).Msg("Error while initializing environment variables")
		panic(err)
	}
}

// TODO when adding new configs, pls group similar configs following this example:
// https://bitbucket.ninjavan.co/projects/WEBHOOKS/repos/webhook-receiver/browse/internal/envs/envs.go#47,55
// can create new structs & files if necessary
type ServiceEnv struct {
	TplVendors                                  TplVendors
	AirtableApiKey                              string `envName:"AIRTABLE_API_KEY" defaultValue:""`
	AliexpressSecretKey                         string `envName:"ALIEXPRESS_SECRET_KEY" defaultValue:""`
	AsendiaSftpHost                             string `envName:"ASENDIA_SFTP_HOST" defaultValue:""`
	AsendiaSftpPassword                         string `envName:"ASENDIA_SFTP_PASSWORD" defaultValue:""`
	AsendiaSftpPort                             string `envName:"ASENDIA_SFTP_PORT" defaultValue:"22"`
	AsendiaSftpRoot                             string `envName:"ASENDIA_SFTP_ROOT" defaultValue:""`
	AsendiaSftpUsername                         string `envName:"ASENDIA_SFTP_USERNAME" defaultValue:""`
	AuthCacheTtlInMinutes                       int    `envName:"AUTH_CACHE_TTL_IN_MINUTES" defaultValue:"10"`
	AuthCacheTtlInSeconds                       int    `envName:"AUTH_CACHE_TTL_IN_SECONDS" defaultValue:"0"`
	AuthErrorCacheTtlInMinutes                  int    `envName:"AUTH_ERROR_CACHE_TTL_IN_MINUTES" defaultValue:"5"`
	AuthErrorCacheTtlInSeconds                  int    `envName:"AUTH_ERROR_CACHE_TTL_IN_SECONDS" defaultValue:"0"`
	AutoSyncHolmDeleteIntervalInDays            int    `envName:"AUTO_SYNC_HOLM_DELETE_INTERVAL_IN_DAYS" defaultValue:"60"`
	AutoSyncHolmJobExpression                   string `envName:"AUTO_SYNC_HOLM_JOB_EXPRESSION" defaultValue:"*/2 * * * *"`
	AutoSyncHolmRetryDurationInMinutes          int    `envName:"AUTO_SYNC_HOLM_RETRY_DURATION_IN_MINUTES" defaultValue:"2"`
	CainiaoDataDigest                           string `envName:"CAINIAO_DATA_DIGEST" defaultValue:""`
	CainiaoLogisticProviderId                   string `envName:"CAINIAO_LOGISTIC_PROVIDER_ID" defaultValue:""`
	CainiaoStatusUpdateUrl                      string `envName:"CAINIAO_STATUS_UPDATE_URL" defaultValue:""`
	CainiaoToCode                               string `envName:"CAINIAO_TO_CODE" defaultValue:""`
	DashPartnerTtlInMinutes                     int    `envName:"DASH_PARTNER_TTL_IN_MINUTES" defaultValue:"60"`
	DashPartnerTtlInSeconds                     int    `envName:"DASH_PARTNER_TTL_IN_SECONDS" defaultValue:"0"`
	DriverInfoCacheInHour                       int    `envName:"DRIVER_INFO_CACHE_IN_HOUR" defaultValue:"24"`
	EmailPublishDeleteIntervalInDays            int    `envName:"EMAIL_PUBLISH_DELETE_INTERVAL_IN_DAYS" defaultValue:"60"`
	EnableCacheCredential                       bool   `envName:"ENABLE_CACHE_CREDENTIAL" defaultValue:"true"`
	EnableDynamicWebhook                        bool   `envName:"ENABLE_DYNAMIC_WEBHOOK" defaultValue:"true"`
	EnableReconcileParcelEvents                 bool   `envName:"ENABLE_RECONCILE_PARCEL_EVENTS" defaultValue:"false"`
	EnableReconcileWebhook                      bool   `envName:"ENABLE_RECONCILE_WEBHOOK" defaultValue:"false"`
	EnableRelabelBackFillItemFromCore           bool   `envName:"ENABLE_RELABEL_BACK_FILL_ITEM_FROM_CORE" defaultValue:"false"`
	EventRetryWebhookSyncRetryDurationInMinutes int    `envName:"EVENT_RETRY_WEBHOOK_SYNC_RETRY_DURATION_IN_MINUTES" defaultValue:"35"`
	EventWebhookSyncDurationInMinutes           int    `envName:"EVENT_WEBHOOK_SYNC_DURATION_IN_MINUTES" defaultValue:"5"`
	EventWebhookSyncMaxRetry                    int    `envName:"EVENT_WEBHOOK_SYNC_MAX_RETRY" defaultValue:"2"`
	// if no receipt from PI is received after this period, reconcile the webhook
	EventWebhookSyncWaitTimeInMinutes                    int     `envName:"EVENT_WEBHOOK_SYNC_WAIT_TIME_IN_MINUTES" defaultValue:"120"`
	FplBulkUpsertEventBatchSize                          int     `envName:"FPL_BULK_UPSERT_EVENT_BATCH_SIZE" defaultValue:"20"`
	FplExternalOcV1GroupId                               int     `envName:"FPL_EXTERNAL_OC_V1_GROUP_ID" defaultValue:"0"`
	FplParcelCreationDeleteIntervalInDays                int     `envName:"FPL_PARCEL_CREATION_DELETE_INTERVAL_IN_DAYS" defaultValue:"60"`
	FplParcelCreationJobExpression                       string  `envName:"FPL_PARCEL_CREATION_JOB_EXPRESSION" defaultValue:"*/1 * * * *"`
	FplParcelCreationRetryDurationInMinutes              int     `envName:"FPL_PARCEL_CREATION_RETRY_DURATION_IN_MINUTES" defaultValue:"2"`
	FplParcelUpdateInfoDeleteIntervalInDays              int     `envName:"FPL_PARCEL_UPDATE_INFO_DELETE_INTERVAL_IN_DAYS" defaultValue:"60"`
	FplParcelUpdateInfoJobExpression                     string  `envName:"FPL_PARCEL_UPDATE_INFO_JOB_EXPRESSION" defaultValue:"*/1 * * * *"`
	FplParcelUpdateInfoRetryDurationInMinutes            int     `envName:"FPL_PARCEL_UPDATE_INFO_RETRY_DURATION_IN_MINUTES" defaultValue:"2"`
	GchatSpaceWebhookUri                                 string  `envName:"GCHAT_SPACE_WEBHOOK_URI" defaultValue:""`
	GchatTiktokPrealertSpaceId                           string  `envName:"GCHAT_TIKTOK_PREALERT_SPACE_ID" defaultValue:""`
	GchatTiktokPrealertSpaceWebhookKey                   string  `envName:"GCHAT_TIKTOK_PREALERT_SPACE_WEBHOOK_KEY" defaultValue:""`
	GchatTiktokPrealertSpaceWebhookToken                 string  `envName:"GCHAT_TIKTOK_PREALERT_SPACE_WEBHOOK_TOKEN" defaultValue:""`
	GchatTiktokWebhookIcon                               string  `envName:"GCHAT_TIKTOK_WEBHOOK_ICON" defaultValue:""`
	GlobalShipperDetailCacheInHour                       int     `envName:"GLOBAL_SHIPPER_DETAIL_CACHE_IN_HOUR" defaultValue:"4"`
	GoogleApplicationCredentials                         string  `envName:"GOOGLE_APPLICATION_CREDENTIALS" defaultValue:""`
	HubQueryCExtractionEnable                            bool    `envName:"HUB_QUERY_C_EXTRACTION_ENABLE" defaultValue:"true"`
	HubQueryCExtractionRoutingZoneIdChunk                int     `envName:"HUB_QUERY_C_EXTRACTION_ROUTING_ZONE_ID_CHUNK" defaultValue:"20"`
	HubQueryCExtractionSortCacheDuration                 int     `envName:"HUB_QUERY_C_EXTRACTION_SORT_CACHE_DURATION" defaultValue:"10800"`
	HubQueryCExtractionTrackingIdChunk                   int     `envName:"HUB_QUERY_C_EXTRACTION_TRACKING_ID_CHUNK" defaultValue:"10"`
	HubQueryCExtractionWorker                            int     `envName:"HUB_QUERY_C_EXTRACTION_WORKER" defaultValue:"10"`
	HubQueryCExtractionZoneCacheDuration                 int     `envName:"HUB_QUERY_C_EXTRACTION_ZONE_CACHE_DURATION" defaultValue:"10800"`
	IHerbEventExportJobExpression                        string  `envName:"IHERB_EVENT_EXPORT_JOB_EXPRESSION" defaultValue:"5/15 * * * *"`
	IHerbSGEventExportJobExpression                      string  `envName:"IHERB_SG_EVENT_EXPORT_JOB_EXPRESSION" defaultValue:"5/15 * * * *"`
	InditexEventExportJobExpression                      string  `envName:"INDITEX_EVENT_EXPORT_JOB_EXPRESSION" defaultValue:"10/15 * * * *"`
	InditexSftpHost                                      string  `envName:"INDITEX_SFTP_HOST" defaultValue:""`
	InditexSftpPort                                      string  `envName:"INDITEX_SFTP_PORT" defaultValue:"22"`
	InditexSftpPrivateKey                                string  `envName:"INDITEX_SFTP_PRIVATE_KEY_B64" defaultValue:""`
	InditexSftpRoot                                      string  `envName:"INDITEX_SFTP_ROOT" defaultValue:""`
	InditexSftpUsername                                  string  `envName:"INDITEX_SFTP_USERNAME" defaultValue:""`
	InternationalReturnServiceIds                        string  `envName:"INTERNATIONAL_RETURN_SERVICE_IDS" defaultValue:""`
	InvoiceSizeLimitInMb                                 int     `envName:"INVOICE_SIZE_LIMIT_IN_MB" defaultValue:"50"`
	InvoiceUploadSignedTtlInMinutes                      int     `envName:"INVOICE_UPLOAD_SIGNED_TTL_IN_MINUTES" defaultValue:"15"`
	JdFplLogisticsProviderCode                           string  `envName:"JD_FPL_LOGISTICS_PROVIDER_CODE" defaultValue:"Ninjavan"`
	JdFplSecretKey                                       string  `envName:"JD_FPL_SECRET_KEY" defaultValue:""`
	JdPartnerId                                          uint64  `envName:"JD_PARTNER_ID" defaultValue:"128"`
	JdTrackingUrl                                        string  `envName:"JD_TRACKING_URL" defaultValue:""`
	JobCacheExpireTimeInSeconds                          int     `envName:"JOB_CACHE_EXPIRE_TIME_IN_SECONDS" defaultValue:"1800"`
	LtCoreGraphqlMean                                    float64 `envName:"LT_CORE_GRAPHQL_MEAN" defaultValue:"55"`
	LtCoreGraphqlStdDev                                  float64 `envName:"LT_CORE_GRAPHQL_STD_DEV" defaultValue:"10"`
	MawbDocumentAssetExpiresInHour                       int     `envName:"MAWB_DOCUMENT_ASSET_EXPIRES_IN_HOUR" defaultValue:"4320"`
	MaxAutoSyncHolmJobsPerFetch                          int     `envName:"MAX_AUTO_SYNC_HOLM_JOBS_PER_FETCH" defaultValue:"1000"`
	MaxParcelCreationJobsPerFetch                        int     `envName:"MAX_PARCEL_CREATION_JOBS_PER_FETCH" defaultValue:"50"`
	MaxParcelUpdateInfoJobsPerFetch                      int     `envName:"MAX_PARCEL_UPDATE_INFO_JOBS_PER_FETCH" defaultValue:"50"`
	MaxPartnerTrackingWarmupCachePerFetch                int     `envName:"MAX_PARTNER_TRACKING_WARMUP_CACHE_PER_FETCH" defaultValue:"2000"`
	MaxPodAggregatorJobsPerFetch                         int     `envName:"MAX_POD_AGGREGATOR_JOBS_PER_FETCH" defaultValue:"2000"`
	MaxShipmentParcelEventCreate                         int     `envName:"MAX_SHIPMENT_PARCEL_EVENT_CREATE" defaultValue:"20000"`
	NotificationGetWebhookUri                            string  `envName:"NOTIFICATION_GET_WEBHOOK_URI" defaultValue:""`
	NumberWorkerGetWebhook                               int     `envName:"NUMBER_WORKER_GET_WEBHOOK" defaultValue:"3"`
	Nv3plWebRootUrl                                      string  `envName:"NV_3PL_WEB_ROOT_URL" defaultValue:""`
	NvAuthApiUrl                                         string  `envName:"NV_AUTH_API_URL" defaultValue:""`
	NvClientCredentialsExpire                            int     `envName:"NV_CLIENT_CREDENTIALS_EXPIRE" defaultValue:"1800"`
	NvCreatedAssetExpiresInHour                          int     `envName:"NV_CREATED_ASSET_EXPIRES_IN_HOUR" defaultValue:"1"`
	NvEnv                                                string  `envName:"NV_ENV" defaultValue:"prod"`
	NvGcsBucketDir                                       string  `envName:"NV_GCS_BUCKET_DIR" defaultValue:"3pl"`
	NvGcsDir                                             string  `envName:"NV_GCS_DIR" defaultValue:""`
	NvGcsInternalBucket                                  string  `envName:"NV_GCS_INTERNAL_BUCKET" defaultValue:""`
	NvInternalCoreHttpUri                                string  `envName:"NV_INTERNAL_CORE_HTTP_URI" defaultValue:""`
	NvInternalEventsHttpUri                              string  `envName:"NV_INTERNAL_EVENTS_HTTP_URI" defaultValue:""`
	NvInternalHttpUriDash                                string  `envName:"NV_INTERNAL_HTTP_URI_DASH" defaultValue:""`
	NvInternalHttpUriDriverManagement                    string  `envName:"NV_INTERNAL_HTTP_URI_DRIVER_MANAGEMENT" defaultValue:""`
	NvInternalHttpUriOrderCreate                         string  `envName:"NV_INTERNAL_HTTP_URI_ORDER_CREATE" defaultValue:"https://api-qa.ninjavan.co/%2s/order-create/"`
	NvInternalHttpUriOrderSearch                         string  `envName:"NV_INTERNAL_HTTP_URI_ORDER_SEARCH" defaultValue:""`
	NvInternalHttpUriRouteV2                             string  `envName:"NV_INTERNAL_HTTP_URI_ROUTE_V2" defaultValue:""`
	NvInternalHttpUriSort                                string  `envName:"NV_INTERNAL_HTTP_URI_SORT" defaultValue:""`
	NvInternalHttpUriTpl                                 string  `envName:"NV_INTERNAL_HTTP_URI_TPL" defaultValue:""`
	NvInternalHttpUriTplOms                              string  `envName:"NV_INTERNAL_HTTP_URI_TPL_OMS" defaultValue:""`
	NvInternalHttpUriWaybill                             string  `envName:"NV_INTERNAL_HTTP_URI_WAYBILL" defaultValue:""`
	NvInternalHttpUriWebhookSubscription                 string  `envName:"NV_INTERNAL_HTTP_URI_WEBHOOK_SUBSCRIPTION" defaultValue:""`
	NvInternalHttpUriWms                                 string  `envName:"NV_INTERNAL_HTTP_URI_WMS" defaultValue:""`
	NvInternalHttpUriXbOperations                        string  `envName:"NV_INTERNAL_HTTP_URI_XB_OPERATIONS" defaultValue:""`
	NvInternalHttpUriZones                               string  `envName:"NV_INTERNAL_HTTP_URI_ZONES" defaultValue:""`
	NvInternalHttpUriNVControl                           string  `envName:"NV_INTERNAL_HTTP_URI_NINJA_CONTROL" defaultValue:"https://api-qa.ninjavan.co/%2s/control/"`
	KafkaTopics                                          KafkaTopics
	NvRedisHost                                          string        `envName:"NV_REDIS_HOST" defaultValue:""`
	NvRedisProtocol                                      string        `envName:"NV_REDIS_PROTOCOL" defaultValue:"tcp"`
	NvReleaseId                                          string        `envName:"NV_RELEASE_ID" defaultValue:""`
	NvServiceName                                        string        `envName:"NV_SERVICE_NAME" defaultValue:"4pl"`
	NvShipperApiBaseUrl                                  string        `envName:"NV_SHIPPER_API_BASE_URL" defaultValue:""`
	OnShipmentJobExpression                              string        `envName:"ON_SHIPMENT_JOB_EXPRESSION" defaultValue:"*/1 * * * *"`
	OrderCreateV41InternalUri                            string        `envName:"ORDER_CREATE_V4_1_INTERNAL_URI" defaultValue:""`
	OrderDataCleanUpJobExpression                        string        `envName:"ORDER_DATA_CLEAN_UP_JOB_EXPRESSION" defaultValue:"* * * * *"`
	OrderDataCleanUpValidDurationBeforeDeletingInMinutes int           `envName:"ORDER_DATA_CLEAN_UP_VALID_DURATION_BEFORE_DELETING_IN_MINUTES" defaultValue:"15"`
	ParcelTrackingExpire                                 int           `envName:"PARCEL_TRACKING_EXPIRE" defaultValue:"1800"`
	PodAggregatorDeleteIntervalInDays                    int           `envName:"POD_AGGREGATOR_DELETE_INTERVAL_IN_DAYS" defaultValue:"60"`
	PodAggregatorJobExpression                           string        `envName:"POD_AGGREGATOR_JOB_EXPRESSION" defaultValue:"*/5 * * * *"`
	PodAggregatorRetryDurationInMinutes                  int           `envName:"POD_AGGREGATOR_RETRY_DURATION_IN_MINUTES" defaultValue:"5"`
	ProducerDeleteIntervalInDays                         int           `envName:"PRODUCER_DELETE_INTERVAL_IN_DAYS" defaultValue:"60"`
	ProducerRetryDurationInMinutes                       int           `envName:"PRODUCER_RETRY_DURATION_IN_MINUTES" defaultValue:"2"`
	PrometheusExporterPort                               int           `envName:"PROMETHEUS_EXPORTER_PORT" defaultValue:"5556"`
	QueryCExtractionCleanUpJobExpression                 string        `envName:"QUERY_C_EXTRACTION_CLEAN_UP_JOB_EXPRESSION" defaultValue:"0 17 * * *"`
	QueryCExtractionDeleteIntervalInDays                 int           `envName:"QUERY_C_EXTRACTION_DELETE_INTERVAL_IN_DAYS" defaultValue:"7"`
	QueryCExtractionFilenamePrefix                       string        `envName:"QUERY_C_EXTRACTION_FILENAME_PREFIX" defaultValue:"parcel_details"`
	ShipmentParcelEventDeleteIntervalInDays              int           `envName:"SHIPMENT_PARCEL_EVENT_DELETE_INTERVAL_IN_DAYS" defaultValue:"2"`
	RateLimitedRequestedTrackingIdInSecond               int           `envName:"RATE_LIMITED_REQUESTED_TRACKING_ID_IN_SECOND" defaultValue:"5"`
	ReconcileFileExpireDurationInHours                   int           `envName:"RECONCILE_FILE_EXPIRE_DURATION_IN_HOURS" defaultValue:"168"`
	ReconcileParcelEventsBulkSize                        int           `envName:"RECONCILE_PARCEL_EVENTS_BULK_SIZE" defaultValue:"100"`
	ReconcileParcelEventsCreatedFromInDays               int           `envName:"RECONCILE_PARCEL_EVENTS_CREATED_FROM_IN_DAYS" defaultValue:"30"`
	ReconcileParcelEventsCreatedToInDays                 int           `envName:"RECONCILE_PARCEL_EVENTS_CREATED_TO_IN_DAYS" defaultValue:"7"`
	ReconcileParcelEventsJobExpression                   string        `envName:"RECONCILE_PARCEL_EVENTS_JOB_EXPRESSION" defaultValue:"0 17 * * *"`
	ReconcileParcelEventsQueryIntervalInHours            int           `envName:"RECONCILE_PARCEL_EVENTS_QUERY_INTERVAL_IN_HOURS" defaultValue:"1"`
	ReconcileParcelEventsUpdatedBeforeInDays             int           `envName:"RECONCILE_PARCEL_EVENTS_UPDATED_BEFORE_IN_DAYS" defaultValue:"3"`
	ReconcileQueryIntervalInHours                        int           `envName:"RECONCILE_QUERY_INTERVAL_IN_HOURS" defaultValue:"6"`
	ReconcileQueryLimitSize                              int           `envName:"RECONCILE_QUERY_LIMIT_SIZE" defaultValue:"1000"`
	ReconcileWebhookGchatSpaceId                         string        `envName:"RECONCILE_WEBHOOK_GCHAT_SPACE_ID" defaultValue:""`
	ReconcileWebhookGchatWebhookKey                      string        `envName:"RECONCILE_WEBHOOK_GCHAT_WEBHOOK_KEY" defaultValue:""`
	ReconcileWebhookGchatWebhookToken                    string        `envName:"RECONCILE_WEBHOOK_GCHAT_WEBHOOK_TOKEN" defaultValue:""`
	ReconcileWebhookWithEventJobExpression               string        `envName:"RECONCILE_WEBHOOK_WITH_EVENT" defaultValue:"0 */6 * * *"`
	RefireWebhookDeleteIntervalInDays                    int           `envName:"REFIRE_WEBHOOK_DELETE_INTERVAL_IN_DAYS" defaultValue:"60"`
	RefireWebhookRetryDurationInMinutes                  int           `envName:"REFIRE_WEBHOOK_RETRY_DURATION_IN_MINUTES" defaultValue:"5"`
	RetryProducerJobExpression                           string        `envName:"RETRY_PRODUCER_JOB_EXPRESSION" defaultValue:"*/5 * * * *"`
	ReturnLabelURLSignedTtl                              time.Duration `envName:"RETURN_LABEL_URL_SIGNED_TTL" defaultValue:"360m"`
	MMCCLMServiceCodeSuffix                              string        `envName:"MMCC_LM_SERVICE_CODE_SUFFIX" defaultValue:"NJV"`
	SfExpressPartnerId                                   uint64        `envName:"SF_EXPRESS_PARTNER_ID" defaultValue:"163"`
	SheinParcelValidInterval                             string        `envName:"PARTNER_26_PARCEL_VALID_INTERVAL" defaultValue:"120"`
	SheinPartnerId                                       uint64        `envName:"SHEIN_PARTNER_ID" defaultValue:"26"`
	ShipmentDocumentSignedTtlInMinutes                   int           `envName:"SHIPMENT_DOCUMENT_SIGNED_TTL_IN_MINUTES" defaultValue:"360"`
	ThgEventExportJobExpression                          string        `envName:"THG_EVENT_EXPORT_JOB_EXPRESSION" defaultValue:"0/15 * * * *"`
	TiktokBaseUrl                                        string        `envName:"TIKTOK_BASE_URL" defaultValue:"https://f-p-sandbox.snssdk.com"`
	TiktokCallbackTimeoutInSeconds                       int           `envName:"TIKTOK_CALLBACK_TIMEOUT_IN_SECONDS" defaultValue:"3"`
	TiktokFplAppKeyMmccMy                                string        `envName:"TIKTOK_FPL_APP_KEY_MMCC_MY" defaultValue:"key"`
	TiktokFplAppKeyMmccPh                                string        `envName:"TIKTOK_FPL_APP_KEY_MMCC_PH" defaultValue:"key"`
	TiktokFplAppKeyMmccSg                                string        `envName:"TIKTOK_FPL_APP_KEY_MMCC_SG" defaultValue:"key"`
	TiktokFplAppKeyMmccTh                                string        `envName:"TIKTOK_FPL_APP_KEY_MMCC_TH" defaultValue:"key"`
	TiktokFplAppKeyMmccVn                                string        `envName:"TIKTOK_FPL_APP_KEY_MMCC_VN" defaultValue:"key"`
	TiktokFplAppKeyMy                                    string        `envName:"TIKTOK_FPL_APP_KEY_MY" defaultValue:"key"`
	TiktokFplAppKeyPh                                    string        `envName:"TIKTOK_FPL_APP_KEY_PH" defaultValue:"key"`
	TiktokFplAppKeySg                                    string        `envName:"TIKTOK_FPL_APP_KEY_SG" defaultValue:"key"`
	TiktokFplAppKeyTh                                    string        `envName:"TIKTOK_FPL_APP_KEY_TH" defaultValue:"key"`
	TiktokFplAppKeyVn                                    string        `envName:"TIKTOK_FPL_APP_KEY_VN" defaultValue:"key"`
	TiktokFplAppSecretMmccMy                             string        `envName:"TIKTOK_FPL_APP_SECRET_MMCC_MY" defaultValue:"secret"`
	TiktokFplAppSecretMmccPh                             string        `envName:"TIKTOK_FPL_APP_SECRET_MMCC_PH" defaultValue:"secret"`
	TiktokFplAppSecretMmccSg                             string        `envName:"TIKTOK_FPL_APP_SECRET_MMCC_SG" defaultValue:"secret"`
	TiktokFplAppSecretMmccTh                             string        `envName:"TIKTOK_FPL_APP_SECRET_MMCC_TH" defaultValue:"secret"`
	TiktokFplAppSecretMmccVn                             string        `envName:"TIKTOK_FPL_APP_SECRET_MMCC_VN" defaultValue:"secret"`
	TiktokFplAppSecretMy                                 string        `envName:"TIKTOK_FPL_APP_SECRET_MY" defaultValue:"secret"`
	TiktokFplAppSecretPh                                 string        `envName:"TIKTOK_FPL_APP_SECRET_PH" defaultValue:"secret"`
	TiktokFplAppSecretSg                                 string        `envName:"TIKTOK_FPL_APP_SECRET_SG" defaultValue:"secret"`
	TiktokFplAppSecretTh                                 string        `envName:"TIKTOK_FPL_APP_SECRET_TH" defaultValue:"secret"`
	TiktokFplAppSecretVn                                 string        `envName:"TIKTOK_FPL_APP_SECRET_VN" defaultValue:"secret"`
	TiktokFplMmccVersion                                 string        `envName:"TIKTOK_FPL_MMCC_VERSION" defaultValue:"3.0"`
	TiktokFplVersion                                     string        `envName:"TIKTOK_FPL_VERSION" defaultValue:"2.0"`
	Tiktok                                               struct {      // TODO move the rest of TT configs here
		PartnerId uint64 `envName:"TIKTOK_PARTNER_ID" defaultValue:"67"`
		MMCCEvent struct {
			LSArtificialDelay    time.Duration `envName:"TIKTOK_LINEHAUL_SCHEDULED_ARTIFICIAL_DELAY" defaultValue:"50s"`
			LSBagArtificialDelay time.Duration `envName:"TIKTOK_LINEHAUL_SCHEDULED_BAG_ARTIFICIAL_DELAY" defaultValue:"1m"`
		}
	}
	TtrackApiChannel                string `envName:"TTRACK_API_CHANNEL" defaultValue:""`
	TtrackApiKey                    string `envName:"TTRACK_API_KEY" defaultValue:""`
	TtrackBaseUrl                   string `envName:"TTRACK_BASE_URL" defaultValue:""`
	UnifiedShipperCacheTtlInSeconds int    `envName:"UNIFIED_SHIPPER_CACHE_TTL_IN_SECONDS" defaultValue:"900"`
	VipshopAppKey                   string `envName:"VIPSHOP_APP_KEY" defaultValue:""`
	VipshopAppSecret                string `envName:"VIPSHOP_APP_SECRET" defaultValue:""`
	VipshopPartnerId                uint64 `envName:"VIPSHOP_PARTNER_ID" defaultValue:"341"`
	VipshopWebhookEndpoint          string `envName:"VIPSHOP_WEBHOOK_ENDPOINT" defaultValue:"http://sandbox.vipapis.com"`
	WishApiKey                      string `envName:"WISH_API_KEY" defaultValue:""`
	WishFplApiKey                   string `envName:"WISH_FPL_API_KEY" defaultValue:""`
	WishNvCarrierCode               int    `envName:"WISH_NV_CARRIER_CODE" defaultValue:"0"`
	WishPartnerId                   uint64 `envName:"WISH_PARTNER_ID" defaultValue:"3"`
	XbShipperCacheTtlInMinutes      int    `envName:"XB_SHIPPER_CACHE_TTL_IN_MINUTES" defaultValue:"15"`
	XbShipperCacheTtlInSeconds      int    `envName:"XB_SHIPPER_CACHE_TTL_IN_SECONDS" defaultValue:"0"`
	NvBypassAuthValidation          bool   `envName:"NV_BYPASS_AUTH_VALIDATION" defaultValue:"false"`
	Waybill                         struct {
	}
	Runtime struct {
		WebAppPort                     string `envName:"NV_3PL_DEFAULT_PORT" defaultValue:"9000"`
		EnableCronJobs                 bool   `envName:"FPL_ENABLE_CRON_JOBS" defaultValue:"true"`
		DbDebug                        bool   `envName:"DB_DEBUG" defaultValue:"false"`
		ConsumerEnable                 bool   `envName:"NV_KAFKA_CONSUMER_ENABLE" defaultValue:"false"`
		CronJobCleanUpTimeoutInSeconds int    `envName:"CLEAN_UP_TIMEOUT_IN_SECONDS" defaultValue:"60"`
		CleanUpWaitTimeInSeconds       int    `envName:"APP_CLEAN_UP_WAIT_TIME_IN_SECONDS" defaultValue:"10"`
	}
	Lazada struct {
		PartnerId    uint64 `envName:"LAZADA_PARTNER_ID" defaultValue:"47052"`
		AppSecret    string `envName:"LAZADA_APP_SECRET" defaultValue:""`
		AppKey       string `envName:"LAZADA_APP_KEY" defaultValue:""`       // not used yet
		ResourceCode string `envName:"LAZADA_RESOURCE_CODE" defaultValue:""` // not used yet
	}
	LazadaMM struct {
		PartnerId uint64 `envName:"LAZADA_MM_PARTNER_ID" defaultValue:"88426"`
		AppSecret string `envName:"LAZADA_MM_APP_SECRET" defaultValue:"njv&123"`
	}
	LazadaMMAIDC struct {
		PartnerId uint64 `envName:"LAZADA_MM_AIDC_PARTNER_ID" defaultValue:"90299"`
	}
	LazadaCC struct {
		PartnerId uint64 `envName:"LAZADA_CC_PARTNER_ID" defaultValue:"88426"`
		AppKey    string `envName:"LAZADA_CC_APP_KEY" defaultValue:""`
		AppSecret string `envName:"LAZADA_CC_APP_SECRET" defaultValue:"njv&123"`
	}
	Shopee struct {
		PartnerId uint64 `envName:"SHOPEE_PARTNER_ID" defaultValue:"55478"`
		AppKey    string `envName:"SHOPEE_APP_KEY" defaultValue:"temp-shopee-app-key"`
		AppSecret string `envName:"SHOPEE_APP_SECRET" defaultValue:""`
	}
	AliExpress struct {
		PartnerId uint64 `envName:"ALIEXPRESS_PARTNER_ID" defaultValue:"89089"`
	}
	AsyncTask      AsyncTasks
	LocalDevConfig struct {
		EnabledKafkaTopics     string `envName:"ENABLED_KAFKA_TOPICS" defaultValue:"all"`
		IgnoredKafkaTopics     string `envName:"DISABLED_KAFKA_TOPICS" defaultValue:""`
		EnabledCronJobNames    string `envName:"ENABLED_CRON_JOB_NAMES" defaultValue:"all"`
		IgnoredCronJobNames    string `envName:"DISABLED_CRON_JOB_NAMES" defaultValue:""`
		ExecuteRunningCronJobs bool   `envName:"EXECUTE_RUNNING_CRON_JOBS" defaultValue:"false"`
	}

	EnableSynchronousPublisher bool `envName:"ENABLE_SYNCHRONOUS_PUBLISHER" defaultValue:"false"`

	ShipmentPostManifest struct {
		TrackingIDsPerGetCODRequest int `envName:"SHIPMENT_POST_MANIFEST_TIDS_PER_GET_COD_REQUEST" defaultValue:"100"`
		NumberOfGetCODWorkers       int `envName:"SHIPMENT_POST_MANIFEST_NUMBER_OF_GET_COD_WORKERS" defaultValue:"10"`
	}

	RateLimit struct {
		GetOrderEventsQPM int64 `envName:"GET_ORDER_EVENTS_QPM" defaultValue:"100"`
	}

	DBChunkSize struct {
		BulkUpdateParcelInBulkUpsertEvent int `envName:"BULK_UPDATE_PARCEL_CHUNK_SZIE_IN_BULK_UPSERT_EVENT" defaultValue:"1000"`
	}
}
