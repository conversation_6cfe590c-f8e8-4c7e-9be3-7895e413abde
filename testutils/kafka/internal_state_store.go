package kafka

import (
	"context"

	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
	"github.com/rs/zerolog"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"bitbucket.ninjavan.co/fpl/4pl-protos/go/fpl"

	"git.ninjavan.co/3pl/envs"
	dbtestutils "git.ninjavan.co/3pl/testutils/db"
	"git.ninjavan.co/3pl/utils"
)

type internalStateStoreProcessor struct {
	consumedMessageRepo dbtestutils.ConsumedMessageRepository
	logger              zerolog.Logger
}

func NewInternalStateStoreProcessor() *internalStateStoreProcessor {
	return &internalStateStoreProcessor{
		consumedMessageRepo: dbtestutils.NewConsumedMessageMySQL(),
		logger:              utils.NewLogger("internal-state-store-processor"),
	}
}

func (o *internalStateStoreProcessor) Process(msg *kafka.Message) {
	var protoMsg proto.Message
	topic := *msg.TopicPartition.Topic
	switch topic {
	case envs.Instance.KafkaTopics.FplParcelEventTopic:
		protoMsg = &fpl.FPLParcelEvent{}
	case envs.Instance.KafkaTopics.FplBagEventTopic:
		protoMsg = &fpl.FPLBagEvent{}
	case envs.Instance.KafkaTopics.FplMmccParcelEventTopic:
		protoMsg = &fpl.FPLParcelEvent{}
	}

	if err := proto.Unmarshal(msg.Value, protoMsg); err != nil {
		o.logger.Error().Err(err).Msg("failed-to-unmarshal-consumed-msg")
		return
	}

	jsonBytes, err := protojson.Marshal(protoMsg)
	if err != nil {
		o.logger.Error().Err(err).Msg("failed-to-marshal-consumed-msg-to-json-format")
		return
	}
	ctx := context.Background()
	if err := o.consumedMessageRepo.Create(ctx, &dbtestutils.ConsumedMessage{
		Topic:   topic,
		Message: string(jsonBytes),
	}); err != nil {
		o.logger.Error().Err(err).Msg("failed-to-save-consumed-msg")
	}
}
