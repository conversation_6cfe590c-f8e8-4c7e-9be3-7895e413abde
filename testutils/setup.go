package testutils

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"github.com/rs/zerolog"
	"github.com/stretchr/testify/suite"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/kafka"
	tcnetwork "github.com/testcontainers/testcontainers-go/network"
	"github.com/volatiletech/sqlboiler/v4/boil"

	commonsCfg "bitbucket.ninjavan.co/cg/base-commons---go/config"
	"bitbucket.ninjavan.co/cg/datadog-agent---go/nvsql"
	"bitbucket.ninjavan.co/cg/db-commons---go/sql"
	kafkaCommonConsumer "bitbucket.ninjavan.co/cg/kafka-commons---go/v3/consumer"

	"git.ninjavan.co/3pl/cache"
	internalconsumer "git.ninjavan.co/3pl/consumers"
	"git.ninjavan.co/3pl/envs"
	"git.ninjavan.co/3pl/event_publisher"
	"git.ninjavan.co/3pl/services"
	"git.ninjavan.co/3pl/testutils/container"
	kafkaTestUtils "git.ninjavan.co/3pl/testutils/kafka"
	"git.ninjavan.co/3pl/utils"
)

type BaseIntegration struct {
	ctx context.Context
	suite.Suite
	containers []testcontainers.Container
	cleanUpFns []func()
}

func (base *BaseIntegration) SetupSuite() {
	ctx := context.Background()

	containers := SetupContainers(
		ctx,
		WithMysqlOption(),
		WithWiremockOption(),
		WithKafkaOption(),
		WithRedisOption(),
	)
	base.ctx = ctx
	base.containers = containers

	commonsConfig := commonsCfg.New()
	cache.Init(commonsConfig)

	flag.Parse()

	dbEnv := sql.Config(commonsConfig)
	database := nvsql.Open(
		dbEnv.DBUser, dbEnv.DBPassword, dbEnv.DBHost,
		dbEnv.DBName, dbEnv.DBTLSVersion,
		dbEnv.DBPort, dbEnv.DBMaxConnections,
	)
	boil.SetDB(database)

	cleanUpFns := make([]func(), 0)

	cleanUpFns = append(cleanUpFns, func() {
		sql.Close(database)
	})

	zerolog.DefaultContextLogger = &utils.Logger

	CleanupAndInitDB()

	services.SetupHttpClients(commonsConfig)

	nvProducer := event_publisher.SetupProducer(commonsConfig)
	go event_publisher.ReportFunc()(nvProducer)
	closeProducer := func() {
		if err := nvProducer.Close(); err != nil {
			log.Fatalf("Error closing producer: %v", err)
		}
	}
	cleanUpFns = append(cleanUpFns, closeProducer)

	topology := kafkaCommonConsumer.NewConsumerMap()

	// Add all topic need integration test here
	nvConsumer := setupConsumers(topology, commonsConfig)
	if nvConsumer != nil {
		cleanUpFns = append(cleanUpFns, nvConsumer.Close)
	}

	c, err := kafkaCommonConsumer.New(ctx, commonsConfig, topology)
	if err != nil {
		log.Fatalf("Error creating consumer with topics: %v, err: %v", topology.GetTopics(), err)
	}
	cleanUpFns = append(cleanUpFns, c.Close)
	base.cleanUpFns = cleanUpFns

	// waiting for the consumer group to rebalance first
	time.Sleep(5 * time.Second)
}

func setupConsumers(topology kafkaCommonConsumer.ConsumerMap, cfg commonsCfg.Config) *kafkaCommonConsumer.NVConsumer {
	// add consumer for create e2e events flow
	topology.AddTopic(
		envs.Instance.KafkaTopics.FplBulkEventsUpsertedTopic,
		internalconsumer.NewFPLBulkEventsUpsertedConsumer(envs.Instance.KafkaTopics.FplBulkEventsUpsertedHandlingRetryTopic),
	)

	// Check message publish to FplParcelEventTopic
	topology.AddTopic(
		envs.Instance.KafkaTopics.FplParcelEventTopic,
		kafkaTestUtils.NewInternalStateStoreProcessor(),
	)

	// add consumer for create mmcc events flow
	topology.AddTopic(
		envs.Instance.KafkaTopics.FplMmccEventsTopic,
		internalconsumer.NewFplMMCCEventConsumer(envs.Instance.KafkaTopics.FplMmccEventsRetryTopic),
	)

	// Check message publish to FplBagEventTopic
	topology.AddTopic(
		envs.Instance.KafkaTopics.FplBagEventTopic,
		kafkaTestUtils.NewInternalStateStoreProcessor(),
	)

	// Check message publish to FplMmccParcelEventTopic
	topology.AddTopic(
		envs.Instance.KafkaTopics.FplMmccParcelEventTopic,
		kafkaTestUtils.NewInternalStateStoreProcessor(),
	)

	c, err := kafkaCommonConsumer.New(context.Background(), cfg, topology)
	if err != nil {
		utils.Logger.Panic().Err(err).Msgf("Error creating consumer with topics: %v", topology.GetTopics())
	}
	return c
}

func (base *BaseIntegration) TearDownSuite() {
	// Send SIGINT signal
	_ = syscall.Kill(os.Getpid(), syscall.SIGINT)

	defer func() {
		for _, c := range base.containers {
			if err := c.Terminate(base.ctx); err != nil {
				log.Printf("failed to terminate container: %v", err)
			}
		}
	}()

	for _, fn := range base.cleanUpFns {
		defer fn()
	}
}

type Option interface {
	Apply(ctx context.Context, network string, providerType testcontainers.ProviderType) testcontainers.Container
}

type RedisOption struct{}

func WithRedisOption() Option {
	return &RedisOption{}
}

func (o *RedisOption) Apply(ctx context.Context, network string, providerType testcontainers.ProviderType) testcontainers.Container {
	redisContainer := container.CreateRedisContainer(ctx, network, providerType)

	redisHost := container.GetHost(ctx, redisContainer)
	redisPort := container.GetPort(ctx, redisContainer, container.RedisTcpPort)

	os.Setenv("NV_REDIS_COMMON_ENABLE", "true")
	os.Setenv("NV_REDIS_HOST", fmt.Sprintf("%s:%s", redisHost, redisPort))

	return redisContainer
}

type MysqlOption struct{}

func WithMysqlOption() Option {
	return &MysqlOption{}
}

func (o *MysqlOption) Apply(ctx context.Context, network string, providerType testcontainers.ProviderType) testcontainers.Container {
	mysqlContainer := container.CreateMysqlContainer(ctx, network, providerType)

	mysqlHost := container.GetHost(ctx, mysqlContainer)
	mysqlPort := container.GetPort(ctx, mysqlContainer, container.MysqlTcpPort)

	os.Setenv("DB_USER", container.MysqlRootUser)
	os.Setenv("DB_PASSWORD", container.MysqlPassword)
	os.Setenv("DB_HOST", mysqlHost)
	os.Setenv("DB_SLAVE_HOST", mysqlHost)
	os.Setenv("DB_PORT", mysqlPort)
	os.Setenv("DB_NAME", container.MysqlSchemaName)
	os.Setenv("NV_DB_MAX_CONNS", "10")

	envs.Instance.Tiktok.PartnerId = 6586
	envs.Instance.Tiktok.MMCCEvent.LSBagArtificialDelay = time.Second * 1

	return mysqlContainer
}

type WiremockOption struct{}

func WithWiremockOption() Option {
	return &WiremockOption{}
}

func (o *WiremockOption) Apply(ctx context.Context, network string, providerType testcontainers.ProviderType) testcontainers.Container {
	wiremockContainer := container.CreateWiremockContainer(ctx, network, providerType)

	wiremockHost := container.GetHost(ctx, wiremockContainer)
	wiremockPort := container.GetPort(ctx, wiremockContainer, container.WiremockTcpPort)

	combinedHostPort := fmt.Sprintf("http://%s:%s", wiremockHost, wiremockPort)
	envs.Instance.NvInternalHttpUriOrderCreate = combinedHostPort + "/%2s/"
	os.Setenv("NV_AUTH_API_URL", combinedHostPort)
	os.Setenv("NV_AUTH_SERVICE_ID", "your-service-id")
	os.Setenv("NV_AUTH_SERVICE_SECRET", "your-service-secret")

	return wiremockContainer
}

type KafkaOption struct{}

func WithKafkaOption() Option {
	return &KafkaOption{}
}

func (o *KafkaOption) Apply(ctx context.Context, network string, providerType testcontainers.ProviderType) testcontainers.Container {
	containerInstance := container.CreatekafkaContainer(ctx, network, providerType)
	kafkaContainer, ok := containerInstance.(*kafka.KafkaContainer)
	if !ok {
		return nil
	}

	// set topic var
	envs.Instance.KafkaTopics.FplParcelCreationProtoV2Topic = "local-4pl-parcel-creation-v2-topic"
	envs.Instance.KafkaTopics.FplBulkEventsUpsertedTopic = "local-4pl-evt-bulk-events-upserted"
	envs.Instance.KafkaTopics.FplParcelEventTopic = "local-4pl-parcel-event-topic"
	envs.Instance.KafkaTopics.FplMmccEventsTopic = "local-4pl-evt-mmcc"
	envs.Instance.KafkaTopics.FplBagEventTopic = "local-4pl-bag-event-topic"
	envs.Instance.KafkaTopics.FplMmccParcelEventTopic = "local-4pl-mmcc-parcel-event-topic"

	// Create predefined topics
	topics := []string{
		envs.Instance.KafkaTopics.FplBulkEventsUpsertedTopic,
		envs.Instance.KafkaTopics.FplParcelEventTopic,
		envs.Instance.KafkaTopics.FplMmccEventsTopic,
		envs.Instance.KafkaTopics.FplBagEventTopic,
		envs.Instance.KafkaTopics.FplMmccParcelEventTopic,
	}
	for _, topic := range topics {
		cmd := []string{
			"kafka-topics",
			"--create",
			"--if-not-exists",
			"--bootstrap-server", "localhost:9092",
			"--topic", topic,
			"--partitions", "1",
			"--replication-factor", "1",
		}

		exitCode, _, err := kafkaContainer.Exec(ctx, cmd)
		if err != nil || exitCode != 0 {
			log.Fatalf("failed to create topic %s: exit code %d, error: %v", topic, exitCode, err)
		}
	}

	brokers, err := kafkaContainer.Brokers(ctx)
	if err != nil {
		log.Fatalf("failed to get kafka brokers: %v", err)
	}
	host := strings.Join(brokers, ",")

	os.Setenv("NV_KAFKA_SERVERS", host)
	os.Setenv("NV_KAFKA_GROUP_ID", "internal-state-store")
	os.Setenv("NV_KAFKA_AUTH_BROKERS", host)
	os.Setenv("NV_KAFKA_PRODUCER_ENABLE", "true")
	os.Setenv("NV_KAFKA_CONSUMER_ENABLE", "true")
	os.Setenv("NV_KAFKA_STREAMS_ENABLE_CONSUMER", "true")
	os.Setenv("KAFKA_AUTO_CREATE_TOPICS_ENABLE", "true")

	// tmp set here, TODO move to test case when use
	os.Setenv("NV_KAFKA_RETRY_00H01M_TOPIC", "NV_KAFKA_RETRY_00H01M_TOPIC")
	os.Setenv("NV_KAFKA_RETRY_00H05M_TOPIC", "NV_KAFKA_RETRY_00H05M_TOPIC")
	os.Setenv("NV_KAFKA_RETRY_00H30M_TOPIC", "NV_KAFKA_RETRY_00H30M_TOPIC")
	os.Setenv("NV_KAFKA_RETRY_00H60M_TOPIC", "NV_KAFKA_RETRY_00H60M_TOPIC")

	return kafkaContainer
}

func SetupContainers(ctx context.Context, opts ...Option) []testcontainers.Container {
	tcs := make([]testcontainers.Container, 0)
	providerType := container.GetProviderType()
	if providerType == testcontainers.ProviderPodman {
		container.PodmanRemoveExitedContainers()
		os.Setenv("TESTCONTAINERS_RYUK_DISABLED", "true")
	}

	network, err := tcnetwork.New(ctx)
	if err != nil {
		log.Fatalf("failed to start docker network: %s", err)
	}

	for _, opt := range opts {
		tc := opt.Apply(ctx, network.Name, providerType)
		tcs = append(tcs, tc)
	}

	return tcs
}

func CleanupAndInitDB() {
	fmt.Println("Starting database cleanup and initialization...")

	sqlFolderPath, err := getSQLFolderPath()
	if err != nil {
		log.Fatalf("Failed to locate SQL folder: %v", err)
	}

	sqlFiles, err := listSQLFiles(sqlFolderPath)
	if err != nil {
		log.Fatalf("Failed to list SQL files: %v", err)
	}

	for _, filePath := range sqlFiles {
		if err := executeQueriesFromFile(filePath); err != nil {
			log.Fatalf("Failed to execute queries from %s: %v", filePath, err)
		}
	}

	fmt.Println("Database cleanup and initialization completed successfully.")
}

func getSQLFolderPath() (string, error) {
	rootPath, err := container.FindProjectRoot()
	if err != nil {
		return "", fmt.Errorf("finding project root: %w", err)
	}
	return filepath.Join(rootPath, "resources", "db", "init_test_db"), nil
}

func listSQLFiles(folderPath string) ([]string, error) {
	files, err := os.ReadDir(folderPath)
	if err != nil {
		return nil, fmt.Errorf("reading directory: %w", err)
	}

	var sqlFiles []string
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".sql") {
			sqlFiles = append(sqlFiles, filepath.Join(folderPath, file.Name()))
		}
	}
	return sqlFiles, nil
}

func executeQueriesFromFile(filePath string) error {
	sqlBytes, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("reading SQL file: %w", err)
	}

	sqlContent := string(sqlBytes)
	sqlQueries := strings.Split(sqlContent, ";")

	for _, query := range sqlQueries {
		query = strings.TrimSpace(query)
		if query == "" {
			continue
		}

		if _, err := boil.GetDB().Exec(query + ";"); err != nil {
			return fmt.Errorf("executing query from %s: %w", filePath, err)
		}
	}

	return nil
}
