package order

// Contracts for service API endpoint

import (
	"encoding/json"
	"time"

	"github.com/volatiletech/null/v8"

	orderProto "bitbucket.ninjavan.co/order/order-protos/go/order/events"
	"bitbucket.ninjavan.co/protos/proto-commons/go/common"

	"git.ninjavan.co/3pl/configs/parcel"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/utils"
)

type Timeslot struct {
	StartTime string `json:"start_time" binding:"required"`
	EndTime   string `json:"end_time" binding:"required"`
	Timezone  string `json:"timezone" binding:"required"`
}

type Pickup struct {
	Date         string    `json:"pickup_date" binding:"required,date_iso8601"`
	Timeslot     *Timeslot `json:"pickup_timeslot" binding:"required"`
	Address      *Address  `json:"pickup_address" binding:"omitempty"`
	ApproxVolume *uint8    `json:"pickup_approx_volume" binding:"omitempty,oneof=0 1 2 3 4 5"`
	Instructions string    `json:"pickup_instructions" binding:"omitempty,max=255"`
}

type Delivery struct {
	StartDate              string    `json:"delivery_start_date" binding:"omitempty"`
	Timeslot               *Timeslot `json:"delivery_timeslot" binding:"omitempty"`
	Instructions           string    `json:"delivery_instructions" binding:"omitempty,max=1000"`
	CashOnDelivery         float64   `json:"cash_on_delivery" binding:"omitempty,min=0"`
	CashOnDeliveryCurrency string    `json:"cash_on_delivery_currency,omitempty" binding:"omitempty"`
	InsuredValue           float64   `json:"insured_value" binding:"omitempty,min=0"`
	InsuredValueCurrency   string    `json:"insured_value_currency,omitempty" binding:"omitempty"`
	AllowSelfCollection    bool      `json:"allow_self_collection" binding:"omitempty"`
}

type Address struct {
	Name            string  `json:"name" binding:"required,min=1,max=255"`
	AddressLine1    string  `json:"address_line1" binding:"required,max=255"`
	AddressLine2    *string `json:"address_line2" binding:"omitempty,max=255"`
	AddressLine3    *string `json:"address_line3" binding:"omitempty,max=255"`
	AddressLine4    *string `json:"address_line4" binding:"omitempty,max=255"`
	City            *string `json:"city" binding:"omitempty,min=3,max=255"`
	StateProvince   *string `json:"state_province" binding:"omitempty,min=3,max=255"`
	CountryCode     string  `json:"country_code" binding:"required,iso3166_1_alpha2"`
	PostCode        *string `json:"post_code" binding:"required_if=CountryCode SG,required_if=CountryCode MY,required_if=CountryCode TH,omitempty,numeric"`
	ContactNumber   *string `json:"contact_number" binding:"required,min=6,max=32"`
	ContactEmail    *string `json:"contact_email" binding:"required,email,max=255"`
	CollectionPoint *string `json:"collection_point" binding:"omitempty,max=1000"`
}

type ParcelItem struct {
	Description                 *string            `json:"description" binding:"required,max=1000"`
	NativeDescription           *string            `json:"native_description" binding:"omitempty,max=1000"`
	UnitValue                   *float64           `json:"unit_value" binding:"required_if=CoreConsumedRelabel false,omitempty,min=0"`
	Quantity                    *int               `json:"quantity" binding:"omitempty,min=1"`
	HSCode                      *uint              `json:"hs_code" binding:"omitempty,hs_code"`
	Url                         *string            `json:"url" binding:"omitempty,url"`
	InvoiceURL                  *string            `json:"invoice_url" binding:"omitempty,url"`
	OriginCountry               *string            `json:"origin_country" binding:"omitempty,iso3166_1_alpha2"`
	UnitWeight                  *float64           `json:"unit_weight" binding:"required,min=0"`
	IsGstIncludedInGoodsValue   bool               `json:"is_gst_included_in_goods_value"`
	GstRegistrationNumber       *string            `json:"gst_registration_number" binding:"omitempty,regexp=alphanumeric,max=255"`
	SKUId                       *string            `json:"sku_id" binding:"omitempty,max=255"`
	Taxes                       TaxesMap           `json:"taxes" binding:"omitempty,dive,dive"`
	CoreConsumedRelabel         bool               `json:"-"`
	DescriptionTranslated       *string            `json:"description_translated" binding:"omitempty,max=2000"`
	NativeDescriptionTranslated *string            `json:"native_description_translated" binding:"omitempty,max=2000"`
	CodFee                      *string            `json:"cod_fee" binding:"omitempty"`
	GoodsCurrency               *string            `json:"goods_currency" binding:"omitempty"`
	Dimensions                  *models.Dimensions `json:"dimensions" binding:"omitempty"`
	SupplierId                  *string            `json:"supplier_id" binding:"omitempty"`
	ImageURL                    *string            `json:"image_url" binding:"omitempty"`
	BoxNo                       *string            `json:"box_no" binding:"omitempty"`
	BoxVolume                   *string            `json:"box_volume" binding:"omitempty"`
	Brand                       *string            `json:"brand" binding:"omitempty"`
	Material                    *string            `json:"material" binding:"omitempty"`
	Color                       *string            `json:"color" binding:"omitempty,max=20"`
}

type CorporateShipper struct {
	BranchID                 string `json:"-"`
	BranchGlobalShipperID    uint   `json:"-"`
	CorporateGlobalShipperID uint   `json:"-"`
}

type TaxesMap map[TaxName]TaxInfo

type TaxName string

type TaxInfo struct {
	Number     *string `json:"number" binding:"omitempty,regexp=alphanumeric,max=255"`
	IsIncluded *bool   `json:"is_included"`
}

type ParcelItemSlice []*ParcelItem

func (o *ParcelItem) ToString() string {
	b, _ := json.Marshal(o)
	return string(b)
}

func (o *ParcelItem) setDefaults() {
	if o.Quantity == nil {
		o.Quantity = null.IntFrom(1).Ptr()
	}
}

func (items ParcelItemSlice) ToParcelItemSliceModel(parcelID uint) models.ParcelItemSlice {
	parcelItems := make(models.ParcelItemSlice, len(items))
	for i, item := range items {
		modelItem := &models.ParcelItem{
			ID:       0,
			ParcelID: uint64(parcelID),
			Metadata: item.ToString(),
		}
		parcelItems[i] = modelItem
	}
	return parcelItems
}

type Source uint16

const (
	SourceUnknown Source = iota
	SourceShipmentModule
	SourceCoreConsumer
	SourceRegularAPI
	SourceCustomAPI
	SourceInternalAPI
	SourceDeprecated1
	SourceDashKeyBoard
	SourceDashCSV
	SourceEPI
	SourceCoreConsumerV2
)

type BaseRequest struct {
	Source              Source                `json:"source" binding:"omitempty"`
	SourceOrderID       *string               `json:"source_order_id" binding:"omitempty,max=255"`
	RefTrackingID       *string               `json:"source_reference_id" binding:"omitempty,max=255"`
	Type                *string               `json:"type" binding:"omitempty,parcel_type"`
	GlobalShipperID     uint                  `json:"shipper_id" binding:"required"`
	RequestedTrackingID string                `json:"requested_tracking_id" binding:"omitempty,min=1,max=29"`
	ServiceCode         string                `json:"service_code" binding:"required,max=255"`
	From                Address               `json:"from" binding:"required"`
	To                  Address               `json:"to" binding:"required"`
	Return              *Address              `json:"address" binding:"omitempty"`
	Pickup              *Pickup               `json:"pickup" binding:"omitempty"`
	Delivery            *Delivery             `json:"delivery" binding:"omitempty"`
	ParcelDetails       *models.ParcelDetails `json:"parcel_details" binding:"omitempty"`
	Items               ParcelItemSlice       `json:"items" binding:"omitempty"`

	// fields inferred from the request itself or its context
	DocumentIDs           []uint            `json:"document_ids" binding:"omitempty"`
	IsMMCCB2C             bool              `json:"is_mmcc_b2c" binding:"omitempty"`
	OriginGlobalShipperID *uint             `json:"origin_global_shipper_id" binding:"omitempty"`
	Corporate             *CorporateShipper `json:"corporate" binding:"omitempty"`
	CustomsDetail         string            `json:"customs_detail" binding:"omitempty"` // to save vendor order
	RequestId             string            `json:"request_id" binding:"omitempty"`
	ForceGlobalShipperId  *uint             `json:"force_global_shipper_id" binding:"omitempty"`
	PartnerUniqueKey      *string           `json:"partner_unique_key" binding:"omitempty"`
}

func (r BaseRequest) IsCorporate() bool {
	_, ok := parcel.CorporateTypes[null.StringFromPtr(r.Type).String]
	return ok
}

func (r BaseRequest) IsRelabel() bool {
	_, ok := parcel.RelabelTypes[null.StringFromPtr(r.Type).String]
	return ok
}

func (r BaseRequest) IsReturn() bool {
	if r.Type != nil && *r.Type == parcel.Return.String() {
		return true
	}
	return false
}

func (r BaseRequest) IsInternationalReturn() bool {
	if r.Type != nil && (*r.Type == parcel.InternationalReturn.String() || *r.Type == parcel.CorporateInternationalReturn.String()) {
		return true
	}
	return false
}

func (r BaseRequest) ToParcel(partnerID uint64) *models.Parcel {
	p := &models.Parcel{
		Source:        uint16(r.Source),
		SourceOrderID: null.StringFromPtr(r.SourceOrderID),
		RefTrackingID: null.StringFromPtr(r.RefTrackingID),

		FromName:          null.StringFrom(r.From.Name),
		FromAddressLine1:  null.StringFrom(r.From.AddressLine1),
		FromAddressLine2:  null.StringFromPtr(r.From.AddressLine2),
		FromAddressLine3:  null.StringFromPtr(r.From.AddressLine3),
		FromAddressLine4:  null.StringFromPtr(r.From.AddressLine4),
		FromCity:          null.StringFromPtr(r.From.City),
		FromCountryCode:   null.StringFrom(r.From.CountryCode),
		FromStateProvince: null.StringFromPtr(r.From.StateProvince),
		FromPostcode:      null.StringFromPtr(r.From.PostCode),
		FromContactNumber: null.StringFromPtr(r.From.ContactNumber),
		FromContactEmail:  null.StringFromPtr(r.From.ContactEmail),

		ToName:          null.StringFrom(r.To.Name),
		ToAddressLine1:  null.StringFrom(r.To.AddressLine1),
		ToAddressLine2:  null.StringFromPtr(r.To.AddressLine2),
		ToAddressLine3:  null.StringFromPtr(r.To.AddressLine3),
		ToAddressLine4:  null.StringFromPtr(r.To.AddressLine4),
		ToCity:          null.StringFromPtr(r.To.City),
		ToCountryCode:   null.StringFrom(r.To.CountryCode),
		ToStateProvince: null.StringFromPtr(r.To.StateProvince),
		ToPostcode:      null.StringFromPtr(r.To.PostCode),
		ToContactNumber: null.StringFromPtr(r.To.ContactNumber),
		ToContactEmail:  null.StringFromPtr(r.To.ContactEmail),

		PartnerUniqueKey: null.StringFromPtr(r.PartnerUniqueKey),
	}

	if r.ParcelDetails != nil {
		r.ParcelDetails.RequestedTrackingID = r.RequestedTrackingID
		bMetadata, _ := json.Marshal(r.ParcelDetails)
		p.Metadata = null.StringFrom(string(bMetadata))
	}

	if r.Return != nil {
		bReturn, _ := json.Marshal(r.Return)
		p.ReturnAddress = null.StringFrom(string(bReturn))
	}

	if r.Delivery != nil {
		bDelivery, _ := json.Marshal(r.Delivery)
		p.DeliveryInfo = null.StringFrom(string(bDelivery))
	}

	if r.Pickup != nil {
		bPickup, _ := json.Marshal(r.Pickup)
		p.PickupInfo = null.StringFrom(string(bPickup))
	}

	if partnerID > 0 {
		p.PartnerID = null.Uint64From(partnerID)
	}

	return p
}

func (r BaseRequest) ToParcelItems() []*models.ParcelItem {
	var parcelItems []*models.ParcelItem
	for _, item := range r.Items {
		item.setDefaults()
		bItem, _ := json.Marshal(item)
		parcelItem := &models.ParcelItem{
			Metadata: null.StringFrom(string(bItem)).String,
		}
		parcelItems = append(parcelItems, parcelItem)
	}
	return parcelItems
}

func (r BaseRequest) FillVendorOrder(order *models.VendorOrder) {
	order.FromName = null.StringFrom(r.From.Name)
	order.FromAddressLine1 = null.StringFrom(r.From.AddressLine1)
	order.FromAddressLine2 = null.StringFromPtr(r.From.AddressLine2)
	order.FromAddressLine3 = null.StringFromPtr(r.From.AddressLine3)
	order.FromAddressLine4 = null.StringFromPtr(r.From.AddressLine4)
	order.FromCity = null.StringFromPtr(r.From.City)
	order.FromCountryCode = null.StringFrom(r.From.CountryCode)
	order.FromStateProvince = null.StringFromPtr(r.From.StateProvince)
	order.FromPostcode = null.StringFromPtr(r.From.PostCode)
	order.FromContactNumber = null.StringFromPtr(r.From.ContactNumber)
	order.FromContactEmail = null.StringFromPtr(r.From.ContactEmail)

	order.ToName = null.StringFrom(r.To.Name)
	order.ToAddressLine1 = null.StringFrom(r.To.AddressLine1)
	order.ToAddressLine2 = null.StringFromPtr(r.To.AddressLine2)
	order.ToAddressLine3 = null.StringFromPtr(r.To.AddressLine3)
	order.ToAddressLine4 = null.StringFromPtr(r.To.AddressLine4)
	order.ToCity = null.StringFromPtr(r.To.City)
	order.ToCountryCode = null.StringFrom(r.To.CountryCode)
	order.ToStateProvince = null.StringFromPtr(r.To.StateProvince)
	order.ToPostcode = null.StringFromPtr(r.To.PostCode)
	order.ToContactNumber = null.StringFromPtr(r.To.ContactNumber)
	order.ToContactEmail = null.StringFromPtr(r.To.ContactEmail)
	if r.ParcelDetails != nil {
		order.Weight = null.Float64FromPtr(r.ParcelDetails.ShipperSubmittedWeight)
	}
	order.CustomsDetail = null.StringFrom(r.CustomsDetail)
}

func NewB2BBundleBaseRequest(event *orderProto.MpsCreatedEvent) *BaseRequest {
	var batteryPacking uint8
	batteryPackingPtr, _ := models.GetBatteryPacking(event.GetCustomsDeclaration().BatteryPacking)
	if batteryPackingPtr != nil {
		batteryPacking = batteryPackingPtr.Uint8()
	}
	batteryType, _ := models.GetBatteryType(event.GetCustomsDeclaration().BatteryType)
	weight, dimensions := CoreToBaseDimension(event.GetDimension())

	return &BaseRequest{
		Source:              SourceCoreConsumerV2,
		SourceOrderID:       null.StringFrom(event.ShipperOrderRefNo).Ptr(),
		GlobalShipperID:     uint(event.GetShipperDetails().GetShipperId()),
		RequestedTrackingID: event.MpsTrackingNumber,
		ServiceCode:         event.GetInternationalServiceCode(),
		From:                CoreToBaseAddress(event.FromAddress),
		To:                  CoreToBaseAddress(event.ToAddress),
		Pickup:              CoreToBasePickup(event.IsPickupRequired, event.GetPickupInfo()),
		Delivery: CoreToBaseDelivery(
			event.DeliveryInfo,
			event.GetCodAmount().GetValue(),
			event.GetCodAmount().GetCurrency(),
			event.GetInsuranceAmount().GetValue(),
			event.GetInsuranceAmount().GetCurrency(),
		),
		ParcelDetails: &models.ParcelDetails{
			ShipperSubmittedWeight: weight,
			Weight:                 weight,
			BatteryPacking:         batteryPacking,
			BatteryType:            batteryType.Uint8(),
			RequestedTrackingID:    event.GetRequestedTrackingNumber(),
			Value: null.NewFloat64(
				event.GetCustomsDeclaration().GetGoodsValue(),
				event.GetCustomsDeclaration() != nil,
			).Ptr(),
			CustomsCurrency:            utils.NullIfEmpty(event.GetCustomsDeclaration().GetGoodsCurrency()).Ptr(),
			TaxID:                      utils.NullIfEmpty(event.GetCustomsDeclaration().GetTaxId()).Ptr(),
			TradeTerms:                 utils.NullIfEmpty(event.GetCustomsDeclaration().GetTradeTerms()).Ptr(),
			CustomsDescription:         utils.NullIfEmpty(event.GetCustomsDeclaration().GetCustomsDescription()).Ptr(),
			Quantity:                   null.UintFrom(uint(len(event.GetPieceTrackingNumbers()))).Ptr(),
			ShipperSubmittedDimensions: dimensions,
			B2BBundle: &models.B2BBundleDetails{
				DocumentsRequired:         event.GetDocumentsRequired(),
				RequestedPieceTrackingIDs: event.GetRequestedPieceTrackingNumbers(),
				PieceTrackingIDs:          event.GetPieceTrackingNumbers(),
				HSCode:                    utils.NullIfEmpty(event.GetCustomsDeclaration().GetHsCode()).Ptr(),
			},
		},
		RequestId: event.GetRequestInfo().GetRequestId(),
	}
}

func CoreToBaseAddress(addr *common.Address) Address {
	return Address{
		Name:          addr.Name,
		AddressLine1:  addr.Address1,
		AddressLine2:  utils.NullIfEmpty(addr.Address2).Ptr(),
		City:          utils.NullIfEmpty(addr.City).Ptr(),
		StateProvince: utils.NullIfEmpty(addr.State).Ptr(),
		CountryCode:   addr.Country,
		PostCode:      utils.NullIfEmpty(addr.Postcode).Ptr(),
		ContactNumber: utils.NullIfEmpty(addr.Contact).Ptr(),
		ContactEmail:  utils.NullIfEmpty(addr.Email).Ptr(),
	}
}

func CoreToBasePickup(pickupRequired bool, pickupInfo *orderProto.PickupInfo) *Pickup {
	if !pickupRequired || pickupInfo == nil {
		return nil
	}

	pickupAdd := CoreToBaseAddress(pickupInfo.Address)
	vol, ok := parcel.PickupVolumes[pickupInfo.ApproximateVolume]
	if !ok {
		vol = parcel.LessThan3Parcels
	}

	return &Pickup{
		Date:         time.UnixMilli(pickupInfo.StartDate).Format(time.DateOnly),
		Timeslot:     CoreToBaseTimeslot(pickupInfo.Timeslot),
		Address:      &pickupAdd,
		ApproxVolume: null.Uint8From(uint8(vol)).Ptr(),
		Instructions: pickupInfo.PickupInstructions,
	}
}

func CoreToBaseDelivery(
	deliveryInfo *orderProto.DeliveryInfo, cod float64, codCurrency string, insurance float64, insuranceCurrency string,
) *Delivery {
	if deliveryInfo == nil {
		return nil
	}

	return &Delivery{
		StartDate:              time.UnixMilli(deliveryInfo.StartDate).Format(time.DateOnly),
		Timeslot:               CoreToBaseTimeslot(deliveryInfo.Timeslot),
		Instructions:           deliveryInfo.DeliveryInstructions,
		CashOnDelivery:         cod,
		CashOnDeliveryCurrency: codCurrency,
		InsuredValue:           insurance,
		InsuredValueCurrency:   insuranceCurrency,
		AllowSelfCollection:    false,
	}
}

func CoreToBaseTimeslot(timeslot *orderProto.OrderTimeSlot) *Timeslot {
	return &Timeslot{
		StartTime: timeslot.StartTime,
		EndTime:   timeslot.EndTime,
		Timezone:  timeslot.Timezone,
	}
}

func CoreToBaseDimension(coreDim *orderProto.OrderDimension) (weight *float64, dimensions *models.Dimensions) {
	if coreDim == nil {
		return nil, nil
	}
	dimensions = &models.Dimensions{
		Length: &coreDim.Length,
		Width:  &coreDim.Width,
		Height: &coreDim.Height,
	}
	return &coreDim.Weight, dimensions
}
