package order

type LazadaCreateParcelReq struct {
	Receiver   LazadaCCReceiver   `json:"receiver" binding:"required,dive"`
	PreResInfo LazadaCCPreResInfo `json:"preResInfo" binding:"required,dive"`
	Pa<PERSON>el     <PERSON>CCParcel     `json:"parcel" binding:"required,dive"`
	TradeInfo  struct {
		TradeOrderId string `json:"tradeOrderId" binding:"max=512"`
	} `json:"tradeInfo"`
	IdempotentId   string            `json:"idempotentId" binding:"required,max=512"`
	OrderCode      string            `json:"orderCode" binding:"required,max=512"`
	TrackingNumber string            `json:"trackingNumber" binding:"omitempty,max=512"`
	ToLocation     string            `json:"toLocation" binding:"max=512"`
	Features       map[string]string `json:"features" binding:"omitempty"`
}

type LazadaCCReceiver struct {
	ID      string                  `json:"id" binding:"max=512"`
	Name    string                  `json:"name" binding:"required,max=512"`
	Phone   string                  `json:"phone" binding:"required,max=512"`
	Mobile  string                  `json:"mobile" binding:"max=512"`
	Email   *string                 `json:"email" binding:"omitempty,max=512"`
	ZipCode *string                 `json:"zipCode" binding:"omitempty,max=512"`
	Address LazadaCCReceiverAddress `json:"address" binding:"required,dive"`
}

type LazadaCCReceiverAddress struct {
	Country       string  `json:"country" binding:"required,max=512"`
	Province      string  `json:"province" binding:"required,max=512"`
	City          string  `json:"city" binding:"required,max=512"`
	District      *string `json:"district" binding:"omitempty,max=512"`
	Street        *string `json:"street" binding:"omitempty,max=512"`
	DetailAddress string  `json:"detailAddress" binding:"required,max=512"`
}

type LazadaCCPreResInfo struct {
	ResCode string                    `json:"resCode" binding:"max=512"`
	ResName string                    `json:"resName" binding:"max=512"`
	Phone   *string                   `json:"phone" binding:"omitempty,max=512"`
	Mobile  string                    `json:"mobile" binding:"max=512"`
	Address LazadaCCPreResInfoAddress `json:"address" binding:"dive"`
}

type LazadaCCPreResInfoAddress struct {
	Country       string  `json:"country" binding:"max=512"`
	Province      *string `json:"province" binding:"omitempty,max=512"`
	City          *string `json:"city" binding:"omitempty,max=512"`
	District      *string `json:"district" binding:"omitempty,max=512"`
	Street        *string `json:"street" binding:"omitempty,max=512"`
	DetailAddress string  `json:"detailAddress" binding:"max=512"`
}

type LazadaCCParcel struct {
	Weight        *float64         `json:"weight"`
	WeightUnit    string           `json:"weightUnit" binding:"max=512"`
	Price         *float64         `json:"price"`
	PriceUnit     *string          `json:"priceUnit" binding:"omitempty,max=512"`
	Length        *float64         `json:"length"`
	Width         *float64         `json:"width"`
	Height        *float64         `json:"height"`
	DimensionUnit *string          `json:"dimensionUnit" binding:"omitempty,max=512"`
	GoodsQuantity uint             `json:"goodsQuantity" binding:"required"`
	GoodsList     []*LazadaCCGoods `json:"goodsList" binding:"required,dive,min=0"`
}

type LazadaCCGoods struct {
	Name                  string                 `json:"name" binding:"required,max=512"`
	CnName                *string                `json:"cnName" binding:"omitempty,max=512"`
	LocalName             string                 `json:"localName" binding:"max=512"`
	CategoryName          string                 `json:"categoryName" binding:"max=512"`
	LeafCategoryName      string                 `json:"leafCategoryName" binding:"max=512"`
	LeafCategoryLocalName string                 `json:"leafCategoryLocalName" binding:"max=512"`
	CategoryFeature       string                 `json:"categoryFeature" binding:"max=512"`
	Price                 *float64               `json:"price"`
	ItemPrice             float64                `json:"itemPrice"`
	PriceUnit             string                 `json:"priceUnit" binding:"max=512"`
	PriceCurrency         *string                `json:"priceCurrency" binding:"omitempty,max=512"`
	Quantity              int                    `json:"quantity" binding:"required"`
	SkuCode               *string                `json:"skuCode" binding:"omitempty,max=512"`
	BarCode               string                 `json:"barCode" binding:"max=512"`
	HSCode                *string                `json:"hsCode" binding:"omitempty,max=512"`
	DeclaredValue         float64                `json:"declaredValue"`
	Extension             LazadaCCGoodsExtension `json:"extension"`
}

type LazadaCCGoodsExtension struct {
	LvgPaid string `json:"lvgPaid" binding:"max=512"`
}
