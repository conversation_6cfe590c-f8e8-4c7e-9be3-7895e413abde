package order

import (
	"git.ninjavan.co/3pl/configs/orders"
)

type LazadaRequest struct {
	LogisticsInterface string `json:"logistics_interface" form:"logistics_interface" binding:"omitempty"`
	LogisticProviderID string `json:"logistic_provider_id" binding:"omitempty"`
	DataDigest         string `json:"data_digest" form:"data_digest" binding:"omitempty"`
	MsgType            string `json:"msg_type" form:"msg_type" binding:"omitempty"`
	MsgId              string `json:"msg_id" form:"msg_id" binding:"omitempty"`
	PartnerCode        string `json:"partner_code" form:"partner_code" binding:"omitempty"`
	FromCode           string `json:"from_code" form:"from_code" binding:"omitempty"`
	ToCode             string `json:"to_code" binding:"omitempty"`
}

const (
	LazadaErrorCodeInvalidSign    = "S02"
	LazadaErrorCodeInternal       = "B0001"
	LazadaErrorCodeInvalidRequest = "B0002"
)

type LazadaBaseResponse struct {
	Success   bool   `json:"success,omitempty"`
	ErrorCode string `json:"errorCode"`
	Message   string `json:"message"`
}

type LazadaCCOCResponse struct {
	*LazadaBaseResponse
	ResultInfo struct {
		ResultStatus bool `json:"resultStatus"`
	} `json:"resultInfo"`
}

func NewLazadaCCSuccessResp() *LazadaCCOCResponse {
	return &LazadaCCOCResponse{
		LazadaBaseResponse: &LazadaBaseResponse{
			Success: true,
		},
		ResultInfo: struct {
			ResultStatus bool `json:"resultStatus"`
		}{
			ResultStatus: true,
		},
	}
}

type LazadaMMOCResponse struct {
	*LazadaBaseResponse
	Data struct {
		OrderCode string `json:"orderCode"`
	} `json:"data"`
}

func NewLazadaMMOCResponse(orderCode string) *LazadaMMOCResponse {
	return &LazadaMMOCResponse{
		LazadaBaseResponse: &LazadaBaseResponse{
			Success: true,
		},
		Data: struct {
			OrderCode string `json:"orderCode"`
		}{
			OrderCode: orderCode,
		},
	}
}

type LazadaMMBagOCResponse struct {
	*LazadaBaseResponse
	Data struct {
		BigBagID string `json:"bigBagID"`
	} `json:"data"`
}

func NewLazadaMMBagOCResponse(bigBagID string) *LazadaMMBagOCResponse {
	return &LazadaMMBagOCResponse{
		LazadaBaseResponse: &LazadaBaseResponse{
			Success: true,
		},
		Data: struct {
			BigBagID string `json:"bigBagID"`
		}{
			BigBagID: bigBagID,
		},
	}
}

func NewLazadaCCErrorResponse(errorCode, errorMsg string) *LazadaCCOCResponse {
	return &LazadaCCOCResponse{
		LazadaBaseResponse: &LazadaBaseResponse{
			Success:   false,
			ErrorCode: errorCode,
			Message:   errorMsg,
		},
		ResultInfo: struct {
			ResultStatus bool `json:"resultStatus"`
		}{
			ResultStatus: false,
		},
	}
}

type LazadaMmccOCResponse struct {
	LazadaBaseResponse
	InboundWarehouseAddress InboundWarehouseAddress `json:"inboundWarehouseAddress,omitempty"`
	FirstMileOrderCode      string                  `json:"firstMileOrderCode,omitempty"`
}

func NewLazadaErrorResponse(errorCode, errorMsg string) LazadaBaseResponse {
	return LazadaBaseResponse{
		Success:   false,
		ErrorCode: errorCode,
		Message:   errorMsg,
	}
}

type LazadaMmccOCRequest struct {
	Attributes         LazadaOcAttributes     `json:"attributes"`
	CargoType          orders.LazadaCargoType `json:"cargoType" binding:"oneof=0 1"`
	ContainerType      string                 `json:"containerType" binding:"max=16"`
	CountryCode        string                 `json:"countryCode" binding:"required,iso3166_1_alpha2"`
	ExpressNumber      string                 `json:"expressNumber" binding:"required,max=64"`
	FirstMileOrderType string                 `json:"firstMileOrderType" binding:"oneof=FCL LCL"`
	FirstMileOrderCode string                 `json:"firstMileOrderCode" binding:"max=64"`
	WarehouseCode      string                 `json:"warehouseCode" binding:"required,oneof=RMCW-CHOICE-WISE-001 RMCW-CHOICE-WISE-002 RMCW-CHOICE-CN-002 RMCW-CHOICE-CN-003 RMCW-WISE-TEST-01"`
	ToWarehouseCode    string                 `json:"toWarehouseCode" binding:"max=64"`
	IsComplete         string                 `json:"isComplete" binding:"oneof=false true"`
	UniqueCode         string                 `json:"uniqueCode" binding:"required,max=128"`
	Remark             string                 `json:"remark" binding:"max=128"`
	TotalBox           string                 `json:"totalBox" binding:"max=16"`
	TotalVolume        string                 `json:"totalVolume" binding:"omitempty,numeric,max=16"`
	TotalWeight        string                 `json:"totalWeight" binding:"omitempty,numeric,max=16"`
	TransportType      string                 `json:"transportType" binding:"omitempty,oneof=2"`
	Sender             LazadaOcAddress        `json:"sender" binding:"required"`
	Receiver           LazadaOcAddress        `json:"receiver" binding:"required"`
	Details            []LazadaMmccParcel     `json:"details" binding:"required,dive"`
}

type LazadaOcAttributes struct {
	TenantCode  string `json:"tenantCode" binding:"required,max=16"`
	TotalOrders string `json:"totalOrders" binding:"max=64"`
}

type LazadaOcAddress struct {
	Name                string `json:"name" binding:"required,max=64"`
	Phone               string `json:"phone" binding:"required,max=64"`
	Country             string `json:"country" binding:"required,max=512"`
	Province            string `json:"province" binding:"required,max=512"`
	City                string `json:"city" binding:"required,max=512"`
	County              string `json:"county" binding:"max=512"`
	Street              string `json:"street" binding:"max=512"`
	Address             string `json:"address" binding:"required,max=512"`
	ZipCode             string `json:"zipCode" binding:"max=16"`
	RequiresAppointment string `json:"requiresAppointment" binding:"omitempty,oneof=false true"`
}

type LazadaMmccParcel struct {
	AppointOrderCode string `json:"appointOrderCode" binding:"required,max=64"`
	BarCode          string `json:"barCode" binding:"max=64"`
	BizOrderCode     string `json:"bizOrderCode" binding:"required,max=64"`
	BoxCount         string `json:"boxCount" binding:"omitempty,numeric"`
	BoxHeight        string `json:"boxHeight" binding:"max=64"`
	BoxLength        string `json:"boxLength" binding:"max=64"`
	BoxNo            string `json:"boxNo" binding:"max=64"`
	BoxVolume        string `json:"boxVolume" binding:"max=64"`
	BoxWeight        string `json:"boxWeight" binding:"max=64"`
	BoxWidth         string `json:"boxWidth" binding:"max=64"`
	Count            string `json:"count" binding:"required,numeric"`
	Currency         string `json:"currency" binding:"required,max=16"`
	ItemBrand        string `json:"itemBrand" binding:"max=64"`
	ItemID           string `json:"itemId" binding:"required,max=20"`
	ItemLink         string `json:"itemLink" binding:"max=2048"`
	ItemPicture      string `json:"itemPicture" binding:"max=2048"`
	Material         string `json:"material" binding:"required,max=64"`
	Name             string `json:"name" binding:"required,max=256"`
	Price            string `json:"price" binding:"required,numeric"`
	FirstCategory    string `json:"firstCategory" binding:"max=128"`
	SecondCategory   string `json:"secondCategory" binding:"max=128"`
	ThirdCategory    string `json:"thirdCategory" binding:"max=128"`
	LeafCategory     string `json:"leafCategory" binding:"max=128"`
	RealSupplierId   string `json:"realSupplierId" binding:"required,numeric"`
}

type InboundWarehouseAddress struct {
	Country  string `json:"country,omitempty"`
	Province string `json:"province,omitempty"`
	City     string `json:"city,omitempty"`
	District string `json:"district,omitempty"`
	Street   string `json:"street,omitempty"`
	Address  string `json:"address,omitempty"`
	Name     string `json:"name,omitempty"`
	Phone    string `json:"phone,omitempty"`
}

type LazadaUpdateRequest struct {
	FirstMileOrderCode string               `json:"firstMileOrderCode"`
	ExpressNumber      string               `json:"expressNumber" binding:"required,max=64"`
	Details            []LazadaUpdateDetail `json:"details" binding:"required,min=1,dive"`
}

func (l *LazadaUpdateRequest) ToFPL() *FPLLazadaUpdateRequest {
	var details []FPLLazadaUpdateDetail
	for _, detail := range l.Details {
		details = append(details, *detail.ToFPL())
	}
	return &FPLLazadaUpdateRequest{
		SourceOrderID: l.ExpressNumber,
		Details:       details,
	}
}

type FPLLazadaUpdateRequest struct {
	SourceOrderID string
	Details       []FPLLazadaUpdateDetail
}

type LazadaOperateType string

var (
	LazadaOperateDelete  LazadaOperateType = "DELETE_IO"
	LazadaOperateConfirm LazadaOperateType = "CONFIRM_FLO"
)

type LazadaUpdateDetail struct {
	BizOrderCode     string            `json:"bizOrderCode" binding:"required,max=64"`
	AppointOrderCode string            `json:"appointOrderCode"`
	OperateType      LazadaOperateType `json:"operateType" binding:"required,oneof=DELETE_IO CONFIRM_FLO"`
}

func (l *LazadaUpdateDetail) ToFPL() *FPLLazadaUpdateDetail {
	return &FPLLazadaUpdateDetail{
		RefTrackingID: l.BizOrderCode,
		OperateType:   l.OperateType,
	}
}

type FPLLazadaUpdateDetail struct {
	RefTrackingID string
	OperateType   LazadaOperateType
}
